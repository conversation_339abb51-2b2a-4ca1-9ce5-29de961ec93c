# 订单模块使用指南

## 📋 模块概述

订单模块是餐饮管理系统的核心业务模块，基于您提供的表结构设计，实现了完整的订单生命周期管理。支持堂食/外带订单、会员关联、桌台管理、优惠明细等功能。

## 🏗️ 架构设计

### 核心实体

1. **Order (订单主表)**
   - 订单基本信息、状态管理、金额计算
   - 支持会员关联、桌台关联
   - 订单类型：堂食(DINE_IN) / 外带(TAKE_AWAY)

2. **OrderItem (订单商品表)**
   - 订单中的具体商品信息
   - 商品信息快照，防止商品变更影响历史订单

3. **OrderDiscount (订单优惠明细表)**
   - 订单优惠信息详细记录
   - 支持多种优惠类型：优惠券、会员折扣、手动改价、营销活动

### 状态设计

#### 订单状态 (Order.Status)
- `PENDING_PAYMENT`: 待支付
- `PROCESSING`: 制作中
- `COMPLETED`: 已完成
- `CANCELLED`: 已取消

#### 支付状态 (Order.PaymentStatus)
- `UNPAID`: 未支付
- `PAID`: 已支付
- `REFUNDED`: 已退款

#### 订单类型 (Order.OrderType)
- `DINE_IN`: 堂食
- `TAKE_AWAY`: 外带

#### 优惠类型 (OrderDiscount.DiscountType)
- `COUPON`: 优惠券
- `MEMBER`: 会员折扣
- `MANUAL`: 手动改价
- `PROMOTION`: 营销活动

## 🚀 API 接口

### 1. 创建订单
**接口**: `POST /api/v1/orders/create`

**请求体**:
```json
{
    "memberId": 1,
    "tableId": 1,
    "orderType": "DINE_IN",
    "remark": "不要香菜",
    "items": [
        {
            "productId": 1,
            "quantity": 2
        },
        {
            "productId": 2,
            "quantity": 1
        }
    ],
    "discounts": [
        {
            "discountType": "MEMBER",
            "description": "会员9折优惠",
            "amount": 5.80
        },
        {
            "discountType": "COUPON",
            "description": "满50减5优惠券",
            "amount": 5.00,
            "referenceId": 1001
        }
    ]
}
```

**响应**:
```json
{
    "code": 200,
    "msg": "订单创建成功",
    "data": {
        "id": 1,
        "orderNo": "20241223143001123456",
        "orderType": "DINE_IN",
        "status": "PENDING_PAYMENT",
        "paymentStatus": "UNPAID",
        "originalAmount": 80.00,
        "discountAmount": 10.80,
        "payableAmount": 69.20
    }
}
```

### 2. 更新订单状态
**接口**: `POST /api/v1/orders/update-status`

**请求体**:
```json
{
    "orderId": 1,
    "status": "PROCESSING"
}
```

### 3. 更新支付状态
**接口**: `POST /api/v1/orders/update-payment-status`

**请求体**:
```json
{
    "orderId": 1,
    "paymentStatus": "PAID"
}
```

### 4. 查询订单详情
**接口**: `GET /api/v1/orders/{id}`

**响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "id": 1,
        "orderNo": "20241223143001123456",
        "memberId": 1,
        "tableId": 1,
        "orderType": "DINE_IN",
        "status": "PROCESSING",
        "paymentStatus": "PAID",
        "originalAmount": 80.00,
        "discountAmount": 10.80,
        "payableAmount": 69.20,
        "items": [
            {
                "id": 1,
                "productId": 1,
                "productName": "美式咖啡",
                "quantity": 2,
                "unitPrice": 25.00,
                "totalPrice": 50.00
            }
        ],
        "discounts": [
            {
                "id": 1,
                "discountType": "MEMBER",
                "description": "会员9折优惠",
                "amount": 5.80
            }
        ]
    }
}
```

### 5. 分页查询订单
**接口**: `POST /api/v1/orders/query`

**请求体**:
```json
{
    "current": 1,
    "size": 10,
    "status": "PROCESSING",
    "orderType": "DINE_IN",
    "startTime": "2024-12-23 00:00:00",
    "endTime": "2024-12-23 23:59:59"
}
```

### 6. 根据条件查询订单
- `GET /api/v1/orders/member/{memberId}` - 查询会员订单
- `GET /api/v1/orders/table/{tableId}` - 查询桌台订单  
- `GET /api/v1/orders/status/{status}` - 查询指定状态订单
- `GET /api/v1/orders/order-no/{orderNo}` - 根据订单号查询

### 7. 删除订单
**接口**: `DELETE /api/v1/orders/{id}`

**说明**: 只能删除待支付或已取消的订单

## 💡 业务流程

### 典型下单流程

1. **创建订单**
   ```
   POST /api/v1/orders/create
   状态: PENDING_PAYMENT (待支付)
   支付状态: UNPAID (未支付)
   ```

2. **支付订单**
   ```
   POST /api/v1/orders/update-payment-status
   支付状态: UNPAID → PAID (已支付)
   订单状态: 自动变更为 PROCESSING (制作中)
   ```

3. **完成订单**
   ```
   POST /api/v1/orders/update-status
   状态: PROCESSING → COMPLETED (已完成)
   ```

### 取消订单流程

```
POST /api/v1/orders/update-status
状态: 任意状态 → CANCELLED (已取消)
```

## 🔧 技术特性

### 1. 数据一致性
- 使用事务确保订单、订单项、优惠明细的数据一致性
- 自动计算订单金额：原始金额 - 优惠金额 = 应付金额

### 2. 冗余设计
- 订单项中冗余存储商品信息，防止商品变更影响历史订单
- 优惠明细独立存储，支持多种优惠类型组合

### 3. 金额计算
- 原始金额：所有订单项总价之和
- 优惠金额：所有优惠明细金额之和
- 应付金额：原始金额 - 优惠金额

### 4. 状态管理
- 支付成功自动将订单状态改为制作中
- 完整的状态流转控制
- 状态变更时自动更新时间戳

### 5. 分页查询
- 支持多条件组合查询
- 时间范围查询支持
- 高效的索引设计

## 📊 数据库设计

### 主要表结构

#### orders (订单主表)
```sql
CREATE TABLE orders (
    "id" BIGSERIAL PRIMARY KEY,
    "order_no" VARCHAR(30) NOT NULL UNIQUE,
    "store_id" BIGINT NOT NULL,
    "member_id" BIGINT,
    "table_id" BIGINT,
    "order_type" VARCHAR(20) NOT NULL DEFAULT 'DINE_IN',
    "status" VARCHAR(20) NOT NULL DEFAULT 'PENDING_PAYMENT',
    "payment_status" VARCHAR(20) NOT NULL DEFAULT 'UNPAID',
    "original_amount" DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    "discount_amount" DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    "payable_amount" DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    "remark" VARCHAR(255),
    "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT NOW()
);
```

#### order_items (订单商品表)
```sql
CREATE TABLE order_items (
    "id" BIGSERIAL PRIMARY KEY,
    "order_id" BIGINT NOT NULL,
    "product_id" BIGINT NOT NULL,
    "product_name" VARCHAR(100) NOT NULL,
    "quantity" INT NOT NULL DEFAULT 1,
    "unit_price" DECIMAL(10, 2) NOT NULL,
    "total_price" DECIMAL(10, 2) NOT NULL
);
```

#### order_discounts (订单优惠明细表)
```sql
CREATE TABLE order_discounts (
    "id" BIGSERIAL PRIMARY KEY,
    "order_id" BIGINT NOT NULL,
    "discount_type" VARCHAR(30) NOT NULL,
    "description" VARCHAR(100) NOT NULL,
    "amount" DECIMAL(10, 2) NOT NULL,
    "reference_id" BIGINT
);
```

### 关键索引
- `idx_orders_order_no`: 订单号索引
- `idx_orders_member_id`: 会员ID索引
- `idx_orders_status`: 状态索引
- `idx_orders_store_created`: 门店+创建时间复合索引
- `idx_order_items_order_id`: 订单ID索引
- `idx_order_discounts_order_id`: 订单ID索引

## 🛠️ 开发指南

### 1. 添加新的订单状态

1. 在 `Order.Status` 枚举中添加新状态
2. 更新状态转换验证逻辑
3. 更新前端状态显示

### 2. 扩展优惠类型

1. 在 `OrderDiscount.DiscountType` 枚举中添加新类型
2. 更新优惠计算逻辑
3. 添加相应的验证规则

### 3. 自定义查询条件

1. 在 `OrderQueryRequest` 中添加新字段
2. 更新 `OrderMapper.xml` 中的查询条件
3. 测试查询性能并优化索引

## 🔍 常见问题

### Q: 如何处理订单取消后的库存回滚？
A: 在订单状态更新为 CANCELLED 时，需要调用库存服务回滚相应商品的库存数量。

### Q: 优惠金额如何计算？
A: 优惠金额是所有 order_discounts 表中该订单的 amount 字段之和。

### Q: 如何实现订单号的唯一性？
A: 使用时间戳 + 6位随机数的方式生成，格式为 `yyyyMMddHHmmss + 6位随机数`。

### Q: 订单删除是物理删除还是逻辑删除？
A: 目前是物理删除，只允许删除待支付或已取消的订单。如需逻辑删除，可以添加 deleted 字段。

## 📈 性能优化建议

1. **查询优化**
   - 使用复合索引优化常用查询组合
   - 避免全表扫描，合理使用分页

2. **缓存策略**
   - 对热点订单数据进行Redis缓存
   - 缓存商品信息减少关联查询

3. **批量操作**
   - 使用批量插入减少数据库交互
   - 合理控制批量操作的数据量

4. **分库分表**
   - 按门店ID进行分库
   - 按时间进行分表（如按月分表）
