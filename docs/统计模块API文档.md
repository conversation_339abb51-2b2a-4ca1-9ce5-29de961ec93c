# 统计模块API文档

## 概述

统计模块提供今日业务关键指标的统计数据，包括销售额、订单数量、新增会员数及其与昨日的对比增长百分比。

## API接口

### 1. 获取今日统计数据

**接口地址：** `GET /api/v1/statistics/today`

**接口描述：** 获取今日业务统计数据，包含销售总额、订单数量、新增会员数及其相比昨日的增长百分比

**请求参数：** 无

**响应格式：**

```json
{
  "code": 200,
  "msg": "今日统计数据",
  "data": {
    "todaySalesAmount": 1500.00,
    "salesGrowthPercentage": 25.50,
    "todayOrderCount": 12,
    "orderGrowthPercentage": 20.00,
    "todayNewMemberCount": 8,
    "memberGrowthPercentage": 60.00
  },
  "timestamp": 1640234567890
}
```

**响应字段说明：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| todaySalesAmount | BigDecimal | 今日订单销售总额（只统计已支付订单） |
| salesGrowthPercentage | BigDecimal | 销售额相比昨日增长百分比 |
| todayOrderCount | Long | 今日订单数量（只统计已支付订单） |
| orderGrowthPercentage | BigDecimal | 订单数量相比昨日增长百分比 |
| todayNewMemberCount | Long | 今日新增会员数 |
| memberGrowthPercentage | BigDecimal | 新增会员数相比昨日增长百分比 |

**增长百分比计算规则：**

1. **正常情况：** `增长百分比 = (今日数据 - 昨日数据) / 昨日数据 * 100`
2. **昨日数据为0：** 如果今日有数据则显示100%增长，否则显示0%
3. **数据为负：** 显示负增长百分比

**示例请求：**

```bash
curl -X GET "http://localhost:8081/api/v1/statistics/today" \
     -H "Content-Type: application/json"
```

**示例响应：**

```json
{
  "code": 200,
  "msg": "今日统计数据",
  "data": {
    "todaySalesAmount": 2350.50,
    "salesGrowthPercentage": 15.75,
    "todayOrderCount": 18,
    "orderGrowthPercentage": 12.50,
    "todayNewMemberCount": 5,
    "memberGrowthPercentage": -16.67
  },
  "timestamp": 1640234567890
}
```

### 2. 获取最近5笔订单详情

**接口地址：** `GET /api/v1/statistics/recent-orders`

**接口描述：** 获取最近5笔订单的详细信息，包含订单号、各商品名称数量及订单总金额

**请求参数：** 无

**响应格式：**

```json
{
  "code": 200,
  "msg": "最近5笔订单",
  "data": [
    {
      "orderNo": "20241223143001123456",
      "totalAmount": 158.50,
      "items": [
        {
          "productName": "招牌牛肉面",
          "quantity": 2
        },
        {
          "productName": "小笼包",
          "quantity": 1
        }
      ]
    },
    {
      "orderNo": "20241223142001123455",
      "totalAmount": 89.00,
      "items": [
        {
          "productName": "红烧肉饭",
          "quantity": 1
        },
        {
          "productName": "酸辣汤",
          "quantity": 2
        }
      ]
    }
  ],
  "timestamp": 1640234567890
}
```

**响应字段说明：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| orderNo | String | 订单号 |
| totalAmount | BigDecimal | 订单总金额（应付金额） |
| items | Array | 订单商品项列表 |
| items[].productName | String | 商品名称 |
| items[].quantity | Integer | 商品数量 |

**示例请求：**

```bash
curl -X GET "http://localhost:8081/api/v1/statistics/recent-orders" \
     -H "Content-Type: application/json"
```

## 数据统计说明

### 销售总额统计
- **统计范围：** 当日00:00:00 至 23:59:59
- **统计条件：** 只统计支付状态为"已支付"的订单
- **统计字段：** 订单表的`payable_amount`字段（应付金额）

### 订单数量统计
- **统计范围：** 当日00:00:00 至 23:59:59
- **统计条件：** 只统计支付状态为"已支付"的订单
- **统计方式：** 按订单记录数量统计

### 新增会员统计
- **统计范围：** 当日00:00:00 至 23:59:59
- **统计条件：** 按会员创建时间统计
- **统计方式：** 按会员记录数量统计

### 最近订单统计
- **统计范围：** 按订单创建时间倒序排列
- **统计数量：** 最近5笔订单
- **统计条件：** 所有状态的订单（包括待支付、已支付等）
- **商品信息：** 显示订单中各商品的名称和数量

## 错误处理

当接口调用失败时，返回格式如下：

```json
{
  "code": 500,
  "msg": "获取统计数据失败: 具体错误信息",
  "data": null,
  "timestamp": 1640234567890
}
```

## 注意事项

1. **时区处理：** 所有时间统计基于服务器本地时区
2. **数据精度：** 金额保留2位小数，百分比保留2位小数
3. **性能考虑：** 统计查询会扫描当日和昨日的数据，建议在数据库相关字段上建立索引
4. **门店隔离：** 统计数据会自动按当前用户所属门店进行隔离

## 数据库索引建议

为了提高统计查询性能，建议在以下字段上建立索引：

```sql
-- 订单表索引
CREATE INDEX idx_orders_created_at_payment_status ON orders(created_at, payment_status);
CREATE INDEX idx_orders_store_id_created_at ON orders(store_id, created_at);

-- 会员表索引  
CREATE INDEX idx_members_created_at ON users(created_at);
CREATE INDEX idx_members_store_id_created_at ON users(store_id, created_at);
```
