# 认证系统使用指南

## 概述

本项目实现了基于JWT的认证系统，包括登录、登出、令牌刷新和访问控制功能。

## 系统架构

### 核心组件

1. **AuthController** - 认证控制器，处理登录、登出、令牌刷新
2. **AuthService** - 认证服务，处理认证业务逻辑
3. **JwtUtil** - JWT工具类，负责令牌生成和验证
4. **JwtAuthenticationFilter** - JWT认证过滤器，拦截请求验证令牌
5. **UserContext** - 用户上下文工具，获取当前用户信息

### 令牌存储

- **访问令牌(Access Token)**: 存储在Redis中，默认24小时过期
- **刷新令牌(Refresh Token)**: 存储在Redis中，默认7天过期

## API接口

### 1. 用户登录

**接口**: `POST /api/v1/auth/login`

**请求体**:
```json
{
    "username": "admin",
    "password": "123456"
}
```

**响应**:
```json
{
    "code": 200,
    "msg": "登录成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzUxMiJ9...",
        "refreshToken": "eyJhbGciOiJIUzUxMiJ9...",
        "tokenType": "Bearer",
        "expiresIn": 86400,
        "staffId": 1,
        "username": "admin",
        "fullName": "管理员",
        "storeId": 1,
        "roleId": 1
    },
    "timestamp": 1642752000000
}
```

### 2. 刷新令牌

**接口**: `POST /api/v1/auth/refresh`

**请求体**:
```json
{
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9..."
}
```

**响应**: 与登录响应相同格式

### 3. 用户登出

**接口**: `POST /api/v1/auth/logout`

**请求头**: `Authorization: Bearer {accessToken}`

**响应**:
```json
{
    "code": 200,
    "msg": "登出成功",
    "data": null,
    "timestamp": 1642752000000
}
```

### 4. 获取当前用户信息

**接口**: `GET /api/v1/auth/me`

**请求头**: `Authorization: Bearer {accessToken}`

**响应**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "staffId": 1,
        "username": "admin",
        "storeId": 1,
        "roleId": 1
    },
    "timestamp": 1642752000000
}
```

## 前端集成

### 1. 登录流程

```javascript
// 登录
const login = async (username, password) => {
    const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
    });
    
    const result = await response.json();
    if (result.code === 200) {
        // 存储令牌
        localStorage.setItem('accessToken', result.data.accessToken);
        localStorage.setItem('refreshToken', result.data.refreshToken);
        return result.data;
    }
    throw new Error(result.msg);
};
```

### 2. 请求拦截器

```javascript
// 添加请求拦截器，自动添加Authorization头
const apiRequest = async (url, options = {}) => {
    const token = localStorage.getItem('accessToken');
    
    const headers = {
        'Content-Type': 'application/json',
        ...options.headers
    };
    
    if (token) {
        headers.Authorization = `Bearer ${token}`;
    }
    
    const response = await fetch(url, {
        ...options,
        headers
    });
    
    // 如果令牌过期，尝试刷新
    if (response.status === 401) {
        const refreshed = await refreshToken();
        if (refreshed) {
            // 重新发送请求
            headers.Authorization = `Bearer ${localStorage.getItem('accessToken')}`;
            return fetch(url, { ...options, headers });
        } else {
            // 跳转到登录页
            window.location.href = '/login';
        }
    }
    
    return response;
};
```

### 3. 令牌刷新

```javascript
// 刷新令牌
const refreshToken = async () => {
    const refreshToken = localStorage.getItem('refreshToken');
    if (!refreshToken) return false;
    
    try {
        const response = await fetch('/api/v1/auth/refresh', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ refreshToken })
        });
        
        const result = await response.json();
        if (result.code === 200) {
            localStorage.setItem('accessToken', result.data.accessToken);
            return true;
        }
    } catch (error) {
        console.error('令牌刷新失败:', error);
    }
    
    // 刷新失败，清除本地令牌
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    return false;
};
```

## 后端使用

### 1. 获取当前用户信息

```java
@RestController
public class ExampleController {

    @GetMapping("/example")
    public ApiResponse<String> example() {
        // 获取当前用户信息
        Long staffId = UserContext.getCurrentStaffId();
        String username = UserContext.getCurrentUsername();
        Long storeId = UserContext.getCurrentStoreId();

        return ApiResponse.success("当前用户: " + username);
    }
}
```

### 2. 异步场景下的用户上下文

由于使用了`TransmittableThreadLocal`，用户上下文会自动传递到异步线程中：

```java
@Service
public class ExampleService {

    @Async
    public CompletableFuture<String> asyncMethod() {
        // 在异步线程中依然可以获取用户上下文
        String username = UserContext.getCurrentUsername();
        Long storeId = UserContext.getCurrentStoreId();

        log.info("异步任务执行，当前用户: {}, 门店: {}", username, storeId);

        return CompletableFuture.completedFuture("异步任务完成");
    }
}
```

### 3. 手动设置用户上下文（测试场景）

```java
@Test
public void testWithUserContext() {
    // 设置测试用户上下文
    UserContextHelper.setTestUserContext(1L, "testuser", 1L, 1L);

    try {
        // 执行需要用户上下文的业务逻辑
        String result = someService.doSomething();
        assertNotNull(result);
    } finally {
        // 清理用户上下文
        UserContext.clear();
    }
}
```

### 4. 带用户上下文执行操作

```java
// 临时切换用户上下文执行操作
UserContext.UserInfo adminUser = UserContextHelper.createUserInfo(
    1L, "admin", 1L, 1L, "admin-token"
);

String result = UserContextHelper.executeWithUserContext(adminUser, () -> {
    // 在这个代码块中，用户上下文是adminUser
    return someService.adminOperation();
});
// 执行完成后自动恢复原来的用户上下文
```

### 5. 自动填充门店ID

由于集成了`UserContext`，MyBatis Plus会自动为继承`BaseEntity`的实体填充`storeId`字段。

### 6. 用户上下文工具方法

```java
// 检查是否有有效的用户上下文
boolean hasContext = UserContextHelper.hasValidUserContext();

// 获取当前用户简要信息
String userInfo = UserContextHelper.getCurrentUserInfo();

// 验证当前用户是否属于指定门店
boolean belongsToStore = UserContextHelper.isCurrentUserBelongToStore(storeId);

// 验证当前用户是否具有指定角色
boolean hasRole = UserContextHelper.isCurrentUserHasRole(roleId);
```

## 配置说明

### application.yml配置

```yaml
jwt:
  secret: by-store-jwt-secret-key-2024-very-long-secret-key-for-security
  expiration: 24  # 访问令牌过期时间（小时）
  refresh-expiration: 7  # 刷新令牌过期时间（天）
```

### 免认证路径

在`JwtAuthenticationFilter`中配置的免认证路径：
- `/api/v1/auth/login` - 登录接口
- `/api/v1/auth/refresh` - 刷新令牌接口
- `/health` - 健康检查
- `/actuator` - 监控端点
- `/swagger` - API文档
- `/v3/api-docs` - OpenAPI文档

## 安全特性

1. **JWT签名验证** - 使用HS512算法签名
2. **令牌过期检查** - 自动检查令牌是否过期
3. **Redis存储验证** - 验证令牌是否在Redis中存在
4. **自动登出** - 登出时清除Redis中的令牌
5. **密码加密** - 使用BCrypt加密存储密码

## 错误处理

系统会返回以下错误码：
- `401` - 未授权（令牌无效、过期或缺失）
- `400` - 参数错误
- `500` - 服务器内部错误

所有错误都会被全局异常处理器统一处理并返回标准格式。
