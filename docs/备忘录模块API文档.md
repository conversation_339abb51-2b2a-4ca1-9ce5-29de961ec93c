# 备忘录模块API文档

## 概述

备忘录模块提供门店备忘录的管理功能，包括创建、查询、状态修改等操作。支持分页查询和批量状态更新。

## 数据库表结构

```sql
CREATE TABLE store_memos (
    "id" BIGSERIAL PRIMARY KEY,
    "store_id" BIGINT NOT NULL,
    "content" TEXT NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'TODO', -- TODO(待办), DONE(已完成)
    "creator_id" BIGINT NOT NULL, -- 关联到创建该备忘录的员工ID
    "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "completed_at" TIMESTAMP -- 完成时间，待办状态时为 NULL
);
```

## API接口

### 1. 创建备忘录

**接口地址：** `POST /api/v1/memos/create`

**接口描述：** 创建新的备忘录

**请求参数：**

```json
{
  "content": "需要采购新的咖啡豆",
  "status": "TODO"
}
```

**请求字段说明：**

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| content | String | 是 | 备忘录内容，最大1000字符 |
| status | String | 否 | 状态，默认为TODO |

**响应格式：**

```json
{
  "code": 200,
  "msg": "备忘录创建成功",
  "data": {
    "id": 1,
    "storeId": 1,
    "content": "需要采购新的咖啡豆",
    "status": "TODO",
    "statusDescription": "待办",
    "creatorId": 1,
    "createdAt": "2024-01-01T10:00:00",
    "updatedAt": "2024-01-01T10:00:00",
    "completedAt": null
  },
  "timestamp": 1640234567890
}
```

### 2. 分页查询备忘录

**接口地址：** `POST /api/v1/memos/query`

**接口描述：** 分页查询备忘录列表

**请求参数：**

```json
{
  "current": 1,
  "size": 10,
  "storeId": 1,
  "status": "TODO",
  "creatorId": 1,
  "content": "采购"
}
```

**请求字段说明：**

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| storeId | Long | 否 | 门店ID，不传则使用当前用户门店 |
| status | String | 否 | 状态筛选 |
| creatorId | Long | 否 | 创建人ID筛选 |
| content | String | 否 | 内容关键词模糊查询 |

**响应格式：**

```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 25,
    "pages": 3,
    "records": [
      {
        "id": 1,
        "storeId": 1,
        "content": "需要采购新的咖啡豆",
        "status": "TODO",
        "statusDescription": "待办",
        "creatorId": 1,
        "createdAt": "2024-01-01T10:00:00",
        "updatedAt": "2024-01-01T10:00:00",
        "completedAt": null
      }
    ]
  },
  "timestamp": 1640234567890
}
```

### 3. 更新备忘录状态

**接口地址：** `POST /api/v1/memos/update-status`

**接口描述：** 更新备忘录状态，支持单个或批量更新

**请求参数：**

```json
{
  "ids": [1, 2, 3],
  "status": "DONE"
}
```

**请求字段说明：**

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | Array | 是 | 备忘录ID列表 |
| status | String | 是 | 新状态：TODO或DONE |

**响应格式：**

```json
{
  "code": 200,
  "msg": "状态更新成功",
  "data": null,
  "timestamp": 1640234567890
}
```

### 4. 根据ID查询备忘录

**接口地址：** `GET /api/v1/memos/{id}`

**接口描述：** 根据ID查询单个备忘录详情

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 备忘录ID |

**响应格式：**

```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "storeId": 1,
    "content": "需要采购新的咖啡豆",
    "status": "TODO",
    "statusDescription": "待办",
    "creatorId": 1,
    "createdAt": "2024-01-01T10:00:00",
    "updatedAt": "2024-01-01T10:00:00",
    "completedAt": null
  },
  "timestamp": 1640234567890
}
```

### 5. 删除备忘录

**接口地址：** `DELETE /api/v1/memos/{id}`

**接口描述：** 删除指定的备忘录

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 备忘录ID |

**响应格式：**

```json
{
  "code": 200,
  "msg": "删除成功",
  "data": null,
  "timestamp": 1640234567890
}
```

### 6. 获取备忘录统计信息

**接口地址：** `GET /api/v1/memos/statistics`

**接口描述：** 获取备忘录统计信息

**查询参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| storeId | Long | 否 | 门店ID，不传则使用当前用户门店 |

**响应格式：**

```json
{
  "code": 200,
  "msg": "统计信息",
  "data": {
    "totalCount": 25,
    "todoCount": 15,
    "doneCount": 10
  },
  "timestamp": 1640234567890
}
```

### 7. 根据状态查询备忘录

**接口地址：** `GET /api/v1/memos/by-status/{status}`

**接口描述：** 根据状态查询备忘录列表

**路径参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | String | 是 | 状态：TODO或DONE |

**响应格式：**

```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "storeId": 1,
      "content": "需要采购新的咖啡豆",
      "status": "TODO",
      "statusDescription": "待办",
      "creatorId": 1,
      "createdAt": "2024-01-01T10:00:00",
      "updatedAt": "2024-01-01T10:00:00",
      "completedAt": null
    }
  ],
  "timestamp": 1640234567890
}
```

## 状态说明

备忘录支持以下状态：

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| TODO | 待办 | 新创建的备忘录默认状态 |
| DONE | 已完成 | 备忘录已完成，会自动记录完成时间 |

## 权限说明

1. **门店隔离**：用户只能操作自己所属门店的备忘录
2. **创建权限**：所有登录用户都可以创建备忘录
3. **修改权限**：只能修改自己创建的备忘录或同门店的备忘录
4. **查询权限**：只能查询自己所属门店的备忘录

## 错误处理

当接口调用失败时，返回格式如下：

```json
{
  "code": 500,
  "msg": "具体错误信息",
  "data": null,
  "timestamp": 1640234567890
}
```

常见错误码：

- `400`：请求参数错误
- `401`：未授权访问
- `403`：权限不足
- `404`：资源不存在
- `500`：服务器内部错误

## 示例请求

### 创建备忘录

```bash
curl -X POST "http://localhost:8081/api/v1/memos/create" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-jwt-token" \
     -d '{
       "content": "检查库存并补充原材料",
       "status": "TODO"
     }'
```

### 分页查询

```bash
curl -X POST "http://localhost:8081/api/v1/memos/query" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-jwt-token" \
     -d '{
       "current": 1,
       "size": 10,
       "status": "TODO"
     }'
```

### 更新状态

```bash
curl -X POST "http://localhost:8081/api/v1/memos/update-status" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer your-jwt-token" \
     -d '{
       "ids": [1, 2],
       "status": "DONE"
     }'
```
