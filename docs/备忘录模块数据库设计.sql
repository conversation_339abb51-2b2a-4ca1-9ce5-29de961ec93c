-- ==================== 备忘录模块数据库设计 ====================

-- 删除已存在的表
DROP TABLE IF EXISTS store_memos;

-- 创建门店备忘录表
CREATE TABLE store_memos (
    "id" BIGSERIAL PRIMARY KEY,
    "store_id" BIGINT NOT NULL,
    "content" TEXT NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'TODO', -- TODO(待办), DONE(已完成)
    "creator_id" BIGINT NOT NULL, -- 关联到创建该备忘录的员工ID
    "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "completed_at" TIMESTAMP -- 完成时间，待办状态时为 NULL
);

-- 添加注释
COMMENT ON TABLE store_memos IS '门店备忘录表 (单表简化版)';
COMMENT ON COLUMN store_memos.id IS '备忘录ID';
COMMENT ON COLUMN store_memos.store_id IS '所属门店ID';
COMMENT ON COLUMN store_memos.content IS '备忘录的具体内容';
COMMENT ON COLUMN store_memos.status IS '当前状态: TODO(待办), DONE(已完成)';
COMMENT ON COLUMN store_memos.creator_id IS '创建人ID (关联员工表)';
COMMENT ON COLUMN store_memos.created_at IS '创建时间';
COMMENT ON COLUMN store_memos.updated_at IS '最后更新时间 (任何修改都会更新)';
COMMENT ON COLUMN store_memos.completed_at IS '完成时间 (仅在状态变为DONE时记录)';

-- 创建索引
CREATE INDEX idx_memos_store_id_status ON store_memos(store_id, status);
CREATE INDEX idx_memos_creator_id ON store_memos(creator_id);
CREATE INDEX idx_memos_created_at ON store_memos(created_at);
CREATE INDEX idx_memos_store_id_created_at ON store_memos(store_id, created_at);

-- ==================== 示例数据 ====================

-- 插入示例备忘录数据
INSERT INTO store_memos (
    store_id, content, status, creator_id, created_at, updated_at
) VALUES 
(
    1, '检查咖啡机运行状态，确保正常工作', 'TODO', 1, 
    NOW() - INTERVAL '2 hours', NOW() - INTERVAL '2 hours'
),
(
    1, '整理收银台区域，清理多余物品', 'DONE', 1, 
    NOW() - INTERVAL '4 hours', NOW() - INTERVAL '1 hour'
),
(
    1, '补充纸巾、餐具和调料包', 'TODO', 2, 
    NOW() - INTERVAL '1 hour', NOW() - INTERVAL '1 hour'
),
(
    1, '检查库存，列出需要采购的原材料清单', 'TODO', 1, 
    NOW() - INTERVAL '30 minutes', NOW() - INTERVAL '30 minutes'
),
(
    1, '清洁店面玻璃和地面', 'DONE', 2, 
    NOW() - INTERVAL '6 hours', NOW() - INTERVAL '3 hours'
);

-- 更新已完成备忘录的完成时间
UPDATE store_memos 
SET completed_at = updated_at 
WHERE status = 'DONE';

-- ==================== 查询示例 ====================

-- 查询所有备忘录
SELECT * FROM store_memos ORDER BY created_at DESC;

-- 查询待办备忘录
SELECT * FROM store_memos WHERE status = 'TODO' ORDER BY created_at DESC;

-- 查询已完成备忘录
SELECT * FROM store_memos WHERE status = 'DONE' ORDER BY completed_at DESC;

-- 查询指定门店的备忘录
SELECT * FROM store_memos WHERE store_id = 1 ORDER BY created_at DESC;

-- 查询指定创建人的备忘录
SELECT * FROM store_memos WHERE creator_id = 1 ORDER BY created_at DESC;

-- 统计备忘录数量
SELECT 
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'TODO' THEN 1 END) as todo_count,
    COUNT(CASE WHEN status = 'DONE' THEN 1 END) as done_count
FROM store_memos 
WHERE store_id = 1;

-- 查询最近7天的备忘录
SELECT * FROM store_memos 
WHERE created_at >= NOW() - INTERVAL '7 days'
ORDER BY created_at DESC;

-- 查询包含关键词的备忘录
SELECT * FROM store_memos 
WHERE content ILIKE '%咖啡%' 
ORDER BY created_at DESC;

-- ==================== 性能优化建议 ====================

-- 如果备忘录数据量很大，可以考虑以下优化：

-- 1. 分区表（按门店ID分区）
-- CREATE TABLE store_memos_partitioned (
--     LIKE store_memos INCLUDING ALL
-- ) PARTITION BY HASH (store_id);

-- 2. 添加更多索引（根据实际查询需求）
-- CREATE INDEX idx_memos_content_gin ON store_memos USING gin(to_tsvector('english', content));

-- 3. 定期清理历史数据（可选）
-- DELETE FROM store_memos 
-- WHERE status = 'DONE' 
-- AND completed_at < NOW() - INTERVAL '1 year';

-- ==================== 权限设置 ====================

-- 如果需要为应用程序创建专用数据库用户
-- CREATE USER memo_app_user WITH PASSWORD 'secure_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON store_memos TO memo_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE store_memos_id_seq TO memo_app_user;
