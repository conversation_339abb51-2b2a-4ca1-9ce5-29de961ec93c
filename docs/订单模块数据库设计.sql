-- 订单模块数据库设计
-- 基于您提供的表结构设计

-- ==================== 订单主表 ====================
DROP TABLE IF EXISTS orders;
CREATE TABLE orders (
    "id" BIGSERIAL PRIMARY KEY,
    "order_no" VARCHAR(30) NOT NULL UNIQUE,
    "store_id" BIGINT NOT NULL,
    "member_id" BIGINT, -- 可为空，非会员顾客
    "table_id" BIGINT, -- 可为空，外带订单
    "order_type" VARCHAR(20) NOT NULL DEFAULT 'DINE_IN', -- DINE_IN (堂食), TAKE_AWAY (外带)
    "status" VARCHAR(20) NOT NULL DEFAULT 'PENDING_PAYMENT', -- PENDING_PAYMENT(待支付), PROCESSING(制作中), COMPLETED(已完成), CANCELLED(已取消)
    "payment_status" VARCHAR(20) NOT NULL DEFAULT 'UNPAID', -- UNPAID(未支付), PAID(已支付), REFUNDED(已退款)
    "original_amount" DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    "discount_amount" DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    "payable_amount" DECIMAL(10, 2) NOT NULL DEFAULT 0.00,
    "remark" VARCHAR(255),
    "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT NOW()
);

COMMENT ON TABLE orders IS '订单主表';
COMMENT ON COLUMN orders.order_no IS '订单号，对客展示，唯一';
COMMENT ON COLUMN orders.store_id IS '所属门店ID';
COMMENT ON COLUMN orders.member_id IS '关联的会员ID (外键, 可为空)';
COMMENT ON COLUMN orders.table_id IS '关联的桌号ID (外键, 可为空)';
COMMENT ON COLUMN orders.order_type IS '订单类型：DINE_IN (堂食), TAKE_AWAY (外带)';
COMMENT ON COLUMN orders.status IS '订单状态：PENDING_PAYMENT, PROCESSING, COMPLETED, CANCELLED';
COMMENT ON COLUMN orders.payment_status IS '支付状态：UNPAID, PAID, REFUNDED';
COMMENT ON COLUMN orders.original_amount IS '原始商品总金额 (所有order_items的总价)';
COMMENT ON COLUMN orders.discount_amount IS '优惠总金额 (所有order_discounts的总和)';
COMMENT ON COLUMN orders.payable_amount IS '应付金额 (original_amount - discount_amount)';
COMMENT ON COLUMN orders.remark IS '订单备注';

CREATE INDEX idx_orders_member_id ON orders(member_id);
CREATE INDEX idx_orders_table_id ON orders(table_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_orders_store_id ON orders(store_id);
CREATE INDEX idx_orders_order_no ON orders(order_no);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_orders_store_status ON orders(store_id, status);
CREATE INDEX idx_orders_store_created ON orders(store_id, created_at);

-- ==================== 订单商品表 ====================
DROP TABLE IF EXISTS order_items;
CREATE TABLE order_items (
    "id" BIGSERIAL PRIMARY KEY,
    "order_id" BIGINT NOT NULL,
    "product_id" BIGINT NOT NULL,
    "product_name" VARCHAR(100) NOT NULL,
    "quantity" INT NOT NULL DEFAULT 1 CHECK (quantity > 0),
    "unit_price" DECIMAL(10, 2) NOT NULL,
    "total_price" DECIMAL(10, 2) NOT NULL
);

COMMENT ON TABLE order_items IS '订单商品表';
COMMENT ON COLUMN order_items.order_id IS '所属订单ID';
COMMENT ON COLUMN order_items.product_id IS '关联的商品ID';
COMMENT ON COLUMN order_items.product_name IS '商品名称快照 (防止商品信息变更)';
COMMENT ON COLUMN order_items.quantity IS '商品数量';
COMMENT ON COLUMN order_items.unit_price IS '商品单价快照 (下单时的价格)';
COMMENT ON COLUMN order_items.total_price IS '该项商品总价 (quantity * unit_price)';

CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- 外键约束
ALTER TABLE order_items ADD CONSTRAINT fk_order_items_order_id 
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE;

-- ==================== 订单优惠明细表 ====================
DROP TABLE IF EXISTS order_discounts;
CREATE TABLE order_discounts (
    "id" BIGSERIAL PRIMARY KEY,
    "order_id" BIGINT NOT NULL,
    "discount_type" VARCHAR(30) NOT NULL, -- COUPON(优惠券), MEMBER(会员折扣), MANUAL(手动改价), PROMOTION(营销活动)
    "description" VARCHAR(100) NOT NULL,
    "amount" DECIMAL(10, 2) NOT NULL,
    "reference_id" BIGINT -- 可选的关联ID，如优惠券ID
);

COMMENT ON TABLE order_discounts IS '订单优惠明细表';
COMMENT ON COLUMN order_discounts.order_id IS '所属订单ID';
COMMENT ON COLUMN order_discounts.discount_type IS '优惠类型：COUPON, MEMBER, MANUAL, PROMOTION';
COMMENT ON COLUMN order_discounts.description IS '优惠描述，如"满20减5优惠券"或"店长手动减免"';
COMMENT ON COLUMN order_discounts.amount IS '该项优惠的金额';
COMMENT ON COLUMN order_discounts.reference_id IS '可选的关联ID，如优惠券ID、活动ID';

CREATE INDEX idx_order_discounts_order_id ON order_discounts(order_id);
CREATE INDEX idx_order_discounts_discount_type ON order_discounts(discount_type);
CREATE INDEX idx_order_discounts_reference_id ON order_discounts(reference_id);

-- 外键约束
ALTER TABLE order_discounts ADD CONSTRAINT fk_order_discounts_order_id 
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE;

-- ==================== 示例数据 ====================

-- 插入示例订单
INSERT INTO orders (
    order_no, store_id, member_id, table_id, order_type, status, payment_status,
    original_amount, discount_amount, payable_amount, remark
) VALUES 
(
    '20241223143001123456', 1, 1, 1, 'DINE_IN', 'PROCESSING', 'PAID',
    58.00, 8.00, 50.00, '不要香菜'
),
(
    '20241223143002234567', 1, NULL, NULL, 'TAKE_AWAY', 'PENDING_PAYMENT', 'UNPAID',
    35.00, 0.00, 35.00, NULL
);

-- 插入示例订单商品项
INSERT INTO order_items (
    order_id, product_id, product_name, quantity, unit_price, total_price
) VALUES 
(1, 1, '美式咖啡', 2, 25.00, 50.00),
(1, 2, '拿铁咖啡', 1, 30.00, 30.00),
(2, 1, '美式咖啡', 1, 25.00, 25.00),
(2, 3, '卡布奇诺', 1, 28.00, 28.00);

-- 插入示例订单优惠明细
INSERT INTO order_discounts (
    order_id, discount_type, description, amount, reference_id
) VALUES 
(1, 'MEMBER', '会员9折优惠', 5.80, NULL),
(1, 'COUPON', '满50减5优惠券', 5.00, 1001);

-- ==================== 查询示例 ====================

-- 查询订单详情（包含商品项和优惠明细）
SELECT 
    o.*,
    oi.id as item_id,
    oi.product_name,
    oi.quantity,
    oi.unit_price,
    oi.total_price,
    od.id as discount_id,
    od.discount_type,
    od.description as discount_description,
    od.amount as discount_amount
FROM orders o
LEFT JOIN order_items oi ON o.id = oi.order_id
LEFT JOIN order_discounts od ON o.id = od.order_id
WHERE o.order_no = '20241223143001123456';

-- 查询指定状态的订单
SELECT * FROM orders WHERE status = 'PROCESSING' ORDER BY created_at ASC;

-- 查询会员的订单历史
SELECT * FROM orders WHERE member_id = 1 ORDER BY created_at DESC;

-- 统计今日订单数量和金额
SELECT 
    COUNT(*) as order_count,
    SUM(payable_amount) as total_amount
FROM orders 
WHERE DATE(created_at) = CURRENT_DATE 
AND status IN ('PROCESSING', 'COMPLETED');

-- 商品销售排行榜
SELECT 
    oi.product_name,
    SUM(oi.quantity) as total_quantity,
    SUM(oi.total_price) as total_amount
FROM order_items oi
INNER JOIN orders o ON oi.order_id = o.id
WHERE o.status IN ('PROCESSING', 'COMPLETED')
AND DATE(o.created_at) = CURRENT_DATE
GROUP BY oi.product_id, oi.product_name
ORDER BY total_quantity DESC
LIMIT 10;

-- 优惠使用统计
SELECT 
    od.discount_type,
    COUNT(*) as usage_count,
    SUM(od.amount) as total_discount_amount
FROM order_discounts od
INNER JOIN orders o ON od.order_id = o.id
WHERE DATE(o.created_at) = CURRENT_DATE
GROUP BY od.discount_type
ORDER BY total_discount_amount DESC;
