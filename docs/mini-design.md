# 小程序前端接口设计文档

## 概述

本文档定义了小程序前端所需的所有API接口，包括用户管理、门店查询、商品浏览、订单管理、余额充值、积分管理等功能。

接口分为两类：
- **Public接口**: 无需用户登录即可访问的公开接口
- **Private接口**: 需要用户登录认证才能访问的私有接口

## 基础信息

- **基础URL**: `https://api.yourstore.com`
- **API版本**: `v1`
- **认证方式**: JWT Token (Private接口必需)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00"
}
```

# Public接口 (无需认证)

## 1. 用户认证模块

### 1.1 微信登录 - 获取OpenID

**接口类型**: Public

**接口地址**: `POST /api/v1/mini/auth/wx-login`

**请求参数**:
```json
{
  "code": "微信小程序登录凭证code"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "openid": "oABC123456789",
    "sessionKey": "session_key_value",
    "unionid": "uABC123456789",
    "token": "jwt_token_here"
  }
}
```

## 2. 门店模块

### 2.1 查询所有门店

**接口类型**: Public

**接口地址**: `GET /api/v1/mini/stores`

**响应示例**:
```json
{
  "success": true,
**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "星巴克万达店",
      "address": "北京市朝阳区万达广场3楼",
      "phoneNumber": "010-12345678",
      "x": "116.397128",
      "y": "39.916527",
      "isActive": true,
      "distance": null
    }
  ]
}
```

### 2.2 计算门店距离

**接口类型**: Public

**接口地址**: `POST /api/v1/mini/stores/distance`

**请求参数**:
```json
{
  "userX": "116.397000",
  "userY": "39.916000",
  "storeIds": [1, 2, 3]
}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "storeId": 1,
      "storeName": "星巴克万达店",
      "distance": 0.5,
      "unit": "km"
    }
  ]
}
```

## 3. 商品模块

### 3.1 查询门店商品分类

**接口类型**: Public

**接口地址**: `GET /api/v1/mini/stores/{storeId}/categories`

**路径参数**:
- `storeId`: 门店ID

**查询参数**:
- `parentId`: 父分类ID，0或不传表示查询顶级分类

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "咖啡",
      "parentId": 0,
      "sortOrder": 1,
      "hasChildren": true
    },
    {
      "id": 2,
      "name": "茶饮",
      "parentId": 0,
      "sortOrder": 2,
      "hasChildren": false
    }
  ]
}
```

### 3.2 通过分类查询商品

**接口类型**: Public

**接口地址**: `GET /api/v1/mini/stores/{storeId}/categories/{categoryId}/products`

**路径参数**:
- `storeId`: 门店ID
- `categoryId`: 分类ID

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认20
- `status`: 商品状态，默认PUBLISHED

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 20,
    "total": 50,
    "pages": 3,
    "records": [
      {
        "id": 1,
        "name": "美式咖啡",
        "description": "经典美式咖啡，香醇浓郁",
        "imageUrl": "https://example.com/coffee.jpg",
        "price": 28.00,
        "stockQuantity": 100,
        "productCode": "COFFEE001",
        "status": "PUBLISHED",
        "categoryName": "咖啡"
      }
    ]
  }
}
```

### 3.3 商品搜索

**接口类型**: Public

**接口地址**: `GET /api/v1/mini/stores/{storeId}/products/search`

**查询参数**:
- `keyword`: 搜索关键词
- `page`: 页码，默认1
- `size`: 每页数量，默认20

**响应示例**: 同3.2

## 4. 门店桌台模块

### 4.1 查询门店桌台

**接口类型**: Public

**接口地址**: `GET /api/v1/mini/stores/{storeId}/tables`

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "tableNumber": "A01",
      "capacity": 4,
      "status": "AVAILABLE",
      "qrCode": "https://example.com/qr/table1.png"
    },
    {
      "id": 2,
      "tableNumber": "A02",
      "capacity": 2,
      "status": "OCCUPIED",
      "qrCode": "https://example.com/qr/table2.png"
    }
  ]
}
```

### 4.2 扫码获取桌台信息

**接口类型**: Public

**接口地址**: `GET /api/v1/mini/tables/qr/{qrCode}`

**路径参数**:
- `qrCode`: 二维码标识

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tableId": 1,
    "tableNumber": "A01",
    "storeId": 1,
    "storeName": "星巴克万达店",
    "capacity": 4,
    "status": "AVAILABLE"
  }
}
```

## 5. 会员等级模块

### 5.1 查询会员等级列表

**接口类型**: Public

**接口地址**: `GET /api/v1/mini/membership/levels`

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "levelName": "普通会员",
      "levelTag": "normal",
      "upgradePointsThreshold": 0,
      "description": "享受基础会员权益",
      "iconUrl": "https://example.com/normal.png",
      "isActive": true
    },
    {
      "id": 2,
      "levelName": "白银会员",
      "levelTag": "silver",
      "upgradePointsThreshold": 1000,
      "description": "享受9.5折优惠",
      "iconUrl": "https://example.com/silver.png",
      "isActive": true
    }
  ]
}
```

## 6. 商品评价模块

### 6.1 查询商品评价

**接口类型**: Public

**接口地址**: `GET /api/v1/mini/products/{productId}/reviews`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10
- `rating`: 评分筛选 (1-5)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 25,
    "averageRating": 4.5,
    "ratingDistribution": {
      "5": 15,
      "4": 8,
      "3": 2,
      "2": 0,
      "1": 0
    },
    "records": [
      {
        "id": 1,
        "memberNickname": "用户***",
        "memberAvatar": "https://example.com/avatar.jpg",
        "rating": 5,
        "content": "咖啡很香，服务很好！",
        "images": [
          "https://example.com/review1.jpg"
        ],
        "createdAt": "2024-01-15T12:00:00"
      }
    ]
  }
}
```

## 7. 工具接口

### 7.1 距离计算

**接口类型**: Public

**接口地址**: `POST /api/v1/mini/utils/calculate-distance`

**请求参数**:
```json
{
  "lat1": 39.916527,
  "lng1": 116.397128,
  "lat2": 39.916000,
  "lng2": 116.397000
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "distance": 0.5,
    "unit": "km"
  }
}
```

---

# Private接口 (需要认证)

## 8. 用户管理模块

### 8.1 查询用户信息

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/user/profile`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "memberId": 1001,
    "wxOpenid": "oABC123456789",
    "nickname": "用户昵称",
    "avatarUrl": "https://example.com/avatar.jpg",
    "phoneNumber": "13800138000",
    "membershipLevelName": "黄金会员",
    "balance": 299.50,
    "points": 1580,
    "createdAt": "2024-01-01T10:00:00"
  }
}
```

### 8.2 更新用户信息

**接口类型**: Private

**接口地址**: `PUT /api/v1/mini/user/profile`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "nickname": "新昵称",
  "avatarUrl": "https://example.com/new-avatar.jpg",
  "phoneNumber": "13900139000"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "用户信息更新成功"
}
```

### 8.3 查询用户升级进度

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/membership/upgrade-progress`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "currentLevel": {
      "id": 1,
      "levelName": "普通会员",
      "levelTag": "normal"
    },
    "nextLevel": {
      "id": 2,
      "levelName": "白银会员",
      "levelTag": "silver",
      "upgradePointsThreshold": 1000
    },
    "currentPoints": 580,
    "pointsNeeded": 420,
    "progressPercentage": 58.0
  }
}
```

## 9. 订单模块

### 9.1 创建订单

**接口类型**: Private

**接口地址**: `POST /api/v1/mini/orders`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "storeId": 1,
  "orderType": "DINE_IN",
  "tableNumber": "A01",
  "items": [
    {
      "productId": 1,
      "quantity": 2,
      "unitPrice": 28.00
    }
  ],
  "paymentMethod": "WECHAT_PAY",
  "useBalance": true,
  "balanceAmount": 10.00,
  "remark": "少糖"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "订单创建成功",
  "data": {
    "id": 1001,
    "orderNo": "ORD20240115001",
    "totalAmount": 56.00,
    "actualAmount": 46.00,
    "status": "PENDING",
    "paymentStatus": "PENDING"
  }
}
```

### 9.2 查询用户订单

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/orders`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `status`: 订单状态，可选
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 25,
    "records": [
      {
        "id": 1001,
        "orderNo": "ORD20240115001",
        "storeId": 1,
        "storeName": "星巴克万达店",
        "totalAmount": 56.00,
        "actualAmount": 46.00,
        "status": "COMPLETED",
        "paymentStatus": "PAID",
        "orderType": "DINE_IN",
        "tableNumber": "A01",
        "createdAt": "2024-01-15T10:30:00",
        "items": [
          {
            "productName": "美式咖啡",
            "quantity": 2,
            "unitPrice": 28.00,
            "totalPrice": 56.00
          }
        ]
      }
    ]
  }
}
```

### 9.3 查询订单详情

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/orders/{orderId}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**: 同9.2中的单个订单格式

## 10. 余额管理模块

### 10.1 余额充值

**接口类型**: Private

**接口地址**: `POST /api/v1/mini/balance/recharge`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "amount": 100.00,
  "paymentMethod": "WECHAT_PAY"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "充值订单创建成功",
  "data": {
    "rechargeOrderNumber": "RCH20240115001",
    "amount": 100.00,
    "paymentInfo": {
      "prepayId": "wx_prepay_id",
      "nonceStr": "random_string",
      "timeStamp": "1642234567",
      "signType": "RSA",
      "paySign": "signature"
    }
  }
}
```

### 10.2 查询充值记录

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/balance/recharge-logs`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10
- `status`: 充值状态，可选

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 15,
    "records": [
      {
        "id": 1,
        "rechargeOrderNumber": "RCH20240115001",
        "amount": 100.00,
        "balanceBefore": 50.00,
        "balanceAfter": 150.00,
        "paymentMethod": "WECHAT_PAY",
        "status": "SUCCESSFUL",
        "createdAt": "2024-01-15T10:30:00"
      }
    ]
  }
}
```

### 10.3 查询消费记录

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/balance/consumption-logs`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 8,
    "records": [
      {
        "id": 1,
        "orderId": 1001,
        "orderNo": "ORD20240115001",
        "amountConsumed": 10.00,
        "balanceBefore": 150.00,
        "balanceAfter": 140.00,
        "createdAt": "2024-01-15T11:00:00"
      }
    ]
  }
}
```

## 11. 积分管理模块

### 11.1 查询积分记录

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/points/logs`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10
- `changeType`: 变动类型，可选

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 20,
    "records": [
      {
        "id": 1,
        "changeType": "PURCHASE_EARN",
        "pointsChange": 56,
        "pointsBefore": 1524,
        "pointsAfter": 1580,
        "orderId": 1001,
        "description": "购买商品获得积分",
        "createdAt": "2024-01-15T11:00:00"
      }
    ]
  }
}
```

### 11.2 积分兑换

**接口类型**: Private

**接口地址**: `POST /api/v1/mini/points/exchange`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "exchangeType": "BALANCE",
  "pointsAmount": 1000,
  "exchangeValue": 10.00
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "积分兑换成功",
  "data": {
    "pointsUsed": 1000,
    "exchangeValue": 10.00,
    "pointsAfter": 580,
    "balanceAfter": 150.00
  }
}
```

## 12. 优惠券模块

### 12.1 查询用户优惠券

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/coupons`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `status`: 优惠券状态 (UNUSED/USED/EXPIRED)
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 5,
    "records": [
      {
        "id": 1,
        "couponCode": "DISCOUNT10",
        "couponName": "新用户专享券",
        "discountType": "PERCENTAGE",
        "discountValue": 10.00,
        "minOrderAmount": 50.00,
        "maxDiscountAmount": 20.00,
        "validFrom": "2024-01-01T00:00:00",
        "validTo": "2024-12-31T23:59:59",
        "status": "UNUSED",
        "description": "满50元享9折优惠，最高减20元"
      }
    ]
  }
}
```

### 12.2 领取优惠券

**接口类型**: Private

**接口地址**: `POST /api/v1/mini/coupons/claim`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "couponTemplateId": 1
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "优惠券领取成功",
  "data": {
    "couponId": 1,
    "couponCode": "DISCOUNT10",
    "couponName": "新用户专享券"
  }
}
```

## 13. 消息通知模块

### 13.1 查询用户消息

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/notifications`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `type`: 消息类型 (ORDER/PROMOTION/SYSTEM)
- `isRead`: 是否已读 (true/false)
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 8,
    "unreadCount": 3,
    "records": [
      {
        "id": 1,
        "type": "ORDER",
        "title": "订单状态更新",
        "content": "您的订单 ORD20240115001 已制作完成，请及时取餐",
        "isRead": false,
        "relatedId": 1001,
        "createdAt": "2024-01-15T11:30:00"
      }
    ]
  }
}
```

### 13.2 标记消息已读

**接口类型**: Private

**接口地址**: `PUT /api/v1/mini/notifications/{notificationId}/read`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "消息已标记为已读"
}
```

## 14. 收藏模块

### 14.1 收藏商品

**接口类型**: Private

**接口地址**: `POST /api/v1/mini/favorites/products`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "productId": 1
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "收藏成功"
}
```

### 14.2 查询收藏的商品

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/favorites/products`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 5,
    "records": [
      {
        "id": 1,
        "productId": 1,
        "productName": "美式咖啡",
        "productImage": "https://example.com/coffee.jpg",
        "price": 28.00,
        "storeId": 1,
        "storeName": "星巴克万达店",
        "createdAt": "2024-01-15T10:00:00"
      }
    ]
  }
}
```

### 14.3 取消收藏

**接口类型**: Private

**接口地址**: `DELETE /api/v1/mini/favorites/products/{productId}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "取消收藏成功"
}
```

## 15. 评价模块

### 15.1 提交订单评价

**接口类型**: Private

**接口地址**: `POST /api/v1/mini/reviews`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "orderId": 1001,
  "rating": 5,
  "content": "咖啡很香，服务很好！",
  "images": [
    "https://example.com/review1.jpg",
    "https://example.com/review2.jpg"
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "评价提交成功",
  "data": {
    "reviewId": 1,
    "pointsEarned": 10
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 状态枚举

### 订单状态
- `PENDING`: 待处理
- `CONFIRMED`: 已确认
- `PREPARING`: 制作中
- `READY`: 待取餐
- `COMPLETED`: 已完成
- `CANCELLED`: 已取消

### 支付状态
- `PENDING`: 待支付
- `PAID`: 已支付
- `REFUNDED`: 已退款
- `FAILED`: 支付失败

### 商品状态
- `PUBLISHED`: 已上架
- `ARCHIVED`: 已下架
- `SOLDOUT`: 售罄

### 充值状态
- `PENDING`: 处理中
- `SUCCESSFUL`: 成功
- `FAILED`: 失败

### 积分变动类型
- `PURCHASE_EARN`: 购买获得
- `SIGN_IN`: 签到获得
- `EXCHANGE_USE`: 兑换使用
- `EXPIRE_DEDUCT`: 过期扣除
- `ADMIN_ADJUST`: 管理员调整

## 8. 会员等级模块

### 8.1 查询会员等级列表

**接口地址**: `GET /api/v1/mini/membership/levels`

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "levelName": "普通会员",
      "levelTag": "normal",
      "upgradePointsThreshold": 0,
      "description": "享受基础会员权益",
      "iconUrl": "https://example.com/normal.png",
      "isActive": true
    },
    {
      "id": 2,
      "levelName": "白银会员",
      "levelTag": "silver",
      "upgradePointsThreshold": 1000,
      "description": "享受9.5折优惠",
      "iconUrl": "https://example.com/silver.png",
      "isActive": true
    }
  ]
}
```

### 8.2 查询用户升级进度

**接口地址**: `GET /api/v1/mini/membership/upgrade-progress`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "data": {
    "currentLevel": {
      "id": 1,
      "levelName": "普通会员",
      "levelTag": "normal"
    },
    "nextLevel": {
      "id": 2,
      "levelName": "白银会员",
      "levelTag": "silver",
      "upgradePointsThreshold": 1000
    },
    "currentPoints": 580,
    "pointsNeeded": 420,
    "progressPercentage": 58.0
  }
}
```

## 9. 门店桌台模块

### 9.1 查询门店桌台

**接口地址**: `GET /api/v1/mini/stores/{storeId}/tables`

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "tableNumber": "A01",
      "capacity": 4,
      "status": "AVAILABLE",
      "qrCode": "https://example.com/qr/table1.png"
    },
    {
      "id": 2,
      "tableNumber": "A02",
      "capacity": 2,
      "status": "OCCUPIED",
      "qrCode": "https://example.com/qr/table2.png"
    }
  ]
}
```

### 9.2 扫码获取桌台信息

**接口地址**: `GET /api/v1/mini/tables/qr/{qrCode}`

**路径参数**:
- `qrCode`: 二维码标识

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tableId": 1,
    "tableNumber": "A01",
    "storeId": 1,
    "storeName": "星巴克万达店",
    "capacity": 4,
    "status": "AVAILABLE"
  }
}
```

## 10. 优惠券模块

### 10.1 查询用户优惠券

**接口地址**: `GET /api/v1/mini/coupons`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `status`: 优惠券状态 (UNUSED/USED/EXPIRED)
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 5,
    "records": [
      {
        "id": 1,
        "couponCode": "DISCOUNT10",
        "couponName": "新用户专享券",
        "discountType": "PERCENTAGE",
        "discountValue": 10.00,
        "minOrderAmount": 50.00,
        "maxDiscountAmount": 20.00,
        "validFrom": "2024-01-01T00:00:00",
        "validTo": "2024-12-31T23:59:59",
        "status": "UNUSED",
        "description": "满50元享9折优惠，最高减20元"
      }
    ]
  }
}
```

### 10.2 领取优惠券

**接口地址**: `POST /api/v1/mini/coupons/claim`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "couponTemplateId": 1
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "优惠券领取成功",
  "data": {
    "couponId": 1,
    "couponCode": "DISCOUNT10",
    "couponName": "新用户专享券"
  }
}
```

## 11. 消息通知模块

### 11.1 查询用户消息

**接口地址**: `GET /api/v1/mini/notifications`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `type`: 消息类型 (ORDER/PROMOTION/SYSTEM)
- `isRead`: 是否已读 (true/false)
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 8,
    "unreadCount": 3,
    "records": [
      {
        "id": 1,
        "type": "ORDER",
        "title": "订单状态更新",
        "content": "您的订单 ORD20240115001 已制作完成，请及时取餐",
        "isRead": false,
        "relatedId": 1001,
        "createdAt": "2024-01-15T11:30:00"
      }
    ]
  }
}
```

### 11.2 标记消息已读

**接口地址**: `PUT /api/v1/mini/notifications/{notificationId}/read`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "消息已标记为已读"
}
```

## 12. 收藏模块

### 12.1 收藏商品

**接口地址**: `POST /api/v1/mini/favorites/products`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "productId": 1
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "收藏成功"
}
```

### 14.2 查询收藏的商品

**接口类型**: Private

**接口地址**: `GET /api/v1/mini/favorites/products`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 5,
    "records": [
      {
        "id": 1,
        "productId": 1,
        "productName": "美式咖啡",
        "productImage": "https://example.com/coffee.jpg",
        "price": 28.00,
        "storeId": 1,
        "storeName": "星巴克万达店",
        "createdAt": "2024-01-15T10:00:00"
      }
    ]
  }
}
```

### 14.3 取消收藏

**接口类型**: Private

**接口地址**: `DELETE /api/v1/mini/favorites/products/{productId}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "取消收藏成功"
}
```

---

# 接口分类总结

## Public接口 (7个模块，15个接口)

### 基础功能
1. **用户认证** - 微信登录
2. **门店查询** - 门店列表、距离计算
3. **商品浏览** - 分类查询、商品列表、商品搜索
4. **桌台管理** - 桌台查询、扫码获取桌台信息
5. **会员等级** - 等级列表查询
6. **商品评价** - 评价列表查询
7. **工具接口** - 距离计算

## Private接口 (8个模块，20个接口)

### 用户相关
1. **用户管理** - 用户信息查询/更新、升级进度
2. **订单管理** - 创建订单、查询订单、订单详情
3. **余额管理** - 充值、充值记录、消费记录
4. **积分管理** - 积分记录、积分兑换
5. **优惠券** - 查询优惠券、领取优惠券
6. **消息通知** - 查询消息、标记已读
7. **商品收藏** - 收藏/取消收藏、收藏列表
8. **订单评价** - 提交评价

**响应示例**:
```json
{
  "success": true,
  "message": "收藏成功"
}
```

### 12.2 查询收藏的商品

**接口地址**: `GET /api/v1/mini/favorites/products`

**请求头**: `Authorization: Bearer {token}`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 5,
    "records": [
      {
        "id": 1,
        "productId": 1,
        "productName": "美式咖啡",
        "productImage": "https://example.com/coffee.jpg",
        "price": 28.00,
        "storeId": 1,
        "storeName": "星巴克万达店",
        "createdAt": "2024-01-15T10:00:00"
      }
    ]
  }
}
```

### 12.3 取消收藏

**接口地址**: `DELETE /api/v1/mini/favorites/products/{productId}`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "success": true,
  "message": "取消收藏成功"
}
```

## 13. 评价模块

### 13.1 提交订单评价

**接口地址**: `POST /api/v1/mini/reviews`

**请求头**: `Authorization: Bearer {token}`

**请求参数**:
```json
{
  "orderId": 1001,
  "rating": 5,
  "content": "咖啡很香，服务很好！",
  "images": [
    "https://example.com/review1.jpg",
    "https://example.com/review2.jpg"
  ]
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "评价提交成功",
  "data": {
    "reviewId": 1,
    "pointsEarned": 10
  }
}
```

### 13.2 查询商品评价

**接口地址**: `GET /api/v1/mini/products/{productId}/reviews`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页数量，默认10
- `rating`: 评分筛选 (1-5)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 10,
    "total": 25,
    "averageRating": 4.5,
    "ratingDistribution": {
      "5": 15,
      "4": 8,
      "3": 2,
      "2": 0,
      "1": 0
    },
    "records": [
      {
        "id": 1,
        "memberNickname": "用户***",
        "memberAvatar": "https://example.com/avatar.jpg",
        "rating": 5,
        "content": "咖啡很香，服务很好！",
        "images": [
          "https://example.com/review1.jpg"
        ],
        "createdAt": "2024-01-15T12:00:00"
      }
    ]
  }
}
```

# 接口实现优先级

## 第一期（核心功能）- Public接口优先

### Public接口 (必须优先实现)
1. **微信登录** - 用户认证基础
2. **门店查询** - 门店列表、距离计算
3. **商品模块** - 分类查询、商品列表、商品搜索
4. **桌台查询** - 扫码点餐基础功能

### Private接口 (核心业务)
1. **用户信息管理** - 查询/更新用户信息
2. **订单管理** - 创建订单、查询订单
3. **余额充值** - 充值功能和记录查询

## 第二期（增值功能）

### Private接口 (会员体系)
1. **积分管理** - 积分记录、积分兑换
2. **会员等级** - 升级进度查询
3. **优惠券系统** - 查询和领取优惠券

### Public接口 (完善体验)
1. **商品评价查询** - 提升商品展示
2. **会员等级列表** - 展示会员权益

## 第三期（完善功能）

### Private接口 (用户体验)
1. **消息通知** - 订单状态通知
2. **商品收藏** - 个性化功能
3. **订单评价** - 用户反馈

### 工具接口
1. **距离计算** - 独立工具服务

# 技术实现注意事项

## 安全性要求

### Public接口
- **无需认证**: 但需要做好接口防刷和频率限制
- **数据脱敏**: 返回的数据不包含敏感信息
- **参数验证**: 严格验证输入参数，防止SQL注入等攻击

### Private接口
- **JWT认证**: 所有接口都必须验证JWT Token
- **权限控制**: 用户只能访问自己的数据
- **敏感操作**: 余额、积分变动需要额外验证

## 性能优化

### 缓存策略
- **门店信息**: Redis缓存，1小时过期
- **商品分类**: Redis缓存，30分钟过期
- **商品列表**: Redis缓存，10分钟过期
- **用户信息**: 本地缓存，5分钟过期

### 分页查询
- **默认分页**: 所有列表接口都支持分页
- **最大限制**: 单页最多返回50条记录
- **索引优化**: 确保分页查询字段有索引

## 数据一致性

### 事务处理
- **订单创建**: 库存扣减、订单创建、积分增加需要在同一事务中
- **余额充值**: 充值记录、余额更新需要保证一致性
- **积分兑换**: 积分扣减、余额增加需要原子操作

### 幂等性
- **支付回调**: 支持重复调用，结果一致
- **订单创建**: 防止重复提交
- **优惠券领取**: 防止重复领取

## 错误处理

### 统一错误格式
```json
{
  "success": false,
  "errorCode": "BUSINESS_ERROR",
  "message": "用户友好的错误信息",
  "details": "技术详细错误信息(仅开发环境)",
  "timestamp": "2024-01-15T10:30:00"
}
```

### 常见错误场景
- **参数验证失败**: 返回具体的参数错误信息
- **业务逻辑错误**: 返回用户可理解的错误提示
- **系统异常**: 记录日志，返回通用错误信息

## 接口版本管理

### URL版本控制
- **当前版本**: `/api/v1/mini/`
- **向后兼容**: 保持旧版本接口可用
- **废弃通知**: 提前通知接口废弃计划

### 字段变更策略
- **新增字段**: 直接添加，不影响现有功能
- **修改字段**: 保持兼容性，必要时新增字段
- **删除字段**: 标记为废弃，一个版本后删除

## 监控和日志

### 接口监控
- **响应时间**: 监控接口响应时间，超过500ms告警
- **错误率**: 监控接口错误率，超过5%告警
- **调用量**: 监控接口调用量，识别异常流量

### 日志记录
- **请求日志**: 记录所有接口请求和响应
- **业务日志**: 记录关键业务操作
- **错误日志**: 记录所有异常和错误信息
