# 小程序公开接口使用指南

## 概述

MiniPublicController 实现了小程序前端所需的所有公开接口（无需认证），使用了规范的请求和响应实体类：

1. **用户认证** - 微信登录获取OpenID
2. **门店模块** - 查询门店、计算距离
3. **商品模块** - 查询分类、商品列表、商品搜索
4. **门店桌台** - 查询桌台、扫码获取桌台信息
5. **会员等级** - 查询等级列表
6. **工具接口** - 距离计算

## 实体类说明

### 请求实体类 (Request)
- `MiniWxLoginRequest` - 微信登录请求
- `MiniDistanceCalculateRequest` - 门店距离计算请求
- `MiniCoordinateDistanceRequest` - 坐标距离计算请求

### 响应实体类 (Response)
- `MiniWxLoginResponse` - 微信登录响应
- `MiniStoreResponse` - 门店信息响应
- `MiniStoreDistanceResponse` - 门店距离响应
- `MiniCategoryResponse` - 商品分类响应
- `MiniProductResponse` - 商品信息响应
- `MiniTableResponse` - 桌台信息响应
- `MiniTableDetailResponse` - 桌台详情响应
- `MiniMembershipLevelResponse` - 会员等级响应
- `MiniDistanceResponse` - 距离计算响应

## 接口列表

### 1. 用户认证模块

#### 微信登录
```
POST /api/v1/mini/public/auth/wx-login
Content-Type: application/json

请求体: MiniWxLoginRequest
{
  "code": "微信小程序登录凭证code"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "openid": "mock_openid_1642234567890",
    "sessionKey": "mock_session_key",
    "unionid": "mock_unionid",
    "token": "mock_jwt_token"
  }
}
```

**响应类型：** `ApiResponse<MiniWxLoginResponse>`

### 2. 门店模块

#### 查询所有激活门店
```
GET /api/v1/mini/public/stores
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "星巴克万达店",
      "address": "北京市朝阳区万达广场3楼",
      "phoneNumber": "010-12345678",
      "x": "116.397128",
      "y": "39.916527",
      "isActive": true,
      "distance": null
    }
  ]
}
```

**响应类型：** `ApiResponse<List<MiniStoreResponse>>`

#### 计算门店距离
```
POST /api/v1/mini/public/stores/distance
Content-Type: application/json

请求体: MiniDistanceCalculateRequest
{
  "userX": "116.397000",
  "userY": "39.916000",
  "storeIds": [1, 2, 3]
}
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "storeId": 1,
      "storeName": "星巴克万达店",
      "distance": 0.5,
      "unit": "km"
    }
  ]
}
```

**响应类型：** `ApiResponse<List<MiniStoreDistanceResponse>>`

### 3. 商品模块

#### 查询门店商品分类
```
GET /api/v1/mini/public/stores/{storeId}/categories?parentId=0
```

**参数说明：**
- `storeId`: 门店ID
- `parentId`: 父分类ID，0或不传表示查询顶级分类

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "咖啡",
      "parentId": 0,
      "sortOrder": 1,
      "hasChildren": true
    },
    {
      "id": 2,
      "name": "茶饮",
      "parentId": 0,
      "sortOrder": 2,
      "hasChildren": false
    }
  ]
}
```

#### 通过分类查询商品
```
GET /api/v1/mini/public/stores/{storeId}/categories/{categoryId}/products?page=1&size=20&status=PUBLISHED
```

**参数说明：**
- `storeId`: 门店ID
- `categoryId`: 分类ID
- `page`: 页码，默认1
- `size`: 每页数量，默认20，最大50
- `status`: 商品状态，默认PUBLISHED

**响应示例：**
```json
{
  "success": true,
  "data": {
    "current": 1,
    "size": 20,
    "total": 50,
    "pages": 3,
    "records": [
      {
        "id": 1,
        "name": "美式咖啡",
        "description": "经典美式咖啡，香醇浓郁",
        "imageUrl": "https://example.com/coffee.jpg",
        "price": 28.00,
        "stockQuantity": 100,
        "productCode": "COFFEE001",
        "status": "PUBLISHED",
        "categoryName": "咖啡"
      }
    ]
  }
}
```

#### 商品搜索
```
GET /api/v1/mini/public/stores/{storeId}/products/search?keyword=咖啡&page=1&size=20
```

**参数说明：**
- `storeId`: 门店ID
- `keyword`: 搜索关键词（必需）
- `page`: 页码，默认1
- `size`: 每页数量，默认20，最大50

**响应格式：** 同分类查询商品

### 4. 门店桌台模块

#### 查询门店桌台
```
GET /api/v1/mini/public/stores/{storeId}/tables
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "tableNumber": "A01",
      "capacity": 4,
      "status": "AVAILABLE",
      "qrCode": "https://example.com/qr/table1.png"
    },
    {
      "id": 2,
      "tableNumber": "A02",
      "capacity": 4,
      "status": "UNAVAILABLE",
      "qrCode": "https://example.com/qr/table2.png"
    }
  ]
}
```

#### 扫码获取桌台信息
```
GET /api/v1/mini/public/tables/qr/{qrCode}
```

**参数说明：**
- `qrCode`: 二维码标识

**响应示例：**
```json
{
  "success": true,
  "data": {
    "tableId": 1,
    "tableNumber": "A01",
    "storeId": 1,
    "storeName": "星巴克万达店",
    "capacity": 4,
    "status": "AVAILABLE"
  }
}
```

### 5. 会员等级模块

#### 查询会员等级列表
```
GET /api/v1/mini/public/membership/levels
```

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "levelName": "普通会员",
      "levelTag": "normal",
      "upgradePointsThreshold": 0,
      "description": "享受基础会员权益",
      "iconUrl": "https://example.com/normal.png",
      "isActive": true
    },
    {
      "id": 2,
      "levelName": "白银会员",
      "levelTag": "silver",
      "upgradePointsThreshold": 1000,
      "description": "享受9.5折优惠",
      "iconUrl": "https://example.com/silver.png",
      "isActive": true
    }
  ]
}
```

### 6. 工具接口

#### 距离计算
```
POST /api/v1/mini/public/utils/calculate-distance
Content-Type: application/json

{
  "lat1": 39.916527,
  "lng1": 116.397128,
  "lat2": 39.916000,
  "lng2": 116.397000
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "distance": 0.5,
    "unit": "km"
  }
}
```

## 错误处理

所有接口都遵循统一的错误响应格式：

```json
{
  "success": false,
  "message": "错误描述信息",
  "timestamp": "2024-01-15T10:30:00"
}
```

## 注意事项

1. **无需认证**: 所有接口都是公开的，无需JWT Token
2. **分页限制**: 分页查询最大每页50条记录
3. **搜索功能**: 商品搜索只返回已上架的商品
4. **距离计算**: 使用Haversine公式计算地球表面两点间距离
5. **缓存建议**: 门店、分类等相对静态的数据建议前端缓存
6. **错误处理**: 建议前端对所有接口调用进行错误处理

## 测试建议

1. 使用提供的测试类 `MiniPublicControllerTest` 进行单元测试
2. 可以使用Postman或其他API测试工具进行集成测试
3. 建议在真实环境中测试距离计算的准确性
4. 测试各种边界情况，如空参数、无效ID等

## 实体类设计优势

### 1. 类型安全
- 使用强类型实体类替代Map，编译时即可发现类型错误
- IDE提供完整的代码提示和自动补全
- 减少运行时类型转换错误

### 2. 代码可维护性
- 清晰的字段定义和注释
- 统一的命名规范（Mini前缀区分小程序接口）
- 便于重构和字段变更

### 3. 接口文档化
- 实体类本身就是最好的接口文档
- 字段验证注解提供参数约束说明
- 静态工厂方法简化对象创建

### 4. 验证和转换
- 使用Bean Validation注解进行参数验证
- 提供from()静态方法进行实体转换
- 统一的错误处理和响应格式

## 性能优化建议

1. **数据库索引**: 确保门店、商品、分类等查询字段有适当的索引
2. **缓存策略**: 对门店列表、分类列表等相对静态的数据进行缓存
3. **分页查询**: 合理设置分页大小，避免一次性查询过多数据
4. **图片优化**: 商品图片建议使用CDN加速访问
5. **实体转换**: 使用流式API和静态方法提高转换效率
