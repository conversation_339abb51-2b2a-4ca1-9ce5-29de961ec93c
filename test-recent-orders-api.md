# 测试最近订单API

## 测试步骤

### 1. 启动应用程序
```bash
cd by-boot
mvn spring-boot:run
```

### 2. 测试今日统计数据接口
```bash
curl -X GET "http://localhost:8081/api/v1/statistics/today" \
     -H "Content-Type: application/json"
```

### 3. 测试最近订单接口
```bash
curl -X GET "http://localhost:8081/api/v1/statistics/recent-orders" \
     -H "Content-Type: application/json"
```

## 预期响应

### 今日统计数据响应示例
```json
{
  "code": 200,
  "msg": "今日统计数据",
  "data": {
    "todaySalesAmount": 0.00,
    "salesGrowthPercentage": 0.00,
    "todayOrderCount": 0,
    "orderGrowthPercentage": 0.00,
    "todayNewMemberCount": 0,
    "memberGrowthPercentage": 0.00
  },
  "timestamp": 1640234567890
}
```

### 最近订单响应示例
```json
{
  "code": 200,
  "msg": "最近5笔订单",
  "data": [
    {
      "orderNo": "20241223143001123456",
      "totalAmount": 158.50,
      "items": [
        {
          "productName": "招牌牛肉面",
          "quantity": 2
        },
        {
          "productName": "小笼包",
          "quantity": 1
        }
      ]
    }
  ],
  "timestamp": 1640234567890
}
```

## 注意事项

1. 如果数据库中没有订单数据，最近订单接口将返回空数组
2. 如果数据库中没有今日数据，统计接口将返回0值
3. 确保数据库连接正常
4. 确保相关表（orders, order_items, users）存在并有适当的数据

## 数据库准备（可选）

如果需要测试数据，可以在数据库中插入一些示例数据：

```sql
-- 插入示例订单（需要根据实际表结构调整）
INSERT INTO orders (order_no, store_id, payable_amount, payment_status, created_at, updated_at) 
VALUES 
('TEST001', 1, 100.00, 'PAID', NOW(), NOW()),
('TEST002', 1, 200.00, 'PAID', NOW(), NOW());

-- 插入示例订单商品项
INSERT INTO order_items (order_id, product_name, quantity, unit_price, total_price)
VALUES 
(1, '测试商品1', 2, 50.00, 100.00),
(2, '测试商品2', 1, 200.00, 200.00);
```
