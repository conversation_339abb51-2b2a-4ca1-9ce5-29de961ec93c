-- 为订单表添加支付方式字段
-- 执行时间: 2025-07-24

-- 添加支付方式字段
ALTER TABLE orders ADD COLUMN payment_method VARCHAR(20) COMMENT '支付方式：cash(现金支付), balance(余额支付), wechat_pay(微信支付), offline_scan(线下扫码)';

-- 为现有数据设置默认值（现金支付）
UPDATE orders SET payment_method = 'cash' WHERE payment_method IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_orders_payment_method ON orders(payment_method);

-- 添加注释
ALTER TABLE orders MODIFY COLUMN payment_method VARCHAR(20) NOT NULL COMMENT '支付方式：cash(现金支付), balance(余额支付), wechat_pay(微信支付), offline_scan(线下扫码)';
