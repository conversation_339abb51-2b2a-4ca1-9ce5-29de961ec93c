-- 新增：桌台使用记录表 (table_usages)
-- 执行时间: 2025-07-24

CREATE TABLE table_usages (
    "id" BIGSERIAL PRIMARY KEY,
    "table_id" BIGINT NOT NULL, -- 关联到 store_tables.id
    "order_id" VARCHAR(200), -- 关联到 orders.id，并确保一个订单只占用一次
    "status" VARCHAR(20) NOT NULL DEFAULT 'ACTIVE', -- ACTIVE(进行中), COMPLETED(已完成)
    "opened_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "closed_at" TIMESTAMP, -- 完成或结账的时间，可为空
    "duration_minutes" INT, -- 使用时长（分钟），可在关闭时计算并填入
    "store_id" BIGINT NOT NULL, -- 门店ID，用于数据隔离
    "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT NOW()
);

COMMENT ON TABLE table_usages IS '桌台使用记录表';
COMMENT ON COLUMN table_usages.id IS '使用记录ID';
COMMENT ON COLUMN table_usages.table_id IS '关联的桌台ID';
COMMENT ON COLUMN table_usages.order_id IS '关联的订单ID';
COMMENT ON COLUMN table_usages.status IS '本次使用记录的状态: ACTIVE(进行中), COMPLETED(已完成)';
COMMENT ON COLUMN table_usages.opened_at IS '开桌时间';
COMMENT ON COLUMN table_usages.closed_at IS '关桌/结账时间';
COMMENT ON COLUMN table_usages.duration_minutes IS '使用时长(分钟)，方便统计';
COMMENT ON COLUMN table_usages.store_id IS '门店ID，用于数据隔离';
COMMENT ON COLUMN table_usages.created_at IS '创建时间';
COMMENT ON COLUMN table_usages.updated_at IS '更新时间';

-- 创建索引
CREATE INDEX idx_table_usages_table_id ON table_usages(table_id);
CREATE INDEX idx_table_usages_order_id ON table_usages(order_id);
CREATE INDEX idx_table_usages_status ON table_usages(status);
CREATE INDEX idx_table_usages_store_id ON table_usages(store_id);
CREATE INDEX idx_table_usages_opened_at ON table_usages(opened_at);

-- 创建唯一约束：确保一个订单只能关联一个桌台使用记录
CREATE UNIQUE INDEX idx_table_usages_order_id_unique ON table_usages(order_id) WHERE order_id IS NOT NULL;

-- 创建唯一约束：确保一个桌台同时只能有一个活跃的使用记录
CREATE UNIQUE INDEX idx_table_usages_table_active_unique ON table_usages(table_id) WHERE status = 'ACTIVE';
