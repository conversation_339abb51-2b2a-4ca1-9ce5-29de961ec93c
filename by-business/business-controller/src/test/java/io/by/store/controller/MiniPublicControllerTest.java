package io.by.store.controller;

import io.by.store.application.*;
import io.by.store.controller.v1.mini.MiniPublicController;
import io.by.store.infrastructure.entity.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 小程序公开接口控制器测试
 */
public class MiniPublicControllerTest {

    private MockMvc mockMvc;

    @Mock
    private StoreService storeService;

    @Mock
    private ProductService productService;

    @Mock
    private ProductCategoryService categoryService;

    @Mock
    private StoreTableService storeTableService;

    @Mock
    private MembershipLevelService membershipLevelService;

    @InjectMocks
    private MiniPublicController miniPublicController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(miniPublicController).build();
    }

    @Test
    void testWxLogin_Success() throws Exception {
        mockMvc.perform(post("/api/v1/mini/public/auth/wx-login")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"code\":\"test_code_123\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("登录成功"))
                .andExpect(jsonPath("$.data.openid").exists())
                .andExpect(jsonPath("$.data.token").exists());
    }

    @Test
    void testWxLogin_EmptyCode() throws Exception {
        mockMvc.perform(post("/api/v1/mini/public/auth/wx-login")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"code\":\"\"}"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetStores_Success() throws Exception {
        // 准备测试数据
        Store store1 = new Store();
        store1.setId(1L);
        store1.setName("测试门店1");
        store1.setAddress("测试地址1");
        store1.setPhoneNumber("13800138001");
        store1.setX("116.397128");
        store1.setY("39.916527");
        store1.setIsActive(true);

        Store store2 = new Store();
        store2.setId(2L);
        store2.setName("测试门店2");
        store2.setAddress("测试地址2");
        store2.setPhoneNumber("13800138002");
        store2.setX("116.397000");
        store2.setY("39.916000");
        store2.setIsActive(true);

        List<Store> stores = Arrays.asList(store1, store2);

        when(storeService.getActiveStores()).thenReturn(stores);

        mockMvc.perform(get("/api/v1/mini/public/stores"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("测试门店1"))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].name").value("测试门店2"));
    }

    @Test
    void testCalculateStoreDistance_Success() throws Exception {
        // 准备测试数据
        Store store = new Store();
        store.setId(1L);
        store.setName("测试门店");
        store.setX("116.397128");
        store.setY("39.916527");

        when(storeService.getStoreById(1L)).thenReturn(store);

        mockMvc.perform(post("/api/v1/mini/public/stores/distance")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"userX\":\"116.397000\",\"userY\":\"39.916000\",\"storeIds\":[1]}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].storeId").value(1))
                .andExpect(jsonPath("$.data[0].storeName").value("测试门店"))
                .andExpect(jsonPath("$.data[0].unit").value("km"));
    }

    @Test
    void testGetStoreCategories_Success() throws Exception {
        // 准备测试数据
        ProductCategory category1 = new ProductCategory();
        category1.setId(1L);
        category1.setName("咖啡");
        category1.setParentId(0L);
        category1.setSortOrder(1);

        ProductCategory category2 = new ProductCategory();
        category2.setId(2L);
        category2.setName("茶饮");
        category2.setParentId(0L);
        category2.setSortOrder(2);

        List<ProductCategory> categories = Arrays.asList(category1, category2);

        when(categoryService.getCategoriesByStoreIdAndParentId(1L, 0L)).thenReturn(categories);
        when(categoryService.getCategoriesByStoreIdAndParentId(1L, 1L)).thenReturn(Arrays.asList());
        when(categoryService.getCategoriesByStoreIdAndParentId(1L, 2L)).thenReturn(Arrays.asList());

        mockMvc.perform(get("/api/v1/mini/public/stores/1/categories"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("咖啡"))
                .andExpect(jsonPath("$.data[0].hasChildren").value(false))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].name").value("茶饮"))
                .andExpect(jsonPath("$.data[1].hasChildren").value(false));
    }

    @Test
    void testGetStoreTables_Success() throws Exception {
        // 准备测试数据
        StoreTable table1 = new StoreTable();
        table1.setId(1L);
        table1.setTableNumber("A01");
        table1.setIsActive(true);
        table1.setQrcodeUrl("https://example.com/qr/table1.png");

        StoreTable table2 = new StoreTable();
        table2.setId(2L);
        table2.setTableNumber("A02");
        table2.setIsActive(false);
        table2.setQrcodeUrl("https://example.com/qr/table2.png");

        List<StoreTable> tables = Arrays.asList(table1, table2);

        when(storeTableService.getTablesByStoreId(1L)).thenReturn(tables);

        mockMvc.perform(get("/api/v1/mini/public/stores/1/tables"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].tableNumber").value("A01"))
                .andExpect(jsonPath("$.data[0].status").value("AVAILABLE"))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].tableNumber").value("A02"))
                .andExpect(jsonPath("$.data[1].status").value("UNAVAILABLE"));
    }

    @Test
    void testGetMembershipLevels_Success() throws Exception {
        // 准备测试数据
        MembershipLevel level1 = new MembershipLevel();
        level1.setId(1L);
        level1.setLevelName("普通会员");
        level1.setLevelTag("normal");
        level1.setUpgradePointsThreshold(0L);
        level1.setDescription("享受基础会员权益");
        level1.setIsActive(true);

        MembershipLevel level2 = new MembershipLevel();
        level2.setId(2L);
        level2.setLevelName("白银会员");
        level2.setLevelTag("silver");
        level2.setUpgradePointsThreshold(1000L);
        level2.setDescription("享受9.5折优惠");
        level2.setIsActive(true);

        List<MembershipLevel> levels = Arrays.asList(level1, level2);

        when(membershipLevelService.getAllActiveLevels()).thenReturn(levels);

        mockMvc.perform(get("/api/v1/mini/public/membership/levels"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].levelName").value("普通会员"))
                .andExpect(jsonPath("$.data[0].levelTag").value("normal"))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].levelName").value("白银会员"))
                .andExpect(jsonPath("$.data[1].levelTag").value("silver"));
    }

    @Test
    void testCalculateDistance_Success() throws Exception {
        mockMvc.perform(post("/api/v1/mini/public/utils/calculate-distance")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"lat1\":39.916527,\"lng1\":116.397128,\"lat2\":39.916000,\"lng2\":116.397000}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.unit").value("km"))
                .andExpect(jsonPath("$.data.distance").exists());
    }

    @Test
    void testCalculateDistance_MissingParams() throws Exception {
        mockMvc.perform(post("/api/v1/mini/public/utils/calculate-distance")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"lat1\":39.916527,\"lng1\":116.397128}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("坐标参数不能为空"));
    }
}
