package io.by.store.controller;

import io.by.store.application.ProductService;
import io.by.store.application.util.FileUploadUtils;
import io.by.store.controller.v1.ProductController;
import io.by.store.infrastructure.entity.Product;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 商品控制器图片上传功能测试
 */
public class ProductControllerImageTest {

    private MockMvc mockMvc;

    @Mock
    private ProductService productService;

    @Mock
    private FileUploadUtils fileUploadUtils;

    @InjectMocks
    private ProductController productController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(productController).build();
    }

    @Test
    void testCreateProductWithImage_Success() throws Exception {
        // 准备测试数据
        MockMultipartFile imageFile = new MockMultipartFile(
                "image", 
                "test-image.jpg", 
                "image/jpeg", 
                "test image content".getBytes()
        );

        // Mock 文件上传服务
        when(fileUploadUtils.isQiniuAvailable()).thenReturn(true);
        when(fileUploadUtils.uploadImage(any())).thenReturn("https://test-domain.com/test-image.jpg");

        // Mock 商品服务
        when(productService.createProduct(any(Product.class))).thenReturn(true);

        // 执行测试
        mockMvc.perform(multipart("/api/v1/products/create-with-image")
                .file(imageFile)
                .param("name", "测试商品")
                .param("description", "测试商品描述")
                .param("categoryId", "1")
                .param("price", "99.99")
                .param("stockQuantity", "100")
                .param("alertQuantity", "10")
                .param("productCode", "TEST001")
                .param("status", "PUBLISHED")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("商品创建成功"))
                .andExpect(jsonPath("$.data.name").value("测试商品"))
                .andExpect(jsonPath("$.data.imageUrl").value("https://test-domain.com/test-image.jpg"));
    }

    @Test
    void testCreateProductWithImage_NoQiniuService() throws Exception {
        // 准备测试数据
        MockMultipartFile imageFile = new MockMultipartFile(
                "image", 
                "test-image.jpg", 
                "image/jpeg", 
                "test image content".getBytes()
        );

        // Mock 文件上传服务不可用
        when(fileUploadUtils.isQiniuAvailable()).thenReturn(false);

        // 执行测试
        mockMvc.perform(multipart("/api/v1/products/create-with-image")
                .file(imageFile)
                .param("name", "测试商品")
                .param("description", "测试商品描述")
                .param("categoryId", "1")
                .param("price", "99.99")
                .param("stockQuantity", "100")
                .param("alertQuantity", "10")
                .param("productCode", "TEST001")
                .param("status", "PUBLISHED")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("文件上传服务未启用"));
    }

    @Test
    void testCreateProductWithImage_UploadFailed() throws Exception {
        // 准备测试数据
        MockMultipartFile imageFile = new MockMultipartFile(
                "image", 
                "test-image.jpg", 
                "image/jpeg", 
                "test image content".getBytes()
        );

        // Mock 文件上传失败
        when(fileUploadUtils.isQiniuAvailable()).thenReturn(true);
        when(fileUploadUtils.uploadImage(any())).thenThrow(new RuntimeException("上传失败"));

        // 执行测试
        mockMvc.perform(multipart("/api/v1/products/create-with-image")
                .file(imageFile)
                .param("name", "测试商品")
                .param("description", "测试商品描述")
                .param("categoryId", "1")
                .param("price", "99.99")
                .param("stockQuantity", "100")
                .param("alertQuantity", "10")
                .param("productCode", "TEST001")
                .param("status", "PUBLISHED")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("图片上传失败: 上传失败"));
    }

    @Test
    void testUpdateProductWithImage_Success() throws Exception {
        // 准备测试数据
        MockMultipartFile imageFile = new MockMultipartFile(
                "image", 
                "new-image.jpg", 
                "image/jpeg", 
                "new image content".getBytes()
        );

        Product existingProduct = new Product();
        existingProduct.setId(1L);
        existingProduct.setImageUrl("https://test-domain.com/old-image.jpg");

        Product updatedProduct = new Product();
        updatedProduct.setId(1L);
        updatedProduct.setName("更新的商品");
        updatedProduct.setImageUrl("https://test-domain.com/new-image.jpg");

        // Mock 服务
        when(productService.getProductById(1L)).thenReturn(Optional.of(existingProduct))
                .thenReturn(Optional.of(updatedProduct));
        when(fileUploadUtils.isQiniuAvailable()).thenReturn(true);
        when(fileUploadUtils.uploadImage(any())).thenReturn("https://test-domain.com/new-image.jpg");
        when(fileUploadUtils.deleteFile(anyString())).thenReturn(true);
        when(productService.updateProduct(any(Product.class))).thenReturn(true);

        // 执行测试
        mockMvc.perform(multipart("/api/v1/products/update-with-image")
                .file(imageFile)
                .param("id", "1")
                .param("name", "更新的商品")
                .param("description", "更新的商品描述")
                .param("categoryId", "1")
                .param("price", "129.99")
                .param("stockQuantity", "80")
                .param("alertQuantity", "5")
                .param("productCode", "TEST001")
                .param("status", "PUBLISHED")
                .param("keepOldImage", "false")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("商品更新成功"))
                .andExpect(jsonPath("$.data.name").value("更新的商品"))
                .andExpect(jsonPath("$.data.imageUrl").value("https://test-domain.com/new-image.jpg"));
    }

    @Test
    void testUpdateProductWithImage_ProductNotFound() throws Exception {
        // Mock 商品不存在
        when(productService.getProductById(1L)).thenReturn(Optional.empty());

        // 执行测试
        mockMvc.perform(multipart("/api/v1/products/update-with-image")
                .param("id", "1")
                .param("name", "更新的商品")
                .param("description", "更新的商品描述")
                .param("categoryId", "1")
                .param("price", "129.99")
                .param("stockQuantity", "80")
                .param("alertQuantity", "5")
                .param("productCode", "TEST001")
                .param("status", "PUBLISHED")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("商品不存在"));
    }

    @Test
    void testCreateProductWithoutImage_Success() throws Exception {
        // Mock 商品服务
        when(productService.createProduct(any(Product.class))).thenReturn(true);

        // 执行测试（不上传图片）
        mockMvc.perform(multipart("/api/v1/products/create-with-image")
                .param("name", "无图片商品")
                .param("description", "无图片商品描述")
                .param("categoryId", "1")
                .param("price", "99.99")
                .param("stockQuantity", "100")
                .param("alertQuantity", "10")
                .param("productCode", "TEST002")
                .param("status", "PUBLISHED")
                .contentType(MediaType.MULTIPART_FORM_DATA))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("商品创建成功"))
                .andExpect(jsonPath("$.data.name").value("无图片商品"));
    }
}
