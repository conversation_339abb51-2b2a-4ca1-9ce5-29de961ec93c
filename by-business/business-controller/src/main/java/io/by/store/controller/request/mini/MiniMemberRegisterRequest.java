package io.by.store.controller.request.mini;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 小程序会员注册请求
 */
@Data
public class MiniMemberRegisterRequest {

    /**
     * 微信小程序用户的唯一标识（必填）
     */
    @NotBlank(message = "微信OpenID不能为空")
    @Size(max = 100, message = "微信OpenID长度不能超过100个字符")
    private String wxOpenid;

    /**
     * 微信开放平台UnionID（可选）
     */
    @Size(max = 100, message = "微信UnionID长度不能超过100个字符")
    private String wxUnionid;

    /**
     * 手机号（可选，微信预注册时可为空）
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phoneNumber;

    /**
     * 用户昵称（可选）
     */
    @Size(max = 100, message = "昵称长度不能超过100个字符")
    private String nickname;

    /**
     * 用户头像URL（可选）
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatarUrl;
}
