package io.by.store.controller.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.by.store.application.AuthService;
import io.by.store.application.common.JwtUtil;
import io.by.store.application.common.UserContext;
import io.by.store.controller.common.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * JWT认证过滤器
 */
@Slf4j
@Component
@Order(1)
@RequiredArgsConstructor
public class JwtAuthenticationFilter implements Filter {

    private final JwtUtil jwtUtil;
    private final AuthService authService;

    /**
     * 不需要认证的路径
     */
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
            "/api/v1/auth/login",
            "/api/v1/auth/refresh",
            "/api/v1/mini",
            "/health",
            "/actuator",
            "/swagger",
            "/v3/api-docs",
            "/favicon.ico"
    );

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String requestURI = httpRequest.getRequestURI();
        String method = httpRequest.getMethod();

        try {
            // 跳过不需要认证的路径
            if (shouldSkipAuthentication(requestURI, method)) {
                chain.doFilter(request, response);
                return;
            }

            // 提取令牌
            String token = extractTokenFromRequest(httpRequest);
            if (StrUtil.isBlank(token)) {
                sendUnauthorizedResponse(httpResponse, "访问令牌缺失");
                return;
            }

            // 验证令牌
            if (!authService.validateAccessToken(token)) {
                sendUnauthorizedResponse(httpResponse, "访问令牌无效或已过期");
                return;
            }

            // 设置用户上下文信息
            setUserContext(token);

            // 继续处理请求
            chain.doFilter(request, response);
        } finally {
            // 请求完成后清理用户上下文
            UserContext.clear();
        }
    }

    /**
     * 判断是否跳过认证
     */
    private boolean shouldSkipAuthentication(String requestURI, String method) {
        // OPTIONS请求跳过认证
        if ("OPTIONS".equalsIgnoreCase(method)) {
            return true;
        }

        // 检查排除路径
        return EXCLUDE_PATHS.stream().anyMatch(requestURI::startsWith);
    }

    /**
     * 从请求中提取令牌
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 从Header中获取
        String bearerToken = request.getHeader("Authorization");
        if (StrUtil.isNotBlank(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }

        // 从参数中获取（备用方案）
        String tokenParam = request.getParameter("token");
        if (StrUtil.isNotBlank(tokenParam)) {
            return tokenParam;
        }

        return null;
    }

    /**
     * 设置用户上下文信息
     */
    private void setUserContext(String token) {
        try {
            Long staffId = jwtUtil.getStaffIdFromToken(token);
            String username = jwtUtil.getUsernameFromToken(token);
            Long storeId = jwtUtil.getStoreIdFromToken(token);
            Long roleId = jwtUtil.getRoleIdFromToken(token);

            // 创建用户信息对象
            UserContext.UserInfo userInfo = new UserContext.UserInfo();
            userInfo.setStaffId(staffId);
            userInfo.setUsername(username);
            userInfo.setStoreId(storeId);
            userInfo.setRoleId(roleId);
            userInfo.setToken(token);

            // 设置到ThreadLocal中
            UserContext.setCurrentUser(userInfo);

            log.debug("用户上下文设置成功: staffId={}, username={}, storeId={}",
                    staffId, username, storeId);
        } catch (Exception e) {
            log.warn("设置用户上下文失败: {}", e.getMessage());
            // 设置失败时清理上下文
            UserContext.clear();
        }
    }

    /**
     * 发送未授权响应
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        ApiResponse<Void> apiResponse = ApiResponse.error(401, message);
        String jsonResponse = JSONUtil.toJsonStr(apiResponse);

        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("JWT认证过滤器初始化完成");
    }

    @Override
    public void destroy() {
        log.info("JWT认证过滤器销毁");
    }
}
