package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 查询充值记录请求
 */
@Data
public class RechargeQueryRequest {
    
    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;
    
    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;
    
    /**
     * 会员ID
     */
    @Positive(message = "会员ID必须为正数")
    @NotNull(message = "会员ID不能为空")
    private Long memberId;
    
    /**
     * 充值订单号
     */
    private String orderNumber;
    
    /**
     * 充值状态
     */
    private String status;
    
    /**
     * 支付方式
     */
    private String paymentMethod;
    
    /**
     * 操作员工ID
     */
    @Positive(message = "员工ID必须为正数")
    private Long staffId;
    
    /**
     * 第三方交易流水号
     */
    private String transactionId;
    
    /**
     * 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String startTime;
    
    /**
     * 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String endTime;
}
