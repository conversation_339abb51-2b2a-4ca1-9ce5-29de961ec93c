package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.ProductService;
import io.by.store.application.common.UserContext;
import io.by.store.application.util.FileUploadUtils;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.ProductCreateRequest;
import io.by.store.controller.request.ProductQueryRequest;
import io.by.store.controller.request.ProductUpdateRequest;
import io.by.store.controller.response.ProductResponse;
import io.by.store.controller.response.ProductWithCategoryResponse;
import io.by.store.infrastructure.entity.Product;
import io.by.store.infrastructure.vo.ProductWithCategoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 商品控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/products")
@RequiredArgsConstructor
public class ProductController {

    private final ProductService productService;
    private final FileUploadUtils fileUploadUtils;

    /**
     * 创建商品（JSON格式）
     */
    @PostMapping("/create")
    public ApiResponse<ProductResponse> createProduct(@Valid @RequestBody ProductCreateRequest request) {
        Product product = new Product();
        BeanUtils.copyProperties(request, product);

        // 从当前登录用户获取门店ID
        product.setStoreId(UserContext.getCurrentStoreId());

        boolean success = productService.createProduct(product);
        if (success) {
            ProductResponse response = ProductResponse.from(product);
            return ApiResponse.success("商品创建成功", response);
        }
        return ApiResponse.error("商品创建失败");
    }


    /**
     * 更新商品（JSON格式）
     */
    @PostMapping("/update")
    public ApiResponse<ProductResponse> updateProduct(@Valid @RequestBody ProductUpdateRequest request) {
        Product product = new Product();
        BeanUtils.copyProperties(request, product);

        // 从当前登录用户获取门店ID
        product.setStoreId(UserContext.getCurrentStoreId());

        boolean success = productService.updateProduct(product);
        if (success) {
            Optional<Product> updated = productService.getProductById(request.getId());
            if (updated.isPresent()) {
                ProductResponse response = ProductResponse.from(updated.get());
                return ApiResponse.success("商品更新成功", response);
            }
        }
        return ApiResponse.error("商品更新失败");
    }


    /**
     * 删除商品
     */
    @PostMapping("/delete/{id}")
    public ApiResponse<Void> deleteProduct(@PathVariable Long id) {
        boolean success = productService.deleteProduct(id);
        if (success) {
            return ApiResponse.<Void>success("商品删除成功", null);
        }
        return ApiResponse.error("商品删除失败");
    }

    /**
     * 根据ID查询商品（联表查询，包含分类名称）
     */
    @GetMapping("/{id}")
    public ApiResponse<ProductWithCategoryResponse> getProductById(@PathVariable Long id) {
        Optional<ProductWithCategoryVO> product = productService.getProductByIdWithCategory(id);
        if (product.isPresent()) {
            ProductWithCategoryResponse response = ProductWithCategoryResponse.from(product.get());
            return ApiResponse.success(response);
        }
        return ApiResponse.notFound("商品不存在");
    }

    /**
     * 分页查询商品（联表查询，包含分类名称）
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<ProductWithCategoryResponse>> queryProducts(@Valid @RequestBody ProductQueryRequest request) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        IPage<ProductWithCategoryVO> page = productService.queryProductsWithCategory(
                request.getCurrent(), request.getSize(), request.getName(),
                request.getCategoryId(), request.getStatus(), request.getProductCode(), storeId);

        List<ProductWithCategoryResponse> responseList = ProductWithCategoryResponse.from(page.getRecords());
        PageResponse<ProductWithCategoryResponse> pageResponse = new PageResponse<>();
        pageResponse.setCurrent(page.getCurrent());
        pageResponse.setSize(page.getSize());
        pageResponse.setTotal(page.getTotal());
        pageResponse.setPages(page.getPages());
        pageResponse.setRecords(responseList);

        return ApiResponse.success(pageResponse);
    }



    /**
     * 根据分类查询商品（联表查询，包含分类名称）
     */
    @GetMapping("/category/{categoryId}")
    public ApiResponse<List<ProductWithCategoryResponse>> getProductsByCategory(@PathVariable Long categoryId) {
        List<ProductWithCategoryVO> products = productService.getProductsByCategoryIdWithCategory(categoryId);
        List<ProductWithCategoryResponse> responseList = ProductWithCategoryResponse.from(products);
        return ApiResponse.success(responseList);
    }

    /**
     * 根据状态查询商品
     */
    @GetMapping("/status/{status}")
    public ApiResponse<List<ProductResponse>> getProductsByStatus(@PathVariable String status) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Product> products = productService.getProductsByStatus(status, storeId);
        List<ProductResponse> responseList = ProductResponse.from(products);
        return ApiResponse.success(responseList);
    }

    /**
     * 根据商品编码查询商品
     */
    @GetMapping("/code/{productCode}")
    public ApiResponse<ProductResponse> getProductByCode(@PathVariable String productCode) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        Optional<Product> product = productService.getProductByCode(productCode, storeId);
        if (product.isPresent()) {
            ProductResponse response = ProductResponse.from(product.get());
            return ApiResponse.success(response);
        }
        return ApiResponse.notFound("商品不存在");
    }

    /**
     * 根据名称搜索商品
     */
    @GetMapping("/search")
    public ApiResponse<List<ProductResponse>> searchProducts(@RequestParam String name) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Product> products = productService.searchProductsByName(name, storeId);
        List<ProductResponse> responseList = ProductResponse.from(products);
        return ApiResponse.success(responseList);
    }

    // ==================== 库存管理 ====================

    /**
     * 更新库存
     */
    @PostMapping("/stock/{id}")
    public ApiResponse<Void> updateStock(@PathVariable Long id, @RequestParam Integer quantity) {
        boolean success = productService.updateStock(id, quantity);
        if (success) {
            return ApiResponse.<Void>success("库存更新成功", null);
        }
        return ApiResponse.error("库存更新失败");
    }

    /**
     * 增加库存
     */
    @PostMapping("/stock/increase/{id}")
    public ApiResponse<Void> increaseStock(@PathVariable Long id, @RequestParam Integer quantity) {
        boolean success = productService.increaseStock(id, quantity);
        if (success) {
            return ApiResponse.<Void>success("库存增加成功", null);
        }
        return ApiResponse.error("库存增加失败");
    }

    /**
     * 减少库存
     */
    @PostMapping("/stock/decrease/{id}")
    public ApiResponse<Void> decreaseStock(@PathVariable Long id, @RequestParam Integer quantity) {
        boolean success = productService.decreaseStock(id, quantity);
        if (success) {
            return ApiResponse.<Void>success("库存减少成功", null);
        }
        return ApiResponse.error("库存减少失败");
    }

    /**
     * 查询库存不足的商品
     */
    @GetMapping("/stock/low")
    public ApiResponse<List<ProductResponse>> getLowStockProducts() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Product> products = productService.getLowStockProducts(storeId);
        List<ProductResponse> responseList = ProductResponse.from(products);
        return ApiResponse.success(responseList);
    }

    /**
     * 查询零库存商品
     */
    @GetMapping("/stock/zero")
    public ApiResponse<List<ProductResponse>> getZeroStockProducts() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Product> products = productService.getZeroStockProducts(storeId);
        List<ProductResponse> responseList = ProductResponse.from(products);
        return ApiResponse.success(responseList);
    }

    // ==================== 状态管理 ====================

    /**
     * 上架商品
     */
    @PostMapping("/publish/{id}")
    public ApiResponse<Void> publishProduct(@PathVariable Long id) {
        boolean success = productService.publishProduct(id);
        if (success) {
            return ApiResponse.<Void>success("商品上架成功", null);
        }
        return ApiResponse.error("商品上架失败");
    }

    /**
     * 下架商品
     */
    @PostMapping("/archive/{id}")
    public ApiResponse<Void> archiveProduct(@PathVariable Long id) {
        boolean success = productService.archiveProduct(id);
        if (success) {
            return ApiResponse.<Void>success("商品下架成功", null);
        }
        return ApiResponse.error("商品下架失败");
    }

    /**
     * 标记为售罄
     */
    @PostMapping("/sold-out/{id}")
    public ApiResponse<Void> markAsSoldOut(@PathVariable Long id) {
        boolean success = productService.markAsSoldOut(id);
        if (success) {
            return ApiResponse.<Void>success("商品标记为售罄成功", null);
        }
        return ApiResponse.error("商品标记为售罄失败");
    }

    /**
     * 批量更新商品状态
     */
    @PostMapping("/batch-status")
    public ApiResponse<Void> updateStatusBatch(@RequestBody List<Long> ids, @RequestParam String status) {
        boolean success = productService.updateStatusBatch(ids, status);
        if (success) {
            return ApiResponse.<Void>success("批量更新状态成功", null);
        }
        return ApiResponse.error("批量更新状态失败");
    }

    // ==================== 价格管理 ====================

    /**
     * 更新商品价格
     */
    @PostMapping("/price/{id}")
    public ApiResponse<Void> updatePrice(@PathVariable Long id, @RequestParam BigDecimal price) {
        boolean success = productService.updatePrice(id, price);
        if (success) {
            return ApiResponse.<Void>success("价格更新成功", null);
        }
        return ApiResponse.error("价格更新失败");
    }

    /**
     * 根据价格范围查询商品
     */
    @GetMapping("/price-range")
    public ApiResponse<List<ProductResponse>> getProductsByPriceRange(
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Product> products = productService.getProductsByPriceRange(minPrice, maxPrice, storeId);
        List<ProductResponse> responseList = ProductResponse.from(products);
        return ApiResponse.success(responseList);
    }

    // ==================== 统计功能 ====================

    /**
     * 统计商品总数
     */
    @GetMapping("/count")
    public ApiResponse<Long> countProducts() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        Long count = productService.countProductsByStoreId(storeId);
        return ApiResponse.success(count);
    }

    /**
     * 统计各状态商品数量
     */
    @GetMapping("/count/status/{status}")
    public ApiResponse<Long> countProductsByStatus(@PathVariable String status) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        Long count = productService.countProductsByStatus(status, storeId);
        return ApiResponse.success(count);
    }

    /**
     * 批量删除商品
     */
    @PostMapping("/batch-delete")
    public ApiResponse<Void> deleteProductsBatch(@RequestBody List<Long> ids) {
        boolean success = productService.deleteProductsBatch(ids);
        if (success) {
            return ApiResponse.<Void>success("批量删除成功", null);
        }
        return ApiResponse.error("批量删除失败");
    }
}
