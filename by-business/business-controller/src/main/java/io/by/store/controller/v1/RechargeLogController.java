package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.RechargeLogService;
import io.by.store.application.common.UserContext;
import io.by.store.controller.request.RechargeConfirmRequest;
import io.by.store.controller.request.RechargeCreateRequest;
import io.by.store.controller.request.RechargeFailRequest;
import io.by.store.controller.request.RechargeQueryRequest;
import io.by.store.controller.request.OfflineRechargeRequest;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.response.RechargeLogResponse;
import io.by.store.controller.response.RechargeStatisticsResponse;
import io.by.store.infrastructure.entity.RechargeLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 充值记录控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/recharge-logs")
@RequiredArgsConstructor
public class RechargeLogController {

    private final RechargeLogService rechargeLogService;

    /**
     * 创建充值记录
     */
    @PostMapping("/create")
    public ApiResponse<RechargeLogResponse> createRechargeLog(@Valid @RequestBody RechargeCreateRequest request) {
        RechargeLog rechargeLog = new RechargeLog();
        BeanUtils.copyProperties(request, rechargeLog);

        boolean success = rechargeLogService.createRechargeLog(rechargeLog);
        if (success) {
            RechargeLogResponse response = RechargeLogResponse.from(rechargeLog);
            return ApiResponse.success("充值记录创建成功", response);
        } else {
            return ApiResponse.error("充值记录创建失败");
        }
    }

    /**
     * 线下充值（直接成功）
     */
    @PostMapping("/offline-recharge")
    public ApiResponse<RechargeLogResponse> offlineRecharge(@Valid @RequestBody OfflineRechargeRequest request) {
        try {
            UserContext.UserInfo userInfo = UserContext.getCurrentUser();
            RechargeLog rechargeLog = rechargeLogService.processOfflineRecharge(
                    request.getMemberId(),
                    request.getAmount(),
                    request.getPaymentMethod(),
                    userInfo.getStaffId(),
                    request.getRemark(),
                    request.getPaymentTransactionId()
            );

            RechargeLogResponse response = RechargeLogResponse.from(rechargeLog);
            return ApiResponse.success("线下充值成功", response);
        } catch (Exception e) {
            log.error("线下充值失败: {}", e.getMessage(), e);
            return ApiResponse.error("线下充值失败: " + e.getMessage());
        }
    }

    /**
     * 确认充值成功
     */
    @PostMapping("/confirm")
    public ApiResponse<Void> confirmRecharge(@Valid @RequestBody RechargeConfirmRequest request) {
        boolean success = rechargeLogService.confirmRecharge(
                request.getOrderNumber(), 
                request.getTransactionId()
        );
        if (success) {
            return ApiResponse.<Void>success("充值确认成功", null);
        } else {
            return ApiResponse.error("充值确认失败");
        }
    }

    /**
     * 充值失败
     */
    @PostMapping("/fail")
    public ApiResponse<Void> failRecharge(@Valid @RequestBody RechargeFailRequest request) {
        boolean success = rechargeLogService.failRecharge(
                request.getOrderNumber(), 
                request.getReason()
        );
        if (success) {
            return ApiResponse.<Void>success("充值失败处理成功", null);
        } else {
            return ApiResponse.error("充值失败处理失败");
        }
    }

    /**
     * 充值退款
     */
    @PostMapping("/refund/{orderNumber}")
    public ApiResponse<Void> refundRecharge(@PathVariable String orderNumber) {
        boolean success = rechargeLogService.refundRecharge(orderNumber);
        if (success) {
            return ApiResponse.<Void>success("充值退款成功", null);
        } else {
            return ApiResponse.error("充值退款失败");
        }
    }

    /**
     * 根据ID查询充值记录
     */
    @GetMapping("/{id}")
    public ApiResponse<RechargeLogResponse> getRechargeLogById(@PathVariable Long id) {
        RechargeLog rechargeLog = rechargeLogService.getRechargeLogById(id);
        if (rechargeLog != null) {
            RechargeLogResponse response = RechargeLogResponse.from(rechargeLog);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("充值记录不存在");
        }
    }

    /**
     * 根据订单号查询充值记录
     */
    @GetMapping("/by-order/{orderNumber}")
    public ApiResponse<RechargeLogResponse> getRechargeLogByOrderNumber(@PathVariable String orderNumber) {
        RechargeLog rechargeLog = rechargeLogService.getRechargeLogByOrderNumber(orderNumber);
        if (rechargeLog != null) {
            RechargeLogResponse response = RechargeLogResponse.from(rechargeLog);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("充值记录不存在");
        }
    }

    /**
     * 根据会员ID查询充值记录
     */
    @GetMapping("/by-member/{memberId}")
    public ApiResponse<List<RechargeLogResponse>> getRechargeLogsByMemberId(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        List<RechargeLog> rechargeLogs = rechargeLogService.getRechargeLogsByMemberId(memberId);
        List<RechargeLogResponse> responses = rechargeLogs.stream()
                .map(RechargeLogResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 分页查询充值记录
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<RechargeLogResponse>> queryRechargeLogs(@Valid @RequestBody RechargeQueryRequest request) {
        IPage<RechargeLog> page = rechargeLogService.getRechargeLogsPage(
                request.getCurrent(),
                request.getSize(),
                request.getMemberId(),
                request.getStatus(),
                request.getPaymentMethod(),
                request.getStaffId()
        );

        List<RechargeLogResponse> responses = page.getRecords().stream()
                .map(RechargeLogResponse::from)
                .collect(Collectors.toList());

        PageResponse<RechargeLogResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 获取充值统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<RechargeStatisticsResponse> getRechargeStatistics() {
        RechargeLogService.RechargeStatistics statistics = rechargeLogService.getRechargeStatistics();
        RechargeStatisticsResponse response = RechargeStatisticsResponse.from(statistics);
        return ApiResponse.success("充值统计信息", response);
    }
}
