package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.ModifierService;
import io.by.store.application.common.UserContext;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.ModifierCreateRequest;
import io.by.store.controller.request.ModifierQueryRequest;
import io.by.store.controller.request.ModifierUpdateRequest;
import io.by.store.controller.response.ModifierResponse;
import io.by.store.infrastructure.entity.Modifier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 加料控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/modifiers")
@RequiredArgsConstructor
public class ModifierController {

    private final ModifierService modifierService;

    /**
     * 创建加料
     */
    @PostMapping("/create")
    public ApiResponse<ModifierResponse> createModifier(@Valid @RequestBody ModifierCreateRequest request) {
        Modifier modifier = new Modifier();
        BeanUtils.copyProperties(request, modifier);

        // 从当前登录用户获取门店ID
        modifier.setStoreId(UserContext.getCurrentStoreId());

        boolean success = modifierService.createModifier(modifier);
        if (success) {
            ModifierResponse response = ModifierResponse.from(modifier);
            return ApiResponse.success("加料创建成功", response);
        }
        return ApiResponse.error("加料创建失败");
    }

    /**
     * 更新加料
     */
    @PostMapping("/update")
    public ApiResponse<ModifierResponse> updateModifier(@Valid @RequestBody ModifierUpdateRequest request) {
        Modifier modifier = new Modifier();
        BeanUtils.copyProperties(request, modifier);

        // 从当前登录用户获取门店ID
        modifier.setStoreId(UserContext.getCurrentStoreId());

        boolean success = modifierService.updateModifier(modifier);
        if (success) {
            Optional<Modifier> updated = modifierService.getModifierById(request.getId());
            if (updated.isPresent()) {
                ModifierResponse response = ModifierResponse.from(updated.get());
                return ApiResponse.success("加料更新成功", response);
            }
        }
        return ApiResponse.error("加料更新失败");
    }

    /**
     * 删除加料
     */
    @PostMapping("/delete/{id}")
    public ApiResponse<Void> deleteModifier(@PathVariable Long id) {
        boolean success = modifierService.deleteModifier(id);
        if (success) {
            return ApiResponse.<Void>success("加料删除成功", null);
        }
        return ApiResponse.error("加料删除失败");
    }

    /**
     * 根据ID查询加料
     */
    @GetMapping("/{id}")
    public ApiResponse<ModifierResponse> getModifierById(@PathVariable Long id) {
        Optional<Modifier> modifier = modifierService.getModifierById(id);
        if (modifier.isPresent()) {
            ModifierResponse response = ModifierResponse.from(modifier.get());
            return ApiResponse.success(response);
        }
        return ApiResponse.notFound("加料不存在");
    }

    /**
     * 查询所有加料
     */
    @GetMapping("/all")
    public ApiResponse<List<ModifierResponse>> getAllModifiers() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Modifier> modifiers = modifierService.getModifiersByStoreId(storeId);
        List<ModifierResponse> responseList = ModifierResponse.from(modifiers);
        return ApiResponse.success(responseList);
    }

    /**
     * 分页查询加料
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<ModifierResponse>> queryModifiers(@Valid @RequestBody ModifierQueryRequest request) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        IPage<Modifier> page = modifierService.queryModifiers(
                request.getCurrent(), request.getSize(), request.getName(), storeId);

        List<ModifierResponse> responseList = ModifierResponse.from(page.getRecords());
        PageResponse<ModifierResponse> pageResponse = new PageResponse<>();
        pageResponse.setCurrent(page.getCurrent());
        pageResponse.setSize(page.getSize());
        pageResponse.setTotal(page.getTotal());
        pageResponse.setRecords(responseList);

        return ApiResponse.success(pageResponse);
    }

    /**
     * 根据名称搜索加料
     */
    @GetMapping("/search")
    public ApiResponse<List<ModifierResponse>> searchModifiers(@RequestParam String name) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Modifier> modifiers = modifierService.searchModifiersByName(name, storeId);
        List<ModifierResponse> responseList = ModifierResponse.from(modifiers);
        return ApiResponse.success(responseList);
    }

    // ==================== 价格管理 ====================

    /**
     * 更新加料价格
     */
    @PostMapping("/price/{id}")
    public ApiResponse<Void> updatePrice(@PathVariable Long id, @RequestParam BigDecimal priceChange) {
        boolean success = modifierService.updatePrice(id, priceChange);
        if (success) {
            return ApiResponse.<Void>success("价格更新成功", null);
        }
        return ApiResponse.error("价格更新失败");
    }

    /**
     * 根据价格范围查询加料
     */
    @GetMapping("/price-range")
    public ApiResponse<List<ModifierResponse>> getModifiersByPriceRange(
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Modifier> modifiers = modifierService.getModifiersByPriceRange(minPrice, maxPrice, storeId);
        List<ModifierResponse> responseList = ModifierResponse.from(modifiers);
        return ApiResponse.success(responseList);
    }

    /**
     * 查询免费加料
     */
    @GetMapping("/free")
    public ApiResponse<List<ModifierResponse>> getFreeModifiers() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Modifier> modifiers = modifierService.getFreeModifiers(storeId);
        List<ModifierResponse> responseList = ModifierResponse.from(modifiers);
        return ApiResponse.success(responseList);
    }

    /**
     * 查询收费加料
     */
    @GetMapping("/paid")
    public ApiResponse<List<ModifierResponse>> getPaidModifiers() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<Modifier> modifiers = modifierService.getPaidModifiers(storeId);
        List<ModifierResponse> responseList = ModifierResponse.from(modifiers);
        return ApiResponse.success(responseList);
    }

    /**
     * 批量更新加料价格
     */
    @PostMapping("/batch-price")
    public ApiResponse<Void> updatePriceBatch(@RequestBody List<Long> ids, @RequestParam BigDecimal priceChange) {
        boolean success = modifierService.updatePriceBatch(ids, priceChange);
        if (success) {
            return ApiResponse.<Void>success("批量更新价格成功", null);
        }
        return ApiResponse.error("批量更新价格失败");
    }

    // ==================== 统计功能 ====================

    /**
     * 统计加料总数
     */
    @GetMapping("/count")
    public ApiResponse<Long> countModifiers() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        Long count = modifierService.countModifiersByStoreId(storeId);
        return ApiResponse.success(count);
    }

    /**
     * 统计免费加料数量
     */
    @GetMapping("/count/free")
    public ApiResponse<Long> countFreeModifiers() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        Long count = modifierService.countFreeModifiers(storeId);
        return ApiResponse.success(count);
    }

    /**
     * 统计收费加料数量
     */
    @GetMapping("/count/paid")
    public ApiResponse<Long> countPaidModifiers() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        Long count = modifierService.countPaidModifiers(storeId);
        return ApiResponse.success(count);
    }

    /**
     * 获取加料统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<ModifierService.ModifierStatistics> getModifierStatistics() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        ModifierService.ModifierStatistics statistics = modifierService.getModifierStatistics(storeId);
        return ApiResponse.success(statistics);
    }

    /**
     * 批量删除加料
     */
    @PostMapping("/batch-delete")
    public ApiResponse<Void> deleteModifiersBatch(@RequestBody List<Long> ids) {
        boolean success = modifierService.deleteModifiersBatch(ids);
        if (success) {
            return ApiResponse.<Void>success("批量删除成功", null);
        }
        return ApiResponse.error("批量删除失败");
    }
}
