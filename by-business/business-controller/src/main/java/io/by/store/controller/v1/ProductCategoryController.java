package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.ProductCategoryService;
import io.by.store.application.common.UserContext;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.ProductCategoryCreateRequest;
import io.by.store.controller.request.ProductCategoryQueryRequest;
import io.by.store.controller.request.ProductCategoryUpdateRequest;
import io.by.store.controller.response.ProductCategoryResponse;
import io.by.store.infrastructure.entity.ProductCategory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;

/**
 * 商品分类控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/product-categories")
@RequiredArgsConstructor
public class ProductCategoryController {

    private final ProductCategoryService categoryService;

    /**
     * 创建分类
     */
    @PostMapping("/create")
    public ApiResponse<ProductCategoryResponse> createCategory(@Valid @RequestBody ProductCategoryCreateRequest request) {
        ProductCategory category = new ProductCategory();
        BeanUtils.copyProperties(request, category);

        // 从当前登录用户获取门店ID
        category.setStoreId(UserContext.getCurrentStoreId());

        boolean success = categoryService.createCategory(category);
        if (success) {
            ProductCategoryResponse response = ProductCategoryResponse.from(category);
            return ApiResponse.success("分类创建成功", response);
        }
        return ApiResponse.error("分类创建失败");
    }

    /**
     * 更新分类
     */
    @PostMapping("/update")
    public ApiResponse<ProductCategoryResponse> updateCategory(@Valid @RequestBody ProductCategoryUpdateRequest request) {
        ProductCategory category = new ProductCategory();
        BeanUtils.copyProperties(request, category);

        // 从当前登录用户获取门店ID
        category.setStoreId(UserContext.getCurrentStoreId());

        boolean success = categoryService.updateCategory(category);
        if (success) {
            Optional<ProductCategory> updated = categoryService.getCategoryById(request.getId());
            if (updated.isPresent()) {
                ProductCategoryResponse response = ProductCategoryResponse.from(updated.get());
                return ApiResponse.success("分类更新成功", response);
            }
        }
        return ApiResponse.error("分类更新失败");
    }

    /**
     * 删除分类
     */
    @PostMapping("/delete/{id}")
    public ApiResponse<Void> deleteCategory(@PathVariable Long id) {
        boolean success = categoryService.deleteCategory(id);
        if (success) {
            return ApiResponse.<Void>success("分类删除成功", null);
        }
        return ApiResponse.error("分类删除失败");
    }

    /**
     * 根据ID查询分类
     */
    @GetMapping("/{id}")
    public ApiResponse<ProductCategoryResponse> getCategoryById(@PathVariable Long id) {
        Optional<ProductCategory> category = categoryService.getCategoryById(id);
        if (category.isPresent()) {
            ProductCategoryResponse response = ProductCategoryResponse.from(category.get());
            return ApiResponse.success(response);
        }
        return ApiResponse.notFound("分类不存在");
    }

    /**
     * 分页查询分类
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<ProductCategoryResponse>> queryCategories(@Valid @RequestBody ProductCategoryQueryRequest request) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        IPage<ProductCategory> page = categoryService.queryCategories(
                request.getCurrent(), request.getSize(), request.getName(),
                request.getParentId(), storeId);

        List<ProductCategoryResponse> responseList = ProductCategoryResponse.from(page.getRecords());
        PageResponse<ProductCategoryResponse> pageResponse = new PageResponse<>();
        pageResponse.setCurrent(page.getCurrent());
        pageResponse.setSize(page.getSize());
        pageResponse.setTotal(page.getTotal());
        pageResponse.setRecords(responseList);
        pageResponse.setPages(page.getPages());

        return ApiResponse.success(pageResponse);
    }

    /**
     * 查询顶级分类
     */
    @GetMapping("/top-level")
    public ApiResponse<List<ProductCategoryResponse>> getTopLevelCategories() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<ProductCategory> categories = categoryService.getTopLevelCategories(storeId);
        List<ProductCategoryResponse> responseList = ProductCategoryResponse.from(categories);
        return ApiResponse.success(responseList);
    }

    /**
     * 查询子分类
     */
    @GetMapping("/children/{parentId}")
    public ApiResponse<List<ProductCategoryResponse>> getChildCategories(@PathVariable Long parentId) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<ProductCategory> categories = categoryService.getChildCategories(storeId, parentId);
        List<ProductCategoryResponse> responseList = ProductCategoryResponse.from(categories);
        return ApiResponse.success(responseList);
    }

    /**
     * 查询所有分类（树形结构）
     */
    @GetMapping("/tree")
    public ApiResponse<List<ProductCategoryResponse>> getCategoryTree() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<ProductCategory> allCategories = categoryService.getCategoriesByStoreId(storeId);
        List<ProductCategoryResponse> responseList = ProductCategoryResponse.from(allCategories);

        // TODO: 构建树形结构
        // 这里可以添加构建树形结构的逻辑

        return ApiResponse.success(responseList);
    }

    /**
     * 更新分类排序
     */
    @PostMapping("/sort/{id}")
    public ApiResponse<Void> updateSortOrder(@PathVariable Long id, @RequestParam Integer sortOrder) {
        boolean success = categoryService.updateSortOrder(id, sortOrder);
        if (success) {
            return ApiResponse.<Void>success("排序更新成功", null);
        }
        return ApiResponse.error("排序更新失败");
    }

    /**
     * 根据名称搜索分类
     */
    @GetMapping("/search")
    public ApiResponse<List<ProductCategoryResponse>> searchCategories(@RequestParam String name) {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        List<ProductCategory> categories = categoryService.searchCategoriesByName(name, storeId);
        List<ProductCategoryResponse> responseList = ProductCategoryResponse.from(categories);
        return ApiResponse.success(responseList);
    }

    /**
     * 统计分类数量
     */
    @GetMapping("/count")
    public ApiResponse<Long> countCategories() {
        // 从当前登录用户获取门店ID
        Long storeId = UserContext.getCurrentStoreId();

        Long count = categoryService.countCategoriesByStoreId(storeId);
        return ApiResponse.success(count);
    }

    /**
     * 批量删除分类
     */
    @PostMapping("/batch-delete")
    public ApiResponse<Void> deleteCategoriesBatch(@RequestBody List<Long> ids) {
        boolean success = categoryService.deleteCategoriesBatch(ids);
        if (success) {
            return ApiResponse.<Void>success("批量删除成功", null);
        }
        return ApiResponse.error("批量删除失败");
    }
}
