package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 更新备忘录状态请求
 */
@Data
public class StoreMemoStatusUpdateRequest {

    /**
     * 备忘录ID列表
     */
    @NotEmpty(message = "备忘录ID列表不能为空")
    private List<Long> ids;

    /**
     * 新状态
     */
    @NotBlank(message = "状态不能为空")
    private String status;
}
