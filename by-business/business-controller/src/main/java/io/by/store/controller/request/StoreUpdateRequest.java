package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 更新门店请求
 */
@Data
public class StoreUpdateRequest {
    
    /**
     * 门店ID
     */
    @NotNull(message = "门店ID不能为空")
    private Long id;
    
    /**
     * 门店名称
     */
    @Size(max = 100, message = "门店名称长度不能超过100个字符")
    private String name;
    
    /**
     * 门店地址
     */
    @Size(max = 500, message = "门店地址长度不能超过500个字符")
    private String address;
    
    /**
     * 门店电话
     */
    @Size(max = 20, message = "门店电话长度不能超过20个字符")
    private String phoneNumber;
    
    /**
     * 是否激活：true-激活，false-停用
     */
    private Boolean isActive;
}
