package io.by.store.controller.response;

import io.by.store.infrastructure.entity.Modifier;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 加料响应
 */
@Data
public class ModifierResponse {

    /**
     * 加料ID
     */
    private Long id;

    /**
     * 加料名称
     */
    private String name;

    /**
     * 价格变动 (通常为正数，表示加价)
     */
    private BigDecimal priceChange;

    /**
     * 所属门店ID
     */
    private Long storeId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否免费加料
     */
    private Boolean isFree;

    /**
     * 从实体转换为响应对象
     */
    public static ModifierResponse from(Modifier modifier) {
        if (modifier == null) {
            return null;
        }
        ModifierResponse response = new ModifierResponse();
        BeanUtils.copyProperties(modifier, response);
        
        // 判断是否免费加料
        response.setIsFree(modifier.getPriceChange() != null && 
                          modifier.getPriceChange().compareTo(BigDecimal.ZERO) == 0);
        
        return response;
    }

    /**
     * 批量转换
     */
    public static List<ModifierResponse> from(List<Modifier> modifiers) {
        if (modifiers == null) {
            return null;
        }
        return modifiers.stream()
                .map(ModifierResponse::from)
                .collect(Collectors.toList());
    }
}
