package io.by.store.controller.response;

import io.by.store.infrastructure.entity.StoreMemo;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 备忘录响应
 */
@Data
public class StoreMemoResponse {

    /**
     * 备忘录ID
     */
    private Long id;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 备忘录内容
     */
    private String content;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 从实体转换
     */
    public static StoreMemoResponse from(StoreMemo memo) {
        StoreMemoResponse response = new StoreMemoResponse();
        response.setId(memo.getId());
        response.setStoreId(memo.getStoreId());
        response.setContent(memo.getContent());
        response.setStatus(memo.getStatus());
        
        // 设置状态描述
        try {
            StoreMemo.Status status = StoreMemo.Status.fromCode(memo.getStatus());
            response.setStatusDescription(status.getDescription());
        } catch (Exception e) {
            response.setStatusDescription(memo.getStatus());
        }
        
        response.setCreatorId(memo.getCreatorId());
        response.setCreatedAt(memo.getCreatedAt());
        response.setUpdatedAt(memo.getUpdatedAt());
        response.setCompletedAt(memo.getCompletedAt());
        
        return response;
    }
}
