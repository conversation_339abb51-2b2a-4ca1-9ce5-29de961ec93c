package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 创建门店请求
 */
@Data
public class StoreCreateRequest {
    
    /**
     * 门店名称
     */
    @NotBlank(message = "门店名称不能为空")
    @Size(max = 100, message = "门店名称长度不能超过100个字符")
    private String name;
    
    /**
     * 门店地址
     */
    @Size(max = 500, message = "门店地址长度不能超过500个字符")
    private String address;
    
    /**
     * 门店电话
     */
    @Size(max = 20, message = "门店电话长度不能超过20个字符")
    private String phoneNumber;
    
    /**
     * 是否激活：true-激活，false-停用
     */
    private Boolean isActive;
}
