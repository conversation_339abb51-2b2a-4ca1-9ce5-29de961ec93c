package io.by.store.controller.response;

import io.by.store.infrastructure.entity.RechargeLog;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值记录响应
 */
@Data
public class RechargeLogResponse {
    
    /**
     * 充值记录ID
     */
    private Long id;
    
    /**
     * 充值订单号
     */
    private String rechargeOrderNumber;
    
    /**
     * 充值会员ID
     */
    private Long memberId;
    
    /**
     * 充值金额
     */
    private BigDecimal amount;
    
    /**
     * 充值前账户余额
     */
    private BigDecimal balanceBefore;
    
    /**
     * 充值后账户余额
     */
    private BigDecimal balanceAfter;
    
    /**
     * 支付方式
     */
    private String paymentMethod;
    
    /**
     * 支付方式名称
     */
    private String paymentMethodName;
    
    /**
     * 第三方支付交易流水号
     */
    private String paymentTransactionId;
    
    /**
     * 充值状态
     */
    private String status;
    
    /**
     * 充值状态名称
     */
    private String statusName;
    
    /**
     * 操作员工ID
     */
    private Long staffId;

    /**
     * 来源
     */
    private String source;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 从实体类转换
     */
    public static RechargeLogResponse from(RechargeLog rechargeLog) {
        if (rechargeLog == null) {
            return null;
        }
        
        RechargeLogResponse response = new RechargeLogResponse();
        response.setId(rechargeLog.getId());
        response.setRechargeOrderNumber(rechargeLog.getRechargeOrderNumber());
        response.setMemberId(rechargeLog.getMemberId());
        response.setAmount(rechargeLog.getAmount());
        response.setBalanceBefore(rechargeLog.getBalanceBefore());
        response.setBalanceAfter(rechargeLog.getBalanceAfter());
        response.setPaymentMethod(rechargeLog.getPaymentMethod());
        response.setPaymentTransactionId(rechargeLog.getPaymentTransactionId());
        response.setStatus(rechargeLog.getStatus());
        response.setStaffId(rechargeLog.getStaffId());
        response.setSource(rechargeLog.getSource());
        response.setRemark(rechargeLog.getRemark());
        response.setCreatedAt(rechargeLog.getCreatedAt());
        response.setUpdatedAt(rechargeLog.getUpdatedAt());
        
        // 设置支付方式名称
        try {
            io.by.store.application.common.PaymentMethod paymentMethod = 
                io.by.store.application.common.PaymentMethod.fromCode(rechargeLog.getPaymentMethod());
            response.setPaymentMethodName(paymentMethod.getName());
        } catch (Exception e) {
            response.setPaymentMethodName(rechargeLog.getPaymentMethod());
        }
        
        // 设置状态名称
        try {
            io.by.store.application.common.RechargeStatus status = 
                io.by.store.application.common.RechargeStatus.fromCode(rechargeLog.getStatus());
            response.setStatusName(status.getName());
        } catch (Exception e) {
            response.setStatusName(rechargeLog.getStatus());
        }
        
        return response;
    }
}
