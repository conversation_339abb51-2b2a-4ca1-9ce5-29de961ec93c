package io.by.store.controller.request.mini;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 门店距离计算请求
 */
@Data
public class MiniDistanceCalculateRequest {

    /**
     * 用户经度
     */
//    @NotNull(message = "用户经度不能为空")
    private String userX;

    /**
     * 用户纬度
     */
//    @NotNull(message = "用户纬度不能为空")
    private String userY;

    /**
     * 门店ID列表
     */
//    @NotEmpty(message = "门店ID列表不能为空")
    private List<Long> storeIds;
}
