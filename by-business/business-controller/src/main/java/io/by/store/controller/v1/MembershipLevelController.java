package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.MembershipLevelService;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.MembershipLevelCreateRequest;
import io.by.store.controller.request.MembershipLevelQueryRequest;
import io.by.store.controller.request.MembershipLevelUpdateRequest;
import io.by.store.controller.response.MembershipLevelResponse;
import io.by.store.controller.response.MembershipLevelStatisticsResponse;
import io.by.store.infrastructure.entity.MembershipLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员等级控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/membership-levels")
@RequiredArgsConstructor
public class MembershipLevelController {

    private final MembershipLevelService membershipLevelService;

    /**
     * 创建会员等级
     */
    @PostMapping("/create")
    public ApiResponse<MembershipLevelResponse> createMembershipLevel(@Valid @RequestBody MembershipLevelCreateRequest request) {
        MembershipLevel level = new MembershipLevel();
        BeanUtils.copyProperties(request, level);

        boolean success = membershipLevelService.createMembershipLevel(level);
        if (success) {
            MembershipLevelResponse response = MembershipLevelResponse.from(level);
            return ApiResponse.success("会员等级创建成功", response);
        } else {
            return ApiResponse.error("会员等级创建失败");
        }
    }

    /**
     * 更新会员等级
     */
    @PostMapping("/update")
    public ApiResponse<MembershipLevelResponse> updateMembershipLevel(@Valid @RequestBody MembershipLevelUpdateRequest request) {
        MembershipLevel level = new MembershipLevel();
        BeanUtils.copyProperties(request, level);

        boolean success = membershipLevelService.updateMembershipLevel(level);
        if (success) {
            MembershipLevel updatedLevel = membershipLevelService.getMembershipLevelById(level.getId());
            MembershipLevelResponse response = MembershipLevelResponse.from(updatedLevel);
            return ApiResponse.success("会员等级更新成功", response);
        } else {
            return ApiResponse.error("会员等级更新失败");
        }
    }

    /**
     * 删除会员等级
     */
    @PostMapping("/delete/{id}")
    public ApiResponse<Void> deleteMembershipLevel(@PathVariable Long id) {
        boolean success = membershipLevelService.deleteMembershipLevel(id);
        if (success) {
            return ApiResponse.<Void>success("会员等级删除成功", null);
        } else {
            return ApiResponse.error("会员等级删除失败");
        }
    }

    /**
     * 根据ID查询会员等级
     */
    @GetMapping("/{id}")
    public ApiResponse<MembershipLevelResponse> getMembershipLevelById(@PathVariable Long id) {
        MembershipLevel level = membershipLevelService.getMembershipLevelById(id);
        if (level != null) {
            MembershipLevelResponse response = MembershipLevelResponse.from(level);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("会员等级不存在");
        }
    }

    /**
     * 根据等级标签查询会员等级
     */
    @GetMapping("/by-tag/{levelTag}")
    public ApiResponse<MembershipLevelResponse> getMembershipLevelByTag(@PathVariable String levelTag) {
        MembershipLevel level = membershipLevelService.getMembershipLevelByTag(levelTag);
        if (level != null) {
            MembershipLevelResponse response = MembershipLevelResponse.from(level);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("会员等级不存在");
        }
    }

    /**
     * 查询所有启用的会员等级
     */
    @GetMapping("/active")
    public ApiResponse<List<MembershipLevelResponse>> getAllActiveLevels() {
        List<MembershipLevel> levels = membershipLevelService.getAllActiveLevels();
        List<MembershipLevelResponse> responses = levels.stream()
                .map(MembershipLevelResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 分页查询会员等级
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<MembershipLevelResponse>> queryMembershipLevels(@Valid @RequestBody MembershipLevelQueryRequest request) {
        IPage<MembershipLevel> page = membershipLevelService.getMembershipLevelsPage(
                request.getCurrent(),
                request.getSize(),
                request.getLevelName(),
                request.getIsActive()
        );

        List<MembershipLevelResponse> responses = page.getRecords().stream()
                .map(MembershipLevelResponse::from)
                .collect(Collectors.toList());

        PageResponse<MembershipLevelResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 根据积分查询对应的会员等级
     */
    @GetMapping("/by-points/{points}")
    public ApiResponse<MembershipLevelResponse> getLevelByPoints(@PathVariable Long points) {
        MembershipLevel level = membershipLevelService.getLevelByPoints(points);
        if (level != null) {
            MembershipLevelResponse response = MembershipLevelResponse.from(level);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("未找到对应的会员等级");
        }
    }

    /**
     * 启用/禁用会员等级
     */
    @PostMapping("/toggle-status/{id}")
    public ApiResponse<Void> toggleMembershipLevelStatus(@PathVariable Long id) {
        boolean success = membershipLevelService.toggleMembershipLevelStatus(id);
        if (success) {
            return ApiResponse.<Void>success("会员等级状态更新成功", null);
        } else {
            return ApiResponse.error("会员等级状态更新失败");
        }
    }

    /**
     * 获取会员等级统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<MembershipLevelStatisticsResponse> getLevelStatistics() {
        MembershipLevelService.LevelStatistics statistics = membershipLevelService.getLevelStatistics();
        MembershipLevelStatisticsResponse response = MembershipLevelStatisticsResponse.from(statistics);
        return ApiResponse.success("会员等级统计信息", response);
    }

    /**
     * 计算升级到下一等级所需积分
     */
    @GetMapping("/points-to-next/{currentPoints}")
    public ApiResponse<Long> getPointsToNextLevel(@PathVariable Long currentPoints) {
        Long pointsNeeded = membershipLevelService.getPointsToNextLevel(currentPoints);
        return ApiResponse.success("升级所需积分", pointsNeeded);
    }
}
