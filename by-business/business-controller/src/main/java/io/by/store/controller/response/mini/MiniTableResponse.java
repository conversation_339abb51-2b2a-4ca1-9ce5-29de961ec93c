package io.by.store.controller.response.mini;

import io.by.store.infrastructure.entity.StoreTable;
import lombok.Data;

/**
 * 小程序桌台响应
 */
@Data
public class MiniTableResponse {

    /**
     * 桌台ID
     */
    private Long id;

    /**
     * 桌台编号
     */
    private String tableNumber;

    /**
     * 容量
     */
    private Integer capacity;

    /**
     * 状态
     */
    private String status;

    /**
     * 二维码URL
     */
    private String qrCode;

    /**
     * 从StoreTable实体转换
     */
    public static MiniTableResponse from(StoreTable table) {
        MiniTableResponse response = new MiniTableResponse();
        response.setId(table.getId());
        response.setTableNumber(table.getTableNumber());
        response.setCapacity(4); // 默认容量，实际可能需要从数据库获取
        response.setStatus(table.getIsActive() ? "AVAILABLE" : "UNAVAILABLE");
        response.setQrCode(table.getQrcodeUrl());
        return response;
    }
}
