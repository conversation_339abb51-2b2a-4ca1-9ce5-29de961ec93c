package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 查询员工请求
 */
@Data
public class StaffQueryRequest {
    
    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;
    
    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;
    
    /**
     * 门店ID
     */
    private Long storeId;
    
    /**
     * 用户名（模糊查询）
     */
    private String username;
    
    /**
     * 真实姓名（模糊查询）
     */
    private String fullName;
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 账户状态 (true: 启用, false: 禁用, null: 全部)
     */
    private Boolean isActive;
}
