package io.by.store.controller.v1.mini;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序私有接口控制器
 * 需要JWT认证才能访问的接口
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/mini/private")
@RequiredArgsConstructor
public class MiniPrivateController {


}
