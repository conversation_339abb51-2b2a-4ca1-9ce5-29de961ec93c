package io.by.store.controller.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 桌台使用记录查询请求
 */
@Data
public class TableUsageQueryRequest {

    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 桌台ID
     */
    private Long tableId;

    /**
     * 使用状态
     * ACTIVE: 进行中
     * COMPLETED: 已完成
     */
    @Pattern(regexp = "^(ACTIVE|COMPLETED|)$",
             message = "使用状态必须是: ACTIVE, COMPLETED")
    private String status;

    /**
     * 开始时间 (开桌时间范围查询)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间 (开桌时间范围查询)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
