package io.by.store.controller.response.mini;

import lombok.Data;

/**
 * 微信登录响应
 */
@Data
public class MiniWxLoginResponse {

    /**
     * 微信OpenID
     */
    private String openid;

    /**
     * 会话密钥
     */
    private String sessionKey;

    /**
     * 微信UnionID
     */
    private String unionid;

    /**
     * JWT Token
     */
    private String token;

    /**
     * 会员ID
     */
    private Long memberId;

    /**
     * 会员昵称
     */
    private String nickname;



    /**
     * 创建微信登录响应
     */
    public static MiniWxLoginResponse create(String openid, String sessionKey, String unionid, String token,
                                           Long memberId, String nickname) {
        MiniWxLoginResponse response = new MiniWxLoginResponse();
        response.setOpenid(openid);
        response.setSessionKey(sessionKey);
        response.setUnionid(unionid);
        response.setToken(token);
        response.setMemberId(memberId);
        response.setNickname(nickname);
        return response;
    }
}
