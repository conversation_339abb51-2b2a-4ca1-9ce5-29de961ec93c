package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 更新员工密码请求
 */
@Data
public class StaffPasswordUpdateRequest {
    
    /**
     * 员工ID
     */
    @NotNull(message = "员工ID不能为空")
    private Long staffId;
    
    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String newPassword;
}
