package io.by.store.controller.response;

import io.by.store.infrastructure.entity.OrderItem;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单商品项响应
 */
@Data
public class OrderItemResponse {

    /**
     * 订单商品项ID
     */
    private Long id;

    /**
     * 所属订单ID
     */
    private Long orderId;

    /**
     * 关联的商品ID
     */
    private Long productId;

    /**
     * 商品名称快照
     */
    private String productName;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 商品单价快照
     */
    private BigDecimal unitPrice;

    /**
     * 该项商品总价
     */
    private BigDecimal totalPrice;

    /**
     * 从实体类转换
     */
    public static OrderItemResponse from(OrderItem orderItem) {
        if (orderItem == null) {
            return null;
        }

        OrderItemResponse response = new OrderItemResponse();
        response.setId(orderItem.getId());
        response.setOrderId(orderItem.getOrderId());
        response.setProductId(orderItem.getProductId());
        response.setProductName(orderItem.getProductName());
        response.setQuantity(orderItem.getQuantity());
        response.setUnitPrice(orderItem.getUnitPrice());
        response.setTotalPrice(orderItem.getTotalPrice());

        return response;
    }
}
