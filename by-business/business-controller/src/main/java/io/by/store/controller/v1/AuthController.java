package io.by.store.controller.v1;

import io.by.store.application.AuthService;
import io.by.store.application.common.JwtUtil;
import io.by.store.application.common.UserContext;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.request.LoginRequest;
import io.by.store.controller.request.RefreshTokenRequest;
import io.by.store.controller.response.LoginResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 认证控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;
    private final JwtUtil jwtUtil;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        AuthService.LoginResult loginResult = authService.login(request.getUsername(), request.getPassword());
        LoginResponse response = LoginResponse.from(loginResult);
        return ApiResponse.success("登录成功", response);
    }

    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public ApiResponse<LoginResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        AuthService.LoginResult loginResult = authService.refreshToken(request.getRefreshToken());
        LoginResponse response = LoginResponse.from(loginResult);
        return ApiResponse.success("令牌刷新成功", response);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout() {
        // 从UserContext获取当前用户ID
        Long staffId = UserContext.getCurrentStaffId();
        if (staffId != null) {
            authService.logout(staffId);
        }
        return ApiResponse.<Void>success("登出成功", null);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public ApiResponse<CurrentUserResponse> getCurrentUser() {
        // 从UserContext获取当前用户信息
        UserContext.UserInfo userInfo = UserContext.getCurrentUser();
        if (userInfo == null) {
            return ApiResponse.error("未找到用户上下文信息");
        }

        CurrentUserResponse response = new CurrentUserResponse();
        response.setStaffId(userInfo.getStaffId());
        response.setUsername(userInfo.getUsername());
        response.setStoreId(userInfo.getStoreId());
        response.setRoleId(userInfo.getRoleId());

        return ApiResponse.success(response);
    }



    /**
     * 当前用户响应
     */
    @lombok.Data
    public static class CurrentUserResponse {
        private Long staffId;
        private String username;
        private Long storeId;
        private Long roleId;
    }
}
