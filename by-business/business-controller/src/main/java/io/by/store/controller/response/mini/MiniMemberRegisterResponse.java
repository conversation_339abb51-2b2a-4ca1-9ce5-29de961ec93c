package io.by.store.controller.response.mini;

import io.by.store.infrastructure.entity.Member;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 小程序会员注册响应
 */
@Data
public class MiniMemberRegisterResponse {

    /**
     * 会员业务ID
     */
    private String memberId;

    /**
     * 微信OpenID
     */
    private String wxOpenid;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像URL
     */
    private String avatarUrl;

    /**
     * 会员等级ID
     */
    private Long membershipLevelId;

    /**
     * 会员等级名称
     */
    private String membershipLevelName;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 会员积分
     */
    private Long points;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 注册类型（预注册/正式注册/融合注册）
     */
    private String registerType;

    /**
     * 从Member实体转换
     */
    public static MiniMemberRegisterResponse from(Member member, String registerType) {
        MiniMemberRegisterResponse response = new MiniMemberRegisterResponse();
        response.setMemberId(String.valueOf(member.getMemberId()));
        response.setWxOpenid(member.getWxOpenid());
        response.setPhoneNumber(member.getPhoneNumber());
        response.setNickname(member.getNickname());
        response.setAvatarUrl(member.getAvatarUrl());
        response.setMembershipLevelId(member.getMembershipLevelId());
        response.setMembershipLevelName(member.getMembershipLevelName());
        response.setBalance(member.getBalance());
        response.setPoints(member.getPoints());
        response.setCreatedAt(member.getCreatedAt());
        response.setRegisterType(registerType);
        return response;
    }
}
