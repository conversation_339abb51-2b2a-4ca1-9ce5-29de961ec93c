package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 创建加料请求
 */
@Data
public class ModifierCreateRequest {

    /**
     * 加料名称
     */
    @NotBlank(message = "加料名称不能为空")
    @Size(max = 50, message = "加料名称长度不能超过50个字符")
    private String name;

    /**
     * 价格变动 (通常为正数，表示加价)
     */
    @NotNull(message = "价格变动不能为空")
    @DecimalMin(value = "0.00", message = "价格变动不能为负数")
    @Digits(integer = 8, fraction = 2, message = "价格格式不正确，最多8位整数，2位小数")
    private BigDecimal priceChange;
}
