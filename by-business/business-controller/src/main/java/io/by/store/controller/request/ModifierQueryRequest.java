package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 查询加料请求
 */
@Data
public class ModifierQueryRequest {

    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 加料名称（模糊查询）
     */
    private String name;
}
