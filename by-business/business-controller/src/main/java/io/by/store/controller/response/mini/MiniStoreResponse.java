package io.by.store.controller.response.mini;

import io.by.store.infrastructure.entity.Store;
import lombok.Data;

/**
 * 小程序门店响应
 */
@Data
public class MiniStoreResponse {

    /**
     * 门店ID
     */
    private Long id;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 门店地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String phoneNumber;

    /**
     * 经度
     */
    private String x;

    /**
     * 纬度
     */
    private String y;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 距离（公里）
     */
    private Double distance;

    /**
     * 从Store实体转换
     */
    public static MiniStoreResponse from(Store store) {
        MiniStoreResponse response = new MiniStoreResponse();
        response.setId(store.getId());
        response.setName(store.getName());
        response.setAddress(store.getAddress());
        response.setPhoneNumber(store.getPhoneNumber());
        response.setX(store.getX());
        response.setY(store.getY());
        response.setIsActive(store.getIsActive());
        response.setDistance(null); // 距离需要单独计算
        return response;
    }

    /**
     * 从Store实体转换并设置距离
     */
    public static MiniStoreResponse from(Store store, Double distance) {
        MiniStoreResponse response = from(store);
        response.setDistance(distance);
        return response;
    }
}
