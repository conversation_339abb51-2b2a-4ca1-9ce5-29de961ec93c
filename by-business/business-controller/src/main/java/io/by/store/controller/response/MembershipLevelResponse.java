package io.by.store.controller.response;

import io.by.store.infrastructure.entity.MembershipLevel;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 会员等级响应
 */
@Data
public class MembershipLevelResponse {
    
    /**
     * 等级ID
     */
    private Long id;
    
    /**
     * 等级名称
     */
    private String levelName;
    
    /**
     * 等级标签/编码
     */
    private String levelTag;
    
    /**
     * 升级积分阈值
     */
    private Long upgradePointsThreshold;
    
    /**
     * 等级权益描述
     */
    private String description;
    
    /**
     * 等级图标URL
     */
    private String iconUrl;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 从实体类转换
     */
    public static MembershipLevelResponse from(MembershipLevel level) {
        if (level == null) {
            return null;
        }
        
        MembershipLevelResponse response = new MembershipLevelResponse();
        response.setId(level.getId());
        response.setLevelName(level.getLevelName());
        response.setLevelTag(level.getLevelTag());
        response.setUpgradePointsThreshold(level.getUpgradePointsThreshold());
        response.setDescription(level.getDescription());
        response.setIconUrl(level.getIconUrl());
        response.setIsActive(level.getIsActive());
        response.setCreatedAt(level.getCreatedAt());
        response.setUpdatedAt(level.getUpdatedAt());
        
        return response;
    }
}
