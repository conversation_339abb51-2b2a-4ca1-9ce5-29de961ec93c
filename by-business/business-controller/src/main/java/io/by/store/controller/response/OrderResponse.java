package io.by.store.controller.response;

import io.by.store.infrastructure.entity.Order;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单响应
 */
@Data
public class OrderResponse {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 所属门店ID
     */
    private Long storeId;

    /**
     * 关联会员ID
     */
    private Long memberId;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 关联桌台ID
     */
    private Long tableId;

    /**
     * 桌台号
     */
    private String tableNumber;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单类型描述
     */
    private String orderTypeDescription;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 订单状态描述
     */
    private String statusDescription;

    /**
     * 支付状态
     */
    private String paymentStatus;

    /**
     * 支付状态描述
     */
    private String paymentStatusDescription;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付方式描述
     */
    private String paymentMethodDescription;

    /**
     * 原始商品总金额
     */
    private BigDecimal originalAmount;

    /**
     * 优惠总金额
     */
    private BigDecimal discountAmount;

    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 订单备注
     */
    private String remark;

    private String getOrderNo;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 订单商品项列表 (可选，根据需要加载)
     */
    private List<OrderItemResponse> items;

    /**
     * 订单优惠明细列表 (可选，根据需要加载)
     */
    private List<OrderDiscountResponse> discounts;

    /**
     * 从实体类转换
     */
    public static OrderResponse from(Order order) {
        if (order == null) {
            return null;
        }

        OrderResponse response = new OrderResponse();
        response.setId(order.getId());
        response.setOrderNo(order.getOrderNo());
        response.setStoreId(order.getStoreId());
        response.setMemberId(order.getMemberId());
        response.setPhoneNumber(order.getPhoneNumber());
        response.setTableId(order.getTableId());
        response.setTableNumber(order.getTableNumber());
        response.setOrderType(order.getOrderType());
        response.setOrderTypeDescription(getOrderTypeDescription(order.getOrderType()));
        response.setStatus(order.getStatus());
        response.setStatusDescription(getStatusDescription(order.getStatus()));
        response.setPaymentStatus(order.getPaymentStatus());
        response.setPaymentStatusDescription(getPaymentStatusDescription(order.getPaymentStatus()));
        response.setPaymentMethod(order.getPaymentMethod());
        response.setPaymentMethodDescription(getPaymentMethodDescription(order.getPaymentMethod()));
        response.setOriginalAmount(order.getOriginalAmount());
        response.setDiscountAmount(order.getDiscountAmount());
        response.setPayableAmount(order.getPayableAmount());
        response.setRemark(order.getRemark());
        response.setGetOrderNo(order.getGetOrderNo());
        response.setCreatedAt(order.getCreatedAt());
        response.setUpdatedAt(order.getUpdatedAt());

        return response;
    }

    /**
     * 获取订单类型描述
     */
    private static String getOrderTypeDescription(String orderType) {
        if (orderType == null) return null;
        
        for (Order.OrderType type : Order.OrderType.values()) {
            if (type.getCode().equals(orderType)) {
                return type.getDescription();
            }
        }
        return orderType;
    }

    /**
     * 获取状态描述
     */
    private static String getStatusDescription(String status) {
        if (status == null) return null;
        
        for (Order.Status s : Order.Status.values()) {
            if (s.getCode().equals(status)) {
                return s.getDescription();
            }
        }
        return status;
    }

    /**
     * 获取支付状态描述
     */
    private static String getPaymentStatusDescription(String paymentStatus) {
        if (paymentStatus == null) return null;

        for (Order.PaymentStatus s : Order.PaymentStatus.values()) {
            if (s.getCode().equals(paymentStatus)) {
                return s.getDescription();
            }
        }
        return paymentStatus;
    }

    /**
     * 获取支付方式描述
     */
    private static String getPaymentMethodDescription(String paymentMethod) {
        if (paymentMethod == null) return null;

        try {
            io.by.store.application.common.PaymentMethod method =
                io.by.store.application.common.PaymentMethod.fromCode(paymentMethod);
            return method.getName();
        } catch (IllegalArgumentException e) {
            return paymentMethod;
        }
    }
}
