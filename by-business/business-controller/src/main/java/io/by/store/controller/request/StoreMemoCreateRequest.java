package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 创建备忘录请求
 */
@Data
public class StoreMemoCreateRequest {

    /**
     * 备忘录内容
     */
    @NotBlank(message = "备忘录内容不能为空")
    @Size(max = 1000, message = "备忘录内容不能超过1000个字符")
    private String content;

    /**
     * 状态（可选，默认为TODO）
     */
    private String status;
}
