package io.by.store.controller.response;

import io.by.store.application.StatisticsService;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 统计数据响应类
 */
@Data
public class StatisticsResponse {

    /**
     * 今日订单销售总额
     */
    private BigDecimal todaySalesAmount;

    /**
     * 销售额相比昨日增长百分比
     */
    private BigDecimal salesGrowthPercentage;

    /**
     * 今日订单数量
     */
    private Long todayOrderCount;

    /**
     * 订单数量相比昨日增长百分比
     */
    private BigDecimal orderGrowthPercentage;

    /**
     * 今日新增会员数
     */
    private Long todayNewMemberCount;

    /**
     * 新增会员数相比昨日增长百分比
     */
    private BigDecimal memberGrowthPercentage;

    /**
     * 从统计服务数据转换为响应对象
     */
    public static StatisticsResponse from(StatisticsService.DailyStatistics statistics) {
        StatisticsResponse response = new StatisticsResponse();
        response.setTodaySalesAmount(statistics.getTodaySalesAmount());
        response.setSalesGrowthPercentage(statistics.getSalesGrowthPercentage());
        response.setTodayOrderCount(statistics.getTodayOrderCount());
        response.setOrderGrowthPercentage(statistics.getOrderGrowthPercentage());
        response.setTodayNewMemberCount(statistics.getTodayNewMemberCount());
        response.setMemberGrowthPercentage(statistics.getMemberGrowthPercentage());
        return response;
    }
}
