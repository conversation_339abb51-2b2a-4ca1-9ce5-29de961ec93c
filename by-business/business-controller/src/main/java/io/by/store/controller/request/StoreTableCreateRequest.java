package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 创建门店桌台请求
 */
@Data
public class StoreTableCreateRequest {
    
    /**
     * 门店ID
     */
//    @NotNull(message = "门店ID不能为空")
    private Long storeId;
    
    /**
     * 桌台号
     */
    @NotBlank(message = "桌台号不能为空")
    @Size(max = 20, message = "桌台号长度不能超过20个字符")
    private String tableNumber;
    
    /**
     * 二维码URL
     */
    @Size(max = 500, message = "二维码URL长度不能超过500个字符")
    private String qrcodeUrl;
    
    /**
     * 是否激活：true-激活，false-停用
     */
    private Boolean isActive;
}
