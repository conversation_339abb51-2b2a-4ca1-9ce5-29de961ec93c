package io.by.store.controller.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建订单请求
 */
@Data
public class OrderCreateRequest {

    /**
     * 关联会员ID (可选)
     */
    @Positive(message = "会员ID必须为正数")
    private Long memberId;

    /**
     * 关联桌台ID (可选)
     */
    @Positive(message = "桌台ID必须为正数")
    private Long tableId;

    /**
     * 订单类型：DINE_IN (堂食), TAKE_AWAY (外带)
     */
    @NotBlank(message = "订单类型不能为空")
    @Pattern(regexp = "^(DINE_IN|TAKE_AWAY)$",
             message = "订单类型必须是: DINE_IN, TAKE_AWAY")
    private String orderType;

    /**
     * 支付方式：cash(现金支付), balance(余额支付), wechat_pay(微信支付), offline_scan(线下扫码)
     */
    @NotBlank(message = "支付方式不能为空")
    @Pattern(regexp = "^(cash|balance|wechat_pay|offline_scan)$",
             message = "支付方式必须是: cash, balance, wechat_pay, offline_scan")
    private String paymentMethod;

    /**
     * 订单备注
     */
    @Size(max = 255, message = "订单备注长度不能超过255个字符")
    private String remark;

    /**
     * 订单商品项列表
     */
    @NotEmpty(message = "订单商品项不能为空")
    @Valid
    private List<OrderItemCreateRequest> items;

    /**
     * 订单优惠明细列表 (可选)
     */
    @Valid
    private List<OrderDiscountCreateRequest> discounts;

    /**
     * 订单商品项创建请求
     */
    @Data
    public static class OrderItemCreateRequest {

        /**
         * 商品ID
         */
        @NotNull(message = "商品ID不能为空")
        @Positive(message = "商品ID必须为正数")
        private Long productId;

        /**
         * 购买数量
         */
        @NotNull(message = "购买数量不能为空")
        @Min(value = 1, message = "购买数量必须大于0")
        private Integer quantity;
    }

    /**
     * 订单优惠明细创建请求
     */
    @Data
    public static class OrderDiscountCreateRequest {

        /**
         * 优惠类型：COUPON(优惠券), MEMBER(会员折扣), MANUAL(手动改价), PROMOTION(营销活动)
         */
        @NotBlank(message = "优惠类型不能为空")
        @Pattern(regexp = "^(COUPON|MEMBER|MANUAL|PROMOTION)$", 
                 message = "优惠类型必须是: COUPON, MEMBER, MANUAL, PROMOTION")
        private String discountType;

        /**
         * 优惠描述
         */
        @NotBlank(message = "优惠描述不能为空")
        @Size(max = 100, message = "优惠描述长度不能超过100个字符")
        private String description;

        /**
         * 优惠金额
         */
        @NotNull(message = "优惠金额不能为空")
        @DecimalMin(value = "0.01", message = "优惠金额必须大于0")
        @Digits(integer = 8, fraction = 2, message = "金额格式不正确，最多8位整数，2位小数")
        private BigDecimal amount;

        /**
         * 可选的关联ID，如优惠券ID、活动ID
         */
        @Positive(message = "关联ID必须为正数")
        private Long referenceId;
    }
}
