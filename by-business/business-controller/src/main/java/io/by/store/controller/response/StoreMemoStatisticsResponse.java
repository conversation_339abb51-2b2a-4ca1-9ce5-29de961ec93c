package io.by.store.controller.response;

import io.by.store.application.StoreMemoService;
import lombok.Data;

/**
 * 备忘录统计响应
 */
@Data
public class StoreMemoStatisticsResponse {

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 待办数量
     */
    private Integer todoCount;

    /**
     * 已完成数量
     */
    private Integer doneCount;

    /**
     * 从统计信息转换
     */
    public static StoreMemoStatisticsResponse from(StoreMemoService.MemoStatistics statistics) {
        StoreMemoStatisticsResponse response = new StoreMemoStatisticsResponse();
        response.setTotalCount(statistics.getTotalCount());
        response.setTodoCount(statistics.getTodoCount());
        response.setDoneCount(statistics.getDoneCount());
        return response;
    }
}
