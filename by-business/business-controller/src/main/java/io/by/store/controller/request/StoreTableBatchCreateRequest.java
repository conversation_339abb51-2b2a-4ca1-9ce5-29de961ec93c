package io.by.store.controller.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量创建门店桌台请求
 */
@Data
public class StoreTableBatchCreateRequest {
    
    /**
     * 桌台列表
     */
    @NotEmpty(message = "桌台列表不能为空")
    @Size(max = 100, message = "单次最多创建100个桌台")
    @Valid
    private List<StoreTableCreateRequest> tables;
}
