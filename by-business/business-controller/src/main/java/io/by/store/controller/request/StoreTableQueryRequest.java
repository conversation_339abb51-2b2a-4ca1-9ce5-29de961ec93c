package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 查询门店桌台请求
 */
@Data
public class StoreTableQueryRequest {
    
    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;
    
    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;
    
    /**
     * 门店ID
     */
    private Long storeId;
    
    /**
     * 桌台号（模糊查询）
     */
    private String tableNumber;
    
    /**
     * 二维码URL
     */
    private String qrcodeUrl;
    
    /**
     * 是否激活：true-激活，false-停用，null-全部
     */
    private Boolean isActive;
}
