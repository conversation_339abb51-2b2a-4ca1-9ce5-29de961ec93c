package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 创建会员请求
 */
@Data
public class MemberCreateRequest {

    /**
     * 微信小程序用户的唯一标识
     */
    @Size(max = 100, message = "微信OpenID长度不能超过100个字符")
    private String wxOpenid;

    /**
     * 微信开放平台UnionID
     */
    @Size(max = 100, message = "微信UnionID长度不能超过100个字符")
    private String wxUnionid;

    /**
     * 手机号（可选，微信预注册时可为空）
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phoneNumber;

    /**
     * 用户昵称
     */
    @Size(max = 100, message = "昵称长度不能超过100个字符")
    private String nickname;

    /**
     * 用户头像URL
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    private String avatarUrl;

    /**
     * 会员等级ID（可选，不传时使用默认等级）
     */
    @Positive(message = "会员等级ID必须为正数")
    private Long membershipLevelId;

    /**
     * 会员等级名称（可选，用于显示，系统会根据membershipLevelId自动设置）
     */
    @Size(max = 50, message = "会员等级名称长度不能超过50个字符")
    private String membershipLevelName;

    /**
     * 初始余额（可选，默认为0）
     */
    @DecimalMin(value = "0.00", message = "初始余额不能为负数")
    @Digits(integer = 8, fraction = 2, message = "余额格式不正确，最多8位整数，2位小数")
    private BigDecimal balance;

    /**
     * 初始积分（可选，默认为0）
     */
    @Min(value = 0, message = "初始积分不能为负数")
    private Long points;
}
