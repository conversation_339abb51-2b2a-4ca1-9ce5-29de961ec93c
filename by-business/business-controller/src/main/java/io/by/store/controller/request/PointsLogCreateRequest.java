package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 创建积分变动记录请求
 */
@Data
public class PointsLogCreateRequest {
    
    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    @Positive(message = "会员ID必须为正数")
    private Long memberId;
    
    /**
     * 积分变动类型
     */
    @NotBlank(message = "积分变动类型不能为空")
    @Pattern(regexp = "^(earn_from_order|spend_on_order|earn_from_promo|refund_points|admin_adjustment|daily_checkin|birthday_bonus|referral_bonus|earn_from_recharge|points_expired)$",
             message = "积分变动类型必须是有效值")
    private String changeType;
    
    /**
     * 积分变动数量
     */
    @NotNull(message = "积分变动数量不能为空")
    private Long pointsChange;
    
    /**
     * 关联的订单ID（可选）
     */
    @Positive(message = "订单ID必须为正数")
    private Long orderId;
    
    /**
     * 变动描述
     */
    @Size(max = 500, message = "变动描述长度不能超过500个字符")
    private String description;
}
