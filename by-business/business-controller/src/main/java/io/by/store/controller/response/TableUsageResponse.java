package io.by.store.controller.response;

import io.by.store.infrastructure.entity.TableUsage;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 桌台使用记录响应
 */
@Data
public class TableUsageResponse {

    /**
     * 使用记录ID
     */
    private Long id;

    /**
     * 关联的桌台ID
     */
    private Long tableId;

    /**
     * 关联的订单ID
     */
    private String orderId;

    /**
     * 本次使用记录的状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 开桌时间
     */
    private LocalDateTime openedAt;

    /**
     * 关桌/结账时间
     */
    private LocalDateTime closedAt;

    /**
     * 使用时长(分钟)
     */
    private Integer durationMinutes;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 从实体类转换
     */
    public static TableUsageResponse from(TableUsage tableUsage) {
        if (tableUsage == null) {
            return null;
        }

        TableUsageResponse response = new TableUsageResponse();
        response.setId(tableUsage.getId());
        response.setTableId(tableUsage.getTableId());
        response.setOrderId(tableUsage.getOrderId());
        response.setStatus(tableUsage.getStatus());
        response.setStatusDescription(getStatusDescription(tableUsage.getStatus()));
        response.setOpenedAt(tableUsage.getOpenedAt());
        response.setClosedAt(tableUsage.getClosedAt());
        response.setDurationMinutes(tableUsage.getDurationMinutes());
        response.setStoreId(tableUsage.getStoreId());
        response.setCreatedAt(tableUsage.getCreatedAt());
        response.setUpdatedAt(tableUsage.getUpdatedAt());

        return response;
    }

    /**
     * 获取状态描述
     */
    private static String getStatusDescription(String status) {
        if (status == null) return null;

        for (TableUsage.Status s : TableUsage.Status.values()) {
            if (s.getCode().equals(status)) {
                return s.getDescription();
            }
        }
        return status;
    }
}
