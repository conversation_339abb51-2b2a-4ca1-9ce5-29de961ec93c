package io.by.store.controller.response;

import io.by.store.infrastructure.entity.Member;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员响应
 */
@Data
public class MemberResponse {

    /**
     * 会员业务ID
     */
    private String memberId;

    /**
     * 微信小程序用户的唯一标识
     */
    private String wxOpenid;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像URL
     */
    private String avatarUrl;

    /**
     * 会员等级ID
     */
    private Long membershipLevelId;

    /**
     * 会员等级名称
     */
    private String membershipLevelName;


    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 会员积分
     */
    private Long points;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 从实体类转换
     */
    public static MemberResponse from(Member member) {
        if (member == null) {
            return null;
        }

        MemberResponse response = new MemberResponse();
        response.setMemberId(String.valueOf(member.getMemberId()));
        response.setWxOpenid(member.getWxOpenid());
        response.setPhoneNumber(member.getPhoneNumber());
        response.setNickname(member.getNickname());
        response.setAvatarUrl(member.getAvatarUrl());
        response.setMembershipLevelId(member.getMembershipLevelId());
        response.setMembershipLevelName(member.getMembershipLevelName());
        response.setBalance(member.getBalance());
        response.setPoints(member.getPoints());
        response.setCreatedAt(member.getCreatedAt());
        response.setUpdatedAt(member.getUpdatedAt());

        return response;
    }
}
