package io.by.store.controller.response;

import io.by.store.application.AuthService;
import lombok.Data;

/**
 * 登录响应
 */
@Data
public class LoginResponse {
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";
    
    /**
     * 过期时间（秒）
     */
    private Long expiresIn;
    
    /**
     * 员工ID
     */
    private Long staffId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String fullName;
    
    /**
     * 门店ID
     */
    private Long storeId;
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 从登录结果转换
     */
    public static LoginResponse from(AuthService.LoginResult loginResult) {
        if (loginResult == null) {
            return null;
        }
        
        LoginResponse response = new LoginResponse();
        response.setAccessToken(loginResult.getAccessToken());
        response.setRefreshToken(loginResult.getRefreshToken());
        response.setExpiresIn(loginResult.getExpiresIn());
        response.setStaffId(loginResult.getStaffId());
        response.setUsername(loginResult.getUsername());
        response.setFullName(loginResult.getFullName());
        response.setStoreId(loginResult.getStoreId());
        response.setRoleId(loginResult.getRoleId());
        
        return response;
    }
}
