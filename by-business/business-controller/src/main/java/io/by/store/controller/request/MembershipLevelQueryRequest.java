package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 查询会员等级请求
 */
@Data
public class MembershipLevelQueryRequest {
    
    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;
    
    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;
    
    /**
     * 等级名称（模糊查询）
     */
    private String levelName;
    
    /**
     * 等级标签
     */
    private String levelTag;
    
    /**
     * 是否启用（null表示查询全部）
     */
    private Boolean isActive;
    
    /**
     * 最小积分阈值
     */
    @Min(value = 0, message = "最小积分阈值不能为负数")
    private Long minPoints;
    
    /**
     * 最大积分阈值
     */
    @Min(value = 0, message = "最大积分阈值不能为负数")
    private Long maxPoints;
}
