package io.by.store.controller.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.by.store.application.common.JwtUtil;
import io.by.store.application.common.UserContext;
import io.by.store.controller.common.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 小程序JWT认证过滤器
 * 只拦截 /api/v1/mini/private 下的接口
 */
@Slf4j
@Component
@Order(2) // 设置为2，确保在AdminJwtAuthenticationFilter之后执行
@RequiredArgsConstructor
public class MiniJwtAuthFilter implements Filter {

    private final JwtUtil jwtUtil;

    /**
     * 需要拦截的路径前缀
     */
    private static final String MINI_PRIVATE_PATH_PREFIX = "/api/v1/mini/private";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String requestURI = httpRequest.getRequestURI();
        String method = httpRequest.getMethod();

        try {
            // 只拦截小程序私有接口
            if (!shouldInterceptRequest(requestURI, method)) {
                chain.doFilter(request, response);
                return;
            }

            log.debug("小程序JWT认证过滤器拦截请求: {}", requestURI);

            // 提取令牌
            String token = extractTokenFromRequest(httpRequest);
            if (StrUtil.isBlank(token)) {
                sendUnauthorizedResponse(httpResponse, "访问令牌缺失");
                return;
            }

            // 验证令牌
            if (!validateMiniToken(token)) {
                sendUnauthorizedResponse(httpResponse, "访问令牌无效或已过期");
                return;
            }

            // 设置小程序用户上下文信息
            setMiniUserContext(token);

            // 继续处理请求
            chain.doFilter(request, response);
        } finally {
            // 请求完成后清理用户上下文
            UserContext.clear();
        }
    }

    /**
     * 判断是否需要拦截请求
     */
    private boolean shouldInterceptRequest(String requestURI, String method) {
        // OPTIONS请求跳过认证
        if ("OPTIONS".equalsIgnoreCase(method)) {
            return false;
        }

        // 只拦截小程序私有接口
        return requestURI.startsWith(MINI_PRIVATE_PATH_PREFIX);
    }

    /**
     * 从请求中提取令牌
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 从Header中获取
        String bearerToken = request.getHeader("Authorization");
        if (StrUtil.isNotBlank(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }

        // 从参数中获取（备用方案）
        String tokenParam = request.getParameter("token");
        if (StrUtil.isNotBlank(tokenParam)) {
            return tokenParam;
        }

        return null;
    }

    /**
     * 验证小程序令牌
     */
    private boolean validateMiniToken(String token) {
        if (StrUtil.isBlank(token)) {
            return false;
        }

        // 验证令牌格式和签名
        if (!jwtUtil.validateToken(token)) {
            return false;
        }

        // 检查令牌类型
        String tokenType = jwtUtil.getTokenTypeFromToken(token);
        if (!"mini_access".equals(tokenType)) {
            log.warn("令牌类型不正确，期望: mini_access, 实际: {}", tokenType);
            return false;
        }

        // 检查必要的字段
        String openId = jwtUtil.getOpenIdFromToken(token);
        Long memberId = jwtUtil.getMemberIdFromToken(token);
        
        if (StrUtil.isBlank(openId) || memberId == null) {
            log.warn("令牌中缺少必要字段: openId={}, memberId={}", openId, memberId);
            return false;
        }

        return true;
    }

    /**
     * 设置小程序用户上下文信息
     */
    private void setMiniUserContext(String token) {
        try {
            String openId = jwtUtil.getOpenIdFromToken(token);
            Long memberId = jwtUtil.getMemberIdFromToken(token);

            // 创建用户信息对象
            UserContext.UserInfo userInfo = new UserContext.UserInfo();
            userInfo.setOpenId(openId);
            userInfo.setMemberId(memberId);
            userInfo.setToken(token);

            // 设置到ThreadLocal中
            UserContext.setCurrentUser(userInfo);

            log.debug("小程序用户上下文设置成功: openId={}, memberId={}", openId, memberId);
        } catch (Exception e) {
            log.warn("设置小程序用户上下文失败: {}", e.getMessage());
            // 设置失败时清理上下文
            UserContext.clear();
        }
    }

    /**
     * 发送未授权响应
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        ApiResponse<Void> apiResponse = ApiResponse.error(401, message);
        String jsonResponse = JSONUtil.toJsonStr(apiResponse);

        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("小程序JWT认证过滤器初始化完成");
    }

    @Override
    public void destroy() {
        log.info("小程序JWT认证过滤器销毁");
    }
}
