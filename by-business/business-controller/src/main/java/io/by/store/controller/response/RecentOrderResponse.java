package io.by.store.controller.response;

import io.by.store.application.StatisticsService;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 最近订单响应类
 */
@Data
public class RecentOrderResponse {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 订单商品项列表
     */
    private List<OrderItemInfo> items;

    /**
     * 订单商品项信息
     */
    @Data
    public static class OrderItemInfo {
        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品数量
         */
        private Integer quantity;
    }

    /**
     * 从Service对象转换为Response对象
     */
    public static RecentOrderResponse from(StatisticsService.RecentOrderInfo orderInfo) {
        RecentOrderResponse response = new RecentOrderResponse();
        response.setOrderNo(orderInfo.getOrderNo());
        response.setTotalAmount(orderInfo.getTotalAmount());
        
        List<OrderItemInfo> items = orderInfo.getItems().stream()
                .map(item -> {
                    OrderItemInfo itemInfo = new OrderItemInfo();
                    itemInfo.setProductName(item.getProductName());
                    itemInfo.setQuantity(item.getQuantity());
                    return itemInfo;
                })
                .collect(Collectors.toList());
        
        response.setItems(items);
        return response;
    }

    /**
     * 批量转换
     */
    public static List<RecentOrderResponse> fromList(List<StatisticsService.RecentOrderInfo> orderInfos) {
        return orderInfos.stream()
                .map(RecentOrderResponse::from)
                .collect(Collectors.toList());
    }
}
