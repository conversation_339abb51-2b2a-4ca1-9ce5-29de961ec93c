package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 创建消费记录请求
 */
@Data
public class ConsumptionLogCreateRequest {
    
    /**
     * 消费会员ID
     */
    @NotNull(message = "会员ID不能为空")
    @Positive(message = "会员ID必须为正数")
    private Long memberId;
    
    /**
     * 关联的订单ID
     */
    @NotNull(message = "订单ID不能为空")
    @Positive(message = "订单ID必须为正数")
    private Long orderId;
    
    /**
     * 消费金额
     */
    @NotNull(message = "消费金额不能为空")
    @DecimalMin(value = "0.01", message = "消费金额必须大于0")
    @Digits(integer = 8, fraction = 2, message = "金额格式不正确，最多8位整数，2位小数")
    private BigDecimal amountConsumed;
}
