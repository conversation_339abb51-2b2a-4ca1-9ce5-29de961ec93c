package io.by.store.controller.response;

import io.by.store.infrastructure.entity.StoreTable;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门店桌台响应
 */
@Data
public class StoreTableResponse {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 桌台号
     */
    private String tableNumber;
    
    /**
     * 二维码URL
     */
    private String qrcodeUrl;
    
    /**
     * 是否激活：true-激活，false-停用
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 是否开桌
     */
    private Boolean isOpen;

    /**
     * 当前订单ID（如果开桌）
     */
    private String currentOrderId;

    /**
     * 开桌时间（如果开桌）
     */
    private LocalDateTime openedAt;

    /**
     * 当前使用时长（分钟，如果开桌）
     */
    private Integer currentDurationMinutes;
    
    /**
     * 从实体类转换
     */
    public static StoreTableResponse from(StoreTable storeTable) {
        if (storeTable == null) {
            return null;
        }
        
        StoreTableResponse response = new StoreTableResponse();
        response.setId(storeTable.getId());
        response.setTableNumber(storeTable.getTableNumber());
        response.setQrcodeUrl(storeTable.getQrcodeUrl());
        response.setIsActive(storeTable.getIsActive());
        response.setCreatedAt(storeTable.getCreatedAt());
        response.setUpdatedAt(storeTable.getUpdatedAt());
        response.setStoreId(storeTable.getStoreId());
        
        return response;
    }
}
