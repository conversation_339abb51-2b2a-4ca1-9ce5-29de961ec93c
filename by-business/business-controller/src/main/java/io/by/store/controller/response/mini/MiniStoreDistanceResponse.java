package io.by.store.controller.response.mini;

import lombok.Data;

/**
 * 门店距离响应
 */
@Data
public class MiniStoreDistanceResponse {

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 距离
     */
    private Double distance;

    /**
     * 距离单位
     */
    private String unit;

    /**
     * 创建门店距离响应
     */
    public static MiniStoreDistanceResponse create(Long storeId, String storeName, Double distance) {
        MiniStoreDistanceResponse response = new MiniStoreDistanceResponse();
        response.setStoreId(storeId);
        response.setStoreName(storeName);
        response.setDistance(Math.round(distance * 100.0) / 100.0); // 保留两位小数
        response.setUnit("km");
        return response;
    }
}
