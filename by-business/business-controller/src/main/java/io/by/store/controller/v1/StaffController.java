package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.StaffService;
import io.by.store.controller.request.StaffCreateRequest;
import io.by.store.controller.request.StaffPasswordUpdateRequest;
import io.by.store.controller.request.StaffQueryRequest;
import io.by.store.controller.request.StaffUpdateRequest;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.response.StaffResponse;
import io.by.store.infrastructure.entity.Staff;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 员工控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/staff")
@RequiredArgsConstructor
public class StaffController {

    private final StaffService staffService;

    /**
     * 创建员工
     */
    @PostMapping("/create")
    public ApiResponse<StaffResponse> createStaff(@Valid @RequestBody StaffCreateRequest request) {
        Staff staff = new Staff();
        BeanUtils.copyProperties(request, staff);

        boolean success = staffService.createStaff(staff, request.getPassword());
        if (success) {
            StaffResponse response = StaffResponse.from(staff);
            return ApiResponse.success("员工创建成功", response);
        } else {
            return ApiResponse.error("员工创建失败");
        }
    }

    /**
     * 更新员工信息
     */
    @PostMapping("/update")
    public ApiResponse<StaffResponse> updateStaff(@Valid @RequestBody StaffUpdateRequest request) {
        Staff staff = new Staff();
        BeanUtils.copyProperties(request, staff);

        boolean success = staffService.updateStaff(staff);
        if (success) {
            Staff updatedStaff = staffService.getStaffById(staff.getId());
            StaffResponse response = StaffResponse.from(updatedStaff);
            return ApiResponse.success("员工信息更新成功", response);
        } else {
            return ApiResponse.error("员工信息更新失败");
        }
    }

    /**
     * 更新员工密码
     */
    @PostMapping("/update-password")
    public ApiResponse<Void> updateStaffPassword(@Valid @RequestBody StaffPasswordUpdateRequest request) {
        boolean success = staffService.updateStaffPassword(request.getStaffId(), request.getNewPassword());
        if (success) {
            return ApiResponse.<Void>success("密码更新成功", null);
        } else {
            return ApiResponse.error("密码更新失败");
        }
    }

    /**
     * 删除员工
     */
    @PostMapping("/delete/{id}")
    public ApiResponse<Void> deleteStaff(@PathVariable Long id) {
        boolean success = staffService.deleteStaff(id);
        if (success) {
            return ApiResponse.<Void>success("员工删除成功", null);
        } else {
            return ApiResponse.error("员工删除失败");
        }
    }

    /**
     * 根据ID查询员工
     */
    @GetMapping("/{id}")
    public ApiResponse<StaffResponse> getStaffById(@PathVariable Long id) {
        Staff staff = staffService.getStaffById(id);
        if (staff != null) {
            StaffResponse response = StaffResponse.from(staff);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("员工不存在");
        }
    }

    /**
     * 根据用户名查询员工
     */
    @GetMapping("/by-username")
    public ApiResponse<StaffResponse> getStaffByUsername(@RequestParam String username) {
        Staff staff = staffService.getStaffByUsername(username);
        if (staff != null) {
            StaffResponse response = StaffResponse.from(staff);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("员工不存在");
        }
    }

    /**
     * 根据门店ID查询激活的员工列表
     */
    @GetMapping("/by-store/{storeId}")
    public ApiResponse<List<StaffResponse>> getActiveStaffByStoreId(@PathVariable Long storeId) {
        List<Staff> staffList = staffService.getActiveStaffByStoreId(storeId);
        List<StaffResponse> responses = staffList.stream()
                .map(StaffResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 分页查询员工
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<StaffResponse>> queryStaff(@Valid @RequestBody StaffQueryRequest request) {
        IPage<Staff> page = staffService.getStaffPage(
                request.getCurrent(),
                request.getSize(),
                request.getStoreId(),
                request.getUsername(),
                request.getFullName(),
                request.getRoleId(),
                request.getIsActive()
        );

        List<StaffResponse> responses = page.getRecords().stream()
                .map(StaffResponse::from)
                .collect(Collectors.toList());

        PageResponse<StaffResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }
}
