package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.ConsumptionLogService;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.ConsumptionLogCreateRequest;
import io.by.store.controller.request.ConsumptionLogQueryRequest;
import io.by.store.controller.response.ConsumptionLogResponse;
import io.by.store.infrastructure.entity.ConsumptionLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 消费记录控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/consumption-logs")
@RequiredArgsConstructor
public class ConsumptionLogController {

    private final ConsumptionLogService consumptionLogService;

    /**
     * 创建消费记录
     */
    @PostMapping("/create")
    public ApiResponse<ConsumptionLogResponse> createConsumptionLog(@Valid @RequestBody ConsumptionLogCreateRequest request) {
        ConsumptionLog consumptionLog = new ConsumptionLog();
        BeanUtils.copyProperties(request, consumptionLog);

        boolean success = consumptionLogService.createConsumptionLog(consumptionLog);
        if (success) {
            ConsumptionLogResponse response = ConsumptionLogResponse.from(consumptionLog);
            return ApiResponse.success("消费记录创建成功", response);
        } else {
            return ApiResponse.error("消费记录创建失败");
        }
    }

    /**
     * 根据ID查询消费记录
     */
    @GetMapping("/{id}")
    public ApiResponse<ConsumptionLogResponse> getConsumptionLogById(@PathVariable Long id) {
        ConsumptionLog consumptionLog = consumptionLogService.getConsumptionLogById(id);
        if (consumptionLog != null) {
            ConsumptionLogResponse response = ConsumptionLogResponse.from(consumptionLog);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("消费记录不存在");
        }
    }

    /**
     * 根据会员ID查询消费记录
     */
    @GetMapping("/by-member/{memberId}")
    public ApiResponse<List<ConsumptionLogResponse>> getConsumptionLogsByMemberId(@PathVariable Long memberId) {
        List<ConsumptionLog> consumptionLogs = consumptionLogService.getConsumptionLogsByMemberId(memberId);
        List<ConsumptionLogResponse> responses = consumptionLogs.stream()
                .map(ConsumptionLogResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 根据订单ID查询消费记录
     */
    @GetMapping("/by-order/{orderId}")
    public ApiResponse<ConsumptionLogResponse> getConsumptionLogByOrderId(@PathVariable Long orderId) {
        ConsumptionLog consumptionLog = consumptionLogService.getConsumptionLogByOrderId(orderId);
        if (consumptionLog != null) {
            ConsumptionLogResponse response = ConsumptionLogResponse.from(consumptionLog);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("消费记录不存在");
        }
    }

    /**
     * 分页查询消费记录
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<ConsumptionLogResponse>> queryConsumptionLogs(@Valid @RequestBody ConsumptionLogQueryRequest request) {
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        
        // 解析时间参数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            if (request.getStartTime() != null) {
                startTime = LocalDateTime.parse(request.getStartTime(), formatter);
            }
            if (request.getEndTime() != null) {
                endTime = LocalDateTime.parse(request.getEndTime(), formatter);
            }
        } catch (Exception e) {
            return ApiResponse.error("时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }

        IPage<ConsumptionLog> page = consumptionLogService.getConsumptionLogsPage(
                request.getCurrent(),
                request.getSize(),
                request.getMemberId(),
                request.getOrderId(),
                startTime,
                endTime
        );

        List<ConsumptionLogResponse> responses = page.getRecords().stream()
                .map(ConsumptionLogResponse::from)
                .collect(Collectors.toList());

        PageResponse<ConsumptionLogResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 获取消费统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<ConsumptionLogService.ConsumptionStatistics> getConsumptionStatistics() {
        ConsumptionLogService.ConsumptionStatistics statistics = consumptionLogService.getConsumptionStatistics();
        return ApiResponse.success("消费统计信息", statistics);
    }

    /**
     * 获取会员消费统计
     */
    @GetMapping("/member-statistics/{memberId}")
    public ApiResponse<ConsumptionLogService.MemberConsumptionStatistics> getMemberConsumptionStatistics(@PathVariable Long memberId) {
        ConsumptionLogService.MemberConsumptionStatistics statistics = 
                consumptionLogService.getMemberConsumptionStatistics(memberId);
        return ApiResponse.success("会员消费统计", statistics);
    }
}
