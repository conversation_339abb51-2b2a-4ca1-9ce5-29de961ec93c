package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 充值失败请求
 */
@Data
public class RechargeFailRequest {
    
    /**
     * 充值订单号
     */
    @NotBlank(message = "充值订单号不能为空")
    @Size(max = 32, message = "订单号长度不能超过32个字符")
    private String orderNumber;
    
    /**
     * 失败原因
     */
    @NotBlank(message = "失败原因不能为空")
    @Size(max = 200, message = "失败原因长度不能超过200个字符")
    private String reason;
}
