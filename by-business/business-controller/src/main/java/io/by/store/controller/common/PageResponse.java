package io.by.store.controller.common;

import lombok.Data;

import java.util.List;

/**
 * 分页响应数据
 */
@Data
public class PageResponse<T> {
    
    /**
     * 当前页码
     */
    private Long current;
    
    /**
     * 每页大小
     */
    private Long size;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Long pages;
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    public PageResponse() {
    }
    
    public PageResponse(Long current, Long size, Long total, Long pages, List<T> records) {
        this.current = current;
        this.size = size;
        this.total = total;
        this.pages = pages;
        this.records = records;
    }
    
    /**
     * 从MyBatis-Plus的IPage转换
     */
    public static <T> PageResponse<T> from(com.baomidou.mybatisplus.core.metadata.IPage<T> page) {
        return new PageResponse<>(
            page.getCurrent(),
            page.getSize(),
            page.getTotal(),
            page.getPages(),
            page.getRecords()
        );
    }
}
