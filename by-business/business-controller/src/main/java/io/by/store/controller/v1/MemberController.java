package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.MemberService;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.*;
import io.by.store.controller.response.MemberResponse;
import io.by.store.controller.response.MemberStatisticsResponse;
import io.by.store.infrastructure.entity.Member;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/members")
@RequiredArgsConstructor
public class MemberController {

    private final MemberService memberService;

    /**
     * 创建会员
     */
    @PostMapping("/create")
    public ApiResponse<MemberResponse> createMember(@Valid @RequestBody MemberCreateRequest request) {
        Member member = new Member();
        BeanUtils.copyProperties(request, member);

        boolean success = memberService.createMember(member);
        if (success) {
            MemberResponse response = MemberResponse.from(member);
            return ApiResponse.success("会员创建成功", response);
        } else {
            return ApiResponse.error("会员创建失败");
        }
    }

    /**
     * 更新会员信息
     */
    @PostMapping("/update")
    public ApiResponse<MemberResponse> updateMember(@Valid @RequestBody MemberUpdateRequest request) {
        Member member = new Member();
        BeanUtils.copyProperties(request, member);

        boolean success = memberService.updateMember(member);
        if (success) {
            Member updatedMember = memberService.getMemberById(member.getId());
            MemberResponse response = MemberResponse.from(updatedMember);
            return ApiResponse.success("会员信息更新成功", response);
        } else {
            return ApiResponse.error("会员信息更新失败");
        }
    }

    /**
     * 删除会员
     */
    @PostMapping("/delete/{id}")
    public ApiResponse<Void> deleteMember(@PathVariable Long id) {
        boolean success = memberService.deleteMember(id);
        if (success) {
            return ApiResponse.<Void>success("会员删除成功", null);
        } else {
            return ApiResponse.error("会员删除失败");
        }
    }

    /**
     * 根据ID查询会员
     */
    @GetMapping("/{id}")
    public ApiResponse<MemberResponse> getMemberById(@PathVariable Long id) {
        Member member = memberService.getMemberById(id);
        if (member != null) {
            MemberResponse response = MemberResponse.from(member);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("会员不存在");
        }
    }

    /**
     * 根据会员业务ID查询会员
     */
    @GetMapping("/by-member-id/{memberId}")
    public ApiResponse<MemberResponse> getMemberByMemberId(@PathVariable Long memberId) {
        Member member = memberService.getMemberByMemberId(memberId);
        if (member != null) {
            MemberResponse response = MemberResponse.from(member);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("会员不存在");
        }
    }

    /**
     * 根据手机号查询会员
     */
    @GetMapping("/by-phone")
    public ApiResponse<MemberResponse> getMemberByPhoneNumber(@RequestParam String phoneNumber) {
        Member member = memberService.getMemberByPhoneNumber(phoneNumber);
        if (member != null) {
            MemberResponse response = MemberResponse.from(member);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("会员不存在");
        }
    }

    /**
     * 根据微信OpenID查询会员
     */
    @GetMapping("/by-wx-openid")
    public ApiResponse<MemberResponse> getMemberByWxOpenid(@RequestParam String wxOpenid) {
        Member member = memberService.getMemberByWxOpenid(wxOpenid);
        if (member != null) {
            MemberResponse response = MemberResponse.from(member);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("会员不存在");
        }
    }

    /**
     * 分页查询会员
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<MemberResponse>> queryMembers(@Valid @RequestBody MemberQueryRequest request) {
        IPage<Member> page = memberService.getMembersPage(
                request.getCurrent(),
                request.getSize(),
                request.getNickname(),
                request.getPhoneNumber(),
                request.getMembershipLevelId(),
                request.getMembershipLevelName()
        );

        List<MemberResponse> responses = page.getRecords().stream()
                .map(MemberResponse::from)
                .collect(Collectors.toList());

        PageResponse<MemberResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 更新会员余额
     */
    @PostMapping("/update-balance")
    public ApiResponse<Void> updateMemberBalance(@Valid @RequestBody MemberBalanceUpdateRequest request) {
        boolean success = memberService.updateMemberBalance(request.getMemberId(), request.getAmount());
        if (success) {
            return ApiResponse.<Void>success("余额更新成功", null);
        } else {
            return ApiResponse.error("余额更新失败");
        }
    }

    /**
     * 更新会员积分
     */
    @PostMapping("/update-points")
    public ApiResponse<Void> updateMemberPoints(@Valid @RequestBody MemberPointsUpdateRequest request) {
        boolean success = memberService.updateMemberPoints(request.getMemberId(), request.getPoints());
        if (success) {
            return ApiResponse.<Void>success("积分更新成功", null);
        } else {
            return ApiResponse.error("积分更新失败");
        }
    }

    /**
     * 获取会员统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<MemberStatisticsResponse> getMemberStatistics() {
        MemberService.MemberStatistics statistics = memberService.getMemberStatistics();
        MemberStatisticsResponse response = MemberStatisticsResponse.from(statistics);
        return ApiResponse.success("会员统计信息", response);
    }

    /**
     * 根据积分自动升级会员等级
     */
    @PostMapping("/upgrade-by-points/{id}")
    public ApiResponse<MemberResponse> upgradeByPoints(@PathVariable Long id) {
        boolean success = memberService.upgradeByPoints(id);
        if (success) {
            Member updatedMember = memberService.getMemberById(id);
            MemberResponse response = MemberResponse.from(updatedMember);
            return ApiResponse.success("会员等级升级成功", response);
        } else {
            return ApiResponse.error("会员等级升级失败");
        }
    }


}
