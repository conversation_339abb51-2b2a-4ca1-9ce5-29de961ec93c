package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.Positive;

/**
 * 查询会员请求
 */
@Data
public class MemberQueryRequest {
    
    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;
    
    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;
    
    /**
     * 昵称（模糊查询）
     */
    private String nickname;
    
    /**
     * 手机号（模糊查询）
     */
    private String phoneNumber;
    
    /**
     * 会员等级ID
     */
    @Positive(message = "会员等级ID必须为正数")
    private Long membershipLevelId;

    /**
     * 会员等级名称（模糊查询）
     */
    private String membershipLevelName;
    
    /**
     * 微信OpenID
     */
    private String wxOpenid;
    
    /**
     * 微信UnionID
     */
    private String wxUnionid;
}
