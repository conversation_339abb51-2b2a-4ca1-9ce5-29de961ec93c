package io.by.store.controller.response;

import io.by.store.application.RechargeLogService;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 充值统计响应
 */
@Data
public class RechargeStatisticsResponse {
    
    /**
     * 今日充值总金额
     */
    private BigDecimal todayAmount;
    
    /**
     * 本月充值总金额
     */
    private BigDecimal monthAmount;
    
    /**
     * 待处理充值数量
     */
    private Integer pendingCount;
    
    /**
     * 成功充值数量
     */
    private Integer successfulCount;
    
    /**
     * 从统计信息转换
     */
    public static RechargeStatisticsResponse from(RechargeLogService.RechargeStatistics statistics) {
        if (statistics == null) {
            return null;
        }
        
        RechargeStatisticsResponse response = new RechargeStatisticsResponse();
        response.setTodayAmount(statistics.getTodayAmount());
        response.setMonthAmount(statistics.getMonthAmount());
        response.setPendingCount(statistics.getPendingCount());
        response.setSuccessfulCount(statistics.getSuccessfulCount());
        
        return response;
    }
}
