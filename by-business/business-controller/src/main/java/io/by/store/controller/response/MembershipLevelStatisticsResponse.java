package io.by.store.controller.response;

import io.by.store.application.MembershipLevelService;
import lombok.Data;

/**
 * 会员等级统计响应
 */
@Data
public class MembershipLevelStatisticsResponse {
    
    /**
     * 启用的等级总数
     */
    private Integer totalActiveLevels;
    
    /**
     * 最高等级信息
     */
    private MembershipLevelResponse maxLevel;
    
    /**
     * 最低等级信息
     */
    private MembershipLevelResponse minLevel;
    
    /**
     * 从统计信息转换
     */
    public static MembershipLevelStatisticsResponse from(MembershipLevelService.LevelStatistics statistics) {
        if (statistics == null) {
            return null;
        }
        
        MembershipLevelStatisticsResponse response = new MembershipLevelStatisticsResponse();
        response.setTotalActiveLevels(statistics.getTotalActiveLevels());
        response.setMaxLevel(MembershipLevelResponse.from(statistics.getMaxLevel()));
        response.setMinLevel(MembershipLevelResponse.from(statistics.getMinLevel()));
        
        return response;
    }
}
