package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.OrderService;
import io.by.store.application.common.UserContext;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.*;
import io.by.store.controller.response.OrderDiscountResponse;
import io.by.store.controller.response.OrderItemResponse;
import io.by.store.controller.response.OrderResponse;
import io.by.store.infrastructure.entity.Order;
import io.by.store.infrastructure.entity.OrderItem;
import io.by.store.infrastructure.entity.OrderDiscount;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/orders")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;

    /**
     * 创建订单
     */
    @PostMapping("/create")
    public ApiResponse<OrderResponse> createOrder(@Valid @RequestBody OrderCreateRequest request) {
        // 1. 构建订单对象
        Order order = new Order();
        BeanUtils.copyProperties(request, order);
        order.setStoreId(UserContext.getCurrentStoreId());

        // 2. 构建订单商品项列表
        List<OrderItem> orderItems = new ArrayList<>();
        for (OrderCreateRequest.OrderItemCreateRequest itemRequest : request.getItems()) {
            OrderItem orderItem = new OrderItem();
            orderItem.setProductId(itemRequest.getProductId());
            orderItem.setQuantity(itemRequest.getQuantity());
            orderItems.add(orderItem);
        }

        // 3. 构建订单优惠明细列表
        List<OrderDiscount> orderDiscounts = new ArrayList<>();
        if (request.getDiscounts() != null && !request.getDiscounts().isEmpty()) {
            for (OrderCreateRequest.OrderDiscountCreateRequest discountRequest : request.getDiscounts()) {
                OrderDiscount orderDiscount = new OrderDiscount();
                BeanUtils.copyProperties(discountRequest, orderDiscount);
                orderDiscounts.add(orderDiscount);
            }
        }

        // 4. 创建订单
        boolean success = orderService.createOrder(order, orderItems, orderDiscounts);
        if (success) {
            OrderResponse response = OrderResponse.from(order);
            return ApiResponse.success("订单创建成功", response);
        } else {
            return ApiResponse.error("订单创建失败");
        }
    }

    /**
     * 更新订单状态
     */
    @PostMapping("/update-status")
    public ApiResponse<Void> updateOrderStatus(@Valid @RequestBody OrderStatusUpdateRequest request) {
        boolean success = orderService.updateOrderStatus(request.getOrderId(), request.getStatus());
        if (success) {
            return ApiResponse.success("订单状态更新成功", null);
        } else {
            return ApiResponse.error("订单状态更新失败");
        }
    }

    /**
     * 更新订单支付状态
     */
    @PostMapping("/update-payment-status")
    public ApiResponse<Void> updatePaymentStatus(@Valid @RequestBody OrderPaymentUpdateRequest request) {
        boolean success = orderService.updatePaymentStatus(request.getOrderId(), request.getPaymentStatus());
        if (success) {
            return ApiResponse.success("订单支付状态更新成功", null);
        } else {
            return ApiResponse.error("订单支付状态更新失败");
        }
    }

    /**
     * 根据ID查询订单详情
     */
    @GetMapping("/{id}")
    public ApiResponse<OrderResponse> getOrderById(@PathVariable Long id) {
        Order order = orderService.getOrderById(id);
        OrderResponse response = OrderResponse.from(order);

        // 查询订单商品项
        List<OrderItem> orderItems = orderService.getOrderItems(id);
        List<OrderItemResponse> itemResponses = orderItems.stream()
                .map(OrderItemResponse::from)
                .collect(Collectors.toList());
        response.setItems(itemResponses);

        // 查询订单优惠明细
        List<OrderDiscount> orderDiscounts = orderService.getOrderDiscounts(id);
        List<OrderDiscountResponse> discountResponses = orderDiscounts.stream()
                .map(OrderDiscountResponse::from)
                .collect(Collectors.toList());
        response.setDiscounts(discountResponses);

        return ApiResponse.success(response);
    }

    /**
     * 根据订单号查询订单详情
     */
    @GetMapping("/order-no/{orderNo}")
    public ApiResponse<OrderResponse> getOrderByOrderNo(@PathVariable String orderNo) {
        Order order = orderService.getOrderByOrderNo(orderNo);
        OrderResponse response = OrderResponse.from(order);

        // 查询订单商品项
        List<OrderItem> orderItems = orderService.getOrderItems(order.getId());
        List<OrderItemResponse> itemResponses = orderItems.stream()
                .map(OrderItemResponse::from)
                .collect(Collectors.toList());
        response.setItems(itemResponses);

        // 查询订单优惠明细
        List<OrderDiscount> orderDiscounts = orderService.getOrderDiscounts(order.getId());
        List<OrderDiscountResponse> discountResponses = orderDiscounts.stream()
                .map(OrderDiscountResponse::from)
                .collect(Collectors.toList());
        response.setDiscounts(discountResponses);

        return ApiResponse.success(response);
    }

    /**
     * 分页查询订单
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<OrderResponse>> queryOrders(@Valid @RequestBody OrderQueryRequest request) {
        IPage<Order> page = orderService.getOrdersPage(
                request.getCurrent(),
                request.getSize(),
                request.getOrderNo(),
                request.getMemberId(),
                request.getTableId(),
                request.getOrderType(),
                request.getStatus(),
                request.getPaymentStatus(),
                request.getPaymentMethod(),
                request.getStartTime(),
                request.getEndTime()
        );

        List<OrderResponse> responses = page.getRecords().stream()
                .map(OrderResponse::from)
                .collect(Collectors.toList());

        PageResponse<OrderResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 根据会员ID查询订单列表
     */
    @GetMapping("/member/{memberId}")
    public ApiResponse<List<OrderResponse>> getOrdersByMemberId(@PathVariable Long memberId) {
        List<Order> orders = orderService.getOrdersByMemberId(memberId);
        List<OrderResponse> responses = orders.stream()
                .map(OrderResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 根据桌台ID查询订单列表
     */
    @GetMapping("/table/{tableId}")
    public ApiResponse<List<OrderResponse>> getOrdersByTableId(@PathVariable Long tableId) {
        List<Order> orders = orderService.getOrdersByTableId(tableId);
        List<OrderResponse> responses = orders.stream()
                .map(OrderResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 根据状态查询订单列表
     */
    @GetMapping("/status/{status}")
    public ApiResponse<List<OrderResponse>> getOrdersByStatus(@PathVariable String status) {
        List<Order> orders = orderService.getOrdersByStatus(status);
        List<OrderResponse> responses = orders.stream()
                .map(OrderResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 删除订单
     */
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteOrder(@PathVariable Long id) {
        boolean success = orderService.deleteOrder(id);
        if (success) {
            return ApiResponse.success("订单删除成功", null);
        } else {
            return ApiResponse.error("订单删除失败");
        }
    }

    /**
     * 查询订单商品项
     */
    @GetMapping("/{orderId}/items")
    public ApiResponse<List<OrderItemResponse>> getOrderItems(@PathVariable Long orderId) {
        List<OrderItem> orderItems = orderService.getOrderItems(orderId);
        List<OrderItemResponse> responses = orderItems.stream()
                .map(OrderItemResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 查询订单优惠明细
     */
    @GetMapping("/{orderId}/discounts")
    public ApiResponse<List<OrderDiscountResponse>> getOrderDiscounts(@PathVariable Long orderId) {
        List<OrderDiscount> orderDiscounts = orderService.getOrderDiscounts(orderId);
        List<OrderDiscountResponse> responses = orderDiscounts.stream()
                .map(OrderDiscountResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }
}
