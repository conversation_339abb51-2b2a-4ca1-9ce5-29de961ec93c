package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 更新会员余额请求
 */
@Data
public class MemberBalanceUpdateRequest {
    
    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    @Positive(message = "会员ID必须为正数")
    private Long memberId;
    
    /**
     * 变动金额（正数为充值，负数为扣减）
     */
    @NotNull(message = "变动金额不能为空")
    @Digits(integer = 8, fraction = 2, message = "金额格式不正确，最多8位整数，2位小数")
    private BigDecimal amount;
    
    /**
     * 变动原因
     */
    @NotBlank(message = "变动原因不能为空")
    @Size(max = 200, message = "变动原因长度不能超过200个字符")
    private String reason;
}
