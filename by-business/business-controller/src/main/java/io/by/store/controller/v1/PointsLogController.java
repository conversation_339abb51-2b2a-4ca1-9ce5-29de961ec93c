package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.PointsLogService;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.PointsLogCreateRequest;
import io.by.store.controller.request.PointsLogQueryRequest;
import io.by.store.controller.response.PointsLogResponse;
import io.by.store.infrastructure.entity.PointsLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 积分记录控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/points-logs")
@RequiredArgsConstructor
public class PointsLogController {

    private final PointsLogService pointsLogService;

    /**
     * 创建积分记录
     */
    @PostMapping("/create")
    public ApiResponse<PointsLogResponse> createPointsLog(@Valid @RequestBody PointsLogCreateRequest request) {
        PointsLog pointsLog = new PointsLog();
        BeanUtils.copyProperties(request, pointsLog);

        boolean success = pointsLogService.createPointsLog(pointsLog);
        if (success) {
            PointsLogResponse response = PointsLogResponse.from(pointsLog);
            return ApiResponse.success("积分记录创建成功", response);
        } else {
            return ApiResponse.error("积分记录创建失败");
        }
    }

    /**
     * 根据ID查询积分记录
     */
    @GetMapping("/{id}")
    public ApiResponse<PointsLogResponse> getPointsLogById(@PathVariable Long id) {
        PointsLog pointsLog = pointsLogService.getPointsLogById(id);
        if (pointsLog != null) {
            PointsLogResponse response = PointsLogResponse.from(pointsLog);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("积分记录不存在");
        }
    }

    /**
     * 根据会员ID查询积分记录
     */
    @GetMapping("/by-member/{memberId}")
    public ApiResponse<List<PointsLogResponse>> getPointsLogsByMemberId(@PathVariable Long memberId) {
        List<PointsLog> pointsLogs = pointsLogService.getPointsLogsByMemberId(memberId);
        List<PointsLogResponse> responses = pointsLogs.stream()
                .map(PointsLogResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 根据变动类型查询积分记录
     */
    @GetMapping("/by-change-type/{changeType}")
    public ApiResponse<List<PointsLogResponse>> getPointsLogsByChangeType(@PathVariable String changeType) {
        List<PointsLog> pointsLogs = pointsLogService.getPointsLogsByChangeType(changeType);
        List<PointsLogResponse> responses = pointsLogs.stream()
                .map(PointsLogResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 根据订单ID查询积分记录
     */
    @GetMapping("/by-order/{orderId}")
    public ApiResponse<List<PointsLogResponse>> getPointsLogsByOrderId(@PathVariable Long orderId) {
        List<PointsLog> pointsLogs = pointsLogService.getPointsLogsByOrderId(orderId);
        List<PointsLogResponse> responses = pointsLogs.stream()
                .map(PointsLogResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 分页查询积分记录
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<PointsLogResponse>> queryPointsLogs(@Valid @RequestBody PointsLogQueryRequest request) {
        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        
        // 解析时间参数
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            if (request.getStartTime() != null) {
                startTime = LocalDateTime.parse(request.getStartTime(), formatter);
            }
            if (request.getEndTime() != null) {
                endTime = LocalDateTime.parse(request.getEndTime(), formatter);
            }
        } catch (Exception e) {
            return ApiResponse.error("时间格式错误，请使用 yyyy-MM-dd HH:mm:ss 格式");
        }

        IPage<PointsLog> page = pointsLogService.getPointsLogsPage(
                request.getCurrent(),
                request.getSize(),
                request.getMemberId(),
                request.getChangeType(),
                request.getOrderId(),
                startTime,
                endTime
        );

        List<PointsLogResponse> responses = page.getRecords().stream()
                .map(PointsLogResponse::from)
                .collect(Collectors.toList());

        PageResponse<PointsLogResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 获取积分统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<PointsLogService.PointsStatistics> getPointsStatistics() {
        PointsLogService.PointsStatistics statistics = pointsLogService.getPointsStatistics();
        return ApiResponse.success("积分统计信息", statistics);
    }

    /**
     * 获取会员积分统计
     */
    @GetMapping("/member-statistics/{memberId}")
    public ApiResponse<PointsLogService.MemberPointsStatistics> getMemberPointsStatistics(@NotNull(message = "会员ID不能为空") @PathVariable Long memberId) {
        PointsLogService.MemberPointsStatistics statistics = 
                pointsLogService.getMemberPointsStatistics(memberId);
        return ApiResponse.success("会员积分统计", statistics);
    }
}
