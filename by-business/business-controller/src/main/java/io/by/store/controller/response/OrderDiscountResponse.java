package io.by.store.controller.response;

import io.by.store.infrastructure.entity.OrderDiscount;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单优惠明细响应
 */
@Data
public class OrderDiscountResponse {

    /**
     * 订单优惠明细ID
     */
    private Long id;

    /**
     * 所属订单ID
     */
    private Long orderId;

    /**
     * 优惠类型
     */
    private String discountType;

    /**
     * 优惠类型描述
     */
    private String discountTypeDescription;

    /**
     * 优惠描述
     */
    private String description;

    /**
     * 该项优惠的金额
     */
    private BigDecimal amount;

    /**
     * 可选的关联ID
     */
    private Long referenceId;

    /**
     * 从实体类转换
     */
    public static OrderDiscountResponse from(OrderDiscount orderDiscount) {
        if (orderDiscount == null) {
            return null;
        }

        OrderDiscountResponse response = new OrderDiscountResponse();
        response.setId(orderDiscount.getId());
        response.setOrderId(orderDiscount.getOrderId());
        response.setDiscountType(orderDiscount.getDiscountType());
        response.setDiscountTypeDescription(getDiscountTypeDescription(orderDiscount.getDiscountType()));
        response.setDescription(orderDiscount.getDescription());
        response.setAmount(orderDiscount.getAmount());
        response.setReferenceId(orderDiscount.getReferenceId());

        return response;
    }

    /**
     * 获取优惠类型描述
     */
    private static String getDiscountTypeDescription(String discountType) {
        if (discountType == null) return null;
        
        for (OrderDiscount.DiscountType type : OrderDiscount.DiscountType.values()) {
            if (type.getCode().equals(discountType)) {
                return type.getDescription();
            }
        }
        return discountType;
    }
}
