package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 创建会员等级请求
 */
@Data
public class MembershipLevelCreateRequest {
    
    /**
     * 等级名称
     */
    @NotBlank(message = "等级名称不能为空")
    @Size(max = 50, message = "等级名称长度不能超过50个字符")
    private String levelName;
    
    /**
     * 等级标签/编码
     */
    @NotBlank(message = "等级标签不能为空")
    @Size(max = 20, message = "等级标签长度不能超过20个字符")
    @Pattern(regexp = "^[a-z_]+$", message = "等级标签只能包含小写字母和下划线")
    private String levelTag;
    
    /**
     * 升级积分阈值
     */
    @NotNull(message = "升级积分阈值不能为空")
    @Min(value = 0, message = "升级积分阈值不能为负数")
    private Long upgradePointsThreshold;
    
    /**
     * 等级权益描述
     */
    @Size(max = 1000, message = "等级描述长度不能超过1000个字符")
    private String description;
    
    /**
     * 等级图标URL
     */
    @Size(max = 500, message = "图标URL长度不能超过500个字符")
    private String iconUrl;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
}
