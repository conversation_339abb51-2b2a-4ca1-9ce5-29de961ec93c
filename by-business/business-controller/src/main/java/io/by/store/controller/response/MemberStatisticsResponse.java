package io.by.store.controller.response;

import io.by.store.application.MemberService;
import lombok.Data;

/**
 * 会员统计响应
 */
@Data
public class MemberStatisticsResponse {
    
    /**
     * 会员总数
     */
    private Integer totalMembers;
    
    /**
     * 最近注册会员数量
     */
    private Integer recentMembersCount;
    
    /**
     * 从统计信息转换
     */
    public static MemberStatisticsResponse from(MemberService.MemberStatistics statistics) {
        if (statistics == null) {
            return null;
        }
        
        MemberStatisticsResponse response = new MemberStatisticsResponse();
        response.setTotalMembers(statistics.getTotalMembers());
        response.setRecentMembersCount(statistics.getRecentMembersCount());
        
        return response;
    }
}
