package io.by.store.controller.request.mini;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 坐标距离计算请求
 */
@Data
public class MiniCoordinateDistanceRequest {

    /**
     * 第一个点的纬度
     */
    @NotNull(message = "第一个点的纬度不能为空")
    private Double lat1;

    /**
     * 第一个点的经度
     */
    @NotNull(message = "第一个点的经度不能为空")
    private Double lng1;

    /**
     * 第二个点的纬度
     */
    @NotNull(message = "第二个点的纬度不能为空")
    private Double lat2;

    /**
     * 第二个点的经度
     */
    @NotNull(message = "第二个点的经度不能为空")
    private Double lng2;
}
