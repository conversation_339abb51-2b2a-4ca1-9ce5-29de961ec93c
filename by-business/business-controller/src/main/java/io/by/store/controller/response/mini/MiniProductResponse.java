package io.by.store.controller.response.mini;

import io.by.store.infrastructure.vo.ProductWithCategoryVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 小程序商品响应
 */
@Data
public class MiniProductResponse {

    /**
     * 商品ID
     */
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 销售价格
     */
    private BigDecimal price;

    /**
     * 库存数量
     */
    private Integer stockQuantity;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品状态
     */
    private String status;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 从ProductWithCategoryVO转换
     */
    public static MiniProductResponse from(ProductWithCategoryVO product) {
        MiniProductResponse response = new MiniProductResponse();
        response.setId(product.getId());
        response.setName(product.getName());
        response.setDescription(product.getDescription());
        response.setImageUrl(product.getImageUrl());
        response.setPrice(product.getPrice());
        response.setStockQuantity(product.getStockQuantity());
        response.setProductCode(product.getProductCode());
        response.setStatus(product.getStatus());
        response.setCategoryName(product.getCategoryName());
        return response;
    }
}
