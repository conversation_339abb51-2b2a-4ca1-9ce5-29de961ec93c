package io.by.store.controller.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 桌台状态响应
 */
@Data
public class TableStatusResponse {

    /**
     * 桌台ID
     */
    private Long tableId;

    /**
     * 桌台号
     */
    private String tableNumber;

    /**
     * 是否开桌
     */
    private Boolean isOpen;

    /**
     * 当前订单ID（如果开桌）
     */
    private String currentOrderId;

    /**
     * 开桌时间（如果开桌）
     */
    private LocalDateTime openedAt;

    /**
     * 当前使用时长（分钟，如果开桌）
     */
    private Integer currentDurationMinutes;

    /**
     * 历史使用次数
     */
    private Integer totalUsageCount;

    /**
     * 平均使用时长（分钟）
     */
    private Double averageDurationMinutes;

    /**
     * 门店ID
     */
    private Long storeId;
}
