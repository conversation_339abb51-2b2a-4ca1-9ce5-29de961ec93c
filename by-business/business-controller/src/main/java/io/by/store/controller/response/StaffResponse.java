package io.by.store.controller.response;

import io.by.store.infrastructure.entity.Staff;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 员工响应
 */
@Data
public class StaffResponse {
    
    /**
     * 员工ID
     */
    private Long id;
    
    /**
     * 登录用户名
     */
    private String username;
    
    /**
     * 员工真实姓名
     */
    private String fullName;
    
    /**
     * 员工联系电话
     */
    private String phoneNumber;
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 所属门店ID
     */
    private Long storeId;
    
    /**
     * 账户状态
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 从实体类转换（不包含密码信息）
     */
    public static StaffResponse from(Staff staff) {
        if (staff == null) {
            return null;
        }
        
        StaffResponse response = new StaffResponse();
        response.setId(staff.getId());
        response.setUsername(staff.getUsername());
        response.setFullName(staff.getFullName());
        response.setPhoneNumber(staff.getPhoneNumber());
        response.setRoleId(staff.getRoleId());
        response.setStoreId(staff.getStoreId());
        response.setIsActive(staff.getIsActive());
        response.setCreatedAt(staff.getCreatedAt());
        response.setUpdatedAt(staff.getUpdatedAt());
        
        return response;
    }
}
