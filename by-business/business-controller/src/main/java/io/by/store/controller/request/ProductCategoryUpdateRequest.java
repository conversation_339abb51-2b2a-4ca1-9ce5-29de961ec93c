package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.PositiveOrZero;
import javax.validation.constraints.Size;

/**
 * 更新商品分类请求
 */
@Data
public class ProductCategoryUpdateRequest {

    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    @Positive(message = "分类ID必须为正数")
    private Long id;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 50, message = "分类名称长度不能超过50个字符")
    private String name;

    /**
     * 父分类ID (0表示顶级分类)
     */
    @NotNull(message = "父分类ID不能为空")
    @PositiveOrZero(message = "父分类ID必须为非负数")
    private Long parentId;

    /**
     * 排序权重，数字越小越靠前
     */
    @PositiveOrZero(message = "排序权重必须为非负数")
    private Integer sortOrder;
}
