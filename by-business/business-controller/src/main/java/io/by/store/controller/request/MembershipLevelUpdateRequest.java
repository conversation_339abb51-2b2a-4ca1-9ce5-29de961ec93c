package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 更新会员等级请求
 */
@Data
public class MembershipLevelUpdateRequest {
    
    /**
     * 等级ID
     */
    @NotNull(message = "等级ID不能为空")
    @Positive(message = "等级ID必须为正数")
    private Long id;
    
    /**
     * 等级名称
     */
    @Size(max = 50, message = "等级名称长度不能超过50个字符")
    private String levelName;
    
    /**
     * 等级标签/编码
     */
    @Size(max = 20, message = "等级标签长度不能超过20个字符")
    @Pattern(regexp = "^[a-z_]+$", message = "等级标签只能包含小写字母和下划线")
    private String levelTag;
    
    /**
     * 升级积分阈值
     */
    @Min(value = 0, message = "升级积分阈值不能为负数")
    private Long upgradePointsThreshold;
    
    /**
     * 等级权益描述
     */
    @Size(max = 1000, message = "等级描述长度不能超过1000个字符")
    private String description;
    
    /**
     * 等级图标URL
     */
    @Size(max = 500, message = "图标URL长度不能超过500个字符")
    private String iconUrl;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
}
