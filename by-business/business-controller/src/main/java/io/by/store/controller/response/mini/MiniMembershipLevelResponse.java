package io.by.store.controller.response.mini;

import io.by.store.infrastructure.entity.MembershipLevel;
import lombok.Data;

/**
 * 小程序会员等级响应
 */
@Data
public class MiniMembershipLevelResponse {

    /**
     * 等级ID
     */
    private Long id;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 等级标签
     */
    private String levelTag;

    /**
     * 升级积分阈值
     */
    private Long upgradePointsThreshold;

    /**
     * 等级描述
     */
    private String description;

    /**
     * 图标URL
     */
    private String iconUrl;

    /**
     * 是否激活
     */
    private Boolean isActive;

    /**
     * 从MembershipLevel实体转换
     */
    public static MiniMembershipLevelResponse from(MembershipLevel level) {
        MiniMembershipLevelResponse response = new MiniMembershipLevelResponse();
        response.setId(level.getId());
        response.setLevelName(level.getLevelName());
        response.setLevelTag(level.getLevelTag());
        response.setUpgradePointsThreshold(level.getUpgradePointsThreshold());
        response.setDescription(level.getDescription());
        response.setIconUrl(level.getIconUrl());
        response.setIsActive(level.getIsActive());
        return response;
    }
}
