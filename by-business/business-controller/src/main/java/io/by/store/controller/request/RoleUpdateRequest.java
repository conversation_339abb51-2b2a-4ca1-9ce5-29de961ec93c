package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 更新角色请求
 */
@Data
public class RoleUpdateRequest {
    
    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long id;
    
    /**
     * 角色名称 (例如: 店长, 收银员, 后厨), 必须唯一
     */
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String name;
    
    /**
     * 角色描述, 说明该角色的主要职责
     */
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    private String description;
    
    /**
     * 权限配置, 用于存储该角色拥有的操作权限码
     * 例如: ["order:create", "product:edit"]
     */
    private List<String> permissions;
}
