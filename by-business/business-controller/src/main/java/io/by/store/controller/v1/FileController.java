package io.by.store.controller.v1;

import io.by.store.application.common.UserContext;
import io.by.store.controller.common.ApiResponse;
import io.by.store.infrastructure.manager.QinManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件上传控制器
 * 演示七牛云文件上传功能
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/file")
@RequiredArgsConstructor
@ConditionalOnBean(QinManager.class)
public class FileController {

    private final QinManager qinManager;

    /**
     * 上传单个文件
     */
    @PostMapping("/upload")
    public ApiResponse<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file) {

        Map<String, Object> result = new HashMap<>();

        try {
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "上传文件不能为空");
                return ApiResponse.error("上传文件不能为空");
            }

            // 从当前登录用户获取门店ID
            long storeId = UserContext.getCurrentStoreId();
            String fileName = storeId + "/" + UUID.randomUUID().toString().replace("-", "");

            // 上传文件
            String fileUrl = qinManager.uploadFile(file.getBytes(), fileName);

            result.put("success", true);
            result.put("message", "文件上传成功");
            result.put("fileUrl", fileUrl);
            result.put("originalName", file.getOriginalFilename());
            result.put("size", file.getSize());

            log.info("文件上传成功: originalName={}, fileUrl={}", file.getOriginalFilename(), fileUrl);

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "文件上传失败: " + e.getMessage());
            return ApiResponse.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Map<String, Object>> deleteFile(@RequestParam("fileName") String fileName) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean success = qinManager.deleteFile(fileName);

            result.put("success", success);
            result.put("message", success ? "文件删除成功" : "文件删除失败");
            result.put("fileName", fileName);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "文件删除失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 检查文件是否存在
     */
    @GetMapping("/exists")
    public ResponseEntity<Map<String, Object>> fileExists(@RequestParam("fileName") String fileName) {
        Map<String, Object> result = new HashMap<>();

        try {
            boolean exists = qinManager.fileExists(fileName);

            result.put("success", true);
            result.put("exists", exists);
            result.put("fileName", fileName);

            if (exists) {
                result.put("fileUrl", qinManager.getFileUrl(fileName));
            }

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("检查文件存在性失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "检查文件存在性失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 获取上传凭证
     */
    @GetMapping("/upload-token")
    public ResponseEntity<Map<String, Object>> getUploadToken(
            @RequestParam(value = "fileName", required = false) String fileName) {

        Map<String, Object> result = new HashMap<>();

        try {
            String uploadToken = qinManager.generateUploadToken(fileName);

            result.put("success", true);
            result.put("uploadToken", uploadToken);
            result.put("fileName", fileName);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取上传凭证失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取上传凭证失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
