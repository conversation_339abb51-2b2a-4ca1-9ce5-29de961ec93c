package io.by.store.controller.v1;

import io.by.store.application.StatisticsService;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.response.StatisticsResponse;
import io.by.store.controller.response.RecentOrderResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 统计数据控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/statistics")
@RequiredArgsConstructor
public class StatisticsController {

    private final StatisticsService statisticsService;

    /**
     * 获取今日业务统计数据
     * 包含：今日订单销售总额、相比昨日比较百分比、今日订单数量总额、相比昨日百分比、今日新增会员数、相比昨日百分比
     */
    @GetMapping("/today")
    public ApiResponse<StatisticsResponse> getTodayStatistics() {
        try {
            StatisticsService.DailyStatistics statistics = statisticsService.getTodayStatistics();
            StatisticsResponse response = StatisticsResponse.from(statistics);
            return ApiResponse.success("今日统计数据", response);
        } catch (Exception e) {
            log.error("获取今日统计数据失败", e);
            return ApiResponse.error("获取统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近5笔订单详情
     * 包含：订单号、各商品名称数量及订单总金额
     */
    @GetMapping("/recent-orders")
    public ApiResponse<List<RecentOrderResponse>> getRecentOrders() {
        try {
            List<StatisticsService.RecentOrderInfo> recentOrders = statisticsService.getRecentOrders();
            List<RecentOrderResponse> response = RecentOrderResponse.fromList(recentOrders);
            return ApiResponse.success("最近5笔订单", response);
        } catch (Exception e) {
            log.error("获取最近订单失败", e);
            return ApiResponse.error("获取最近订单失败: " + e.getMessage());
        }
    }
}
