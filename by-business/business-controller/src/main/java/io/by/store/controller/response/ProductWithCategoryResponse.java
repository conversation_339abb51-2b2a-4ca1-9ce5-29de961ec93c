package io.by.store.controller.response;

import io.by.store.infrastructure.entity.Product;
import io.by.store.infrastructure.vo.ProductWithCategoryVO;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品与分类联表查询响应
 */
@Data
public class ProductWithCategoryResponse {

    /**
     * 商品ID
     */
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品的详细描述
     */
    private String description;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 所属分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 销售价格
     */
    private BigDecimal price;

    /**
     * 当前库存数量
     */
    private Integer stockQuantity;

    /**
     * 库存预警阈值
     */
    private Integer alertQuantity;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 状态 (PUBLISHED:已上架, ARCHIVED:已下架, SOLDOUT:售罄)
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDescription;

    /**
     * 所属门店ID
     */
    private Long storeId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否库存不足
     */
    private Boolean isLowStock;

    /**
     * 从VO转换为响应对象
     */
    public static ProductWithCategoryResponse from(ProductWithCategoryVO vo) {
        if (vo == null) {
            return null;
        }
        ProductWithCategoryResponse response = new ProductWithCategoryResponse();
        BeanUtils.copyProperties(vo, response);
        
        // 设置状态描述
        if (vo.getStatus() != null) {
            try {
                Product.Status status = Product.Status.valueOf(vo.getStatus());
                response.setStatusDescription(status.getDescription());
            } catch (IllegalArgumentException e) {
                response.setStatusDescription("未知状态");
            }
        }
        
        // 判断是否库存不足
        if (vo.getStockQuantity() != null && vo.getAlertQuantity() != null) {
            response.setIsLowStock(vo.getStockQuantity() <= vo.getAlertQuantity());
        }
        
        return response;
    }

    /**
     * 批量转换
     */
    public static List<ProductWithCategoryResponse> from(List<ProductWithCategoryVO> vos) {
        if (vos == null) {
            return null;
        }
        return vos.stream()
                .map(ProductWithCategoryResponse::from)
                .collect(Collectors.toList());
    }
}
