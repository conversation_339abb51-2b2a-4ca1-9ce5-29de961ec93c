package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 创建充值记录请求
 */
@Data
public class RechargeCreateRequest {
    
    /**
     * 充值会员ID
     */
    @NotNull(message = "会员ID不能为空")
    @Positive(message = "会员ID必须为正数")
    private Long memberId;
    
    /**
     * 充值金额
     */
    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0")
    @Digits(integer = 8, fraction = 2, message = "金额格式不正确，最多8位整数，2位小数")
    private BigDecimal amount;
    
    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    @Pattern(regexp = "^(cash|wechat_pay|offline_scan)$", message = "支付方式必须是: cash, wechat_pay, offline_scan")
    private String paymentMethod;
    
    /**
     * 第三方支付交易流水号（线上支付时必填）
     */
    @Size(max = 100, message = "交易流水号长度不能超过100个字符")
    private String paymentTransactionId;
    
    /**
     * 操作员工ID（线下支付时必填）
     */
    @Positive(message = "员工ID必须为正数")
    private Long staffId;

    /**
     * 来源 (offline、online)
     */
    @Pattern(regexp = "^(offline|online)$", message = "来源必须是: offline, online")
    private String source;

    /**
     * 备注信息
     */
    @Size(max = 200, message = "备注信息长度不能超过200个字符")
    private String remark;
}
