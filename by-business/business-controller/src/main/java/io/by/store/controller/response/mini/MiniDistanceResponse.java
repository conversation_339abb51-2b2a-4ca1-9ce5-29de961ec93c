package io.by.store.controller.response.mini;

import lombok.Data;

/**
 * 距离计算响应
 */
@Data
public class MiniDistanceResponse {

    /**
     * 距离
     */
    private Double distance;

    /**
     * 距离单位
     */
    private String unit;

    /**
     * 创建距离响应
     */
    public static MiniDistanceResponse create(Double distance) {
        MiniDistanceResponse response = new MiniDistanceResponse();
        response.setDistance(Math.round(distance * 100.0) / 100.0); // 保留两位小数
        response.setUnit("km");
        return response;
    }
}
