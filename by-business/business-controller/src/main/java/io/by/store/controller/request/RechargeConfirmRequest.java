package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 确认充值请求
 */
@Data
public class RechargeConfirmRequest {
    
    /**
     * 充值订单号
     */
    @NotBlank(message = "充值订单号不能为空")
    @Size(max = 32, message = "订单号长度不能超过32个字符")
    private String orderNumber;
    
    /**
     * 第三方支付交易流水号
     */
    @Size(max = 100, message = "交易流水号长度不能超过100个字符")
    private String transactionId;
    
    /**
     * 确认备注
     */
    @Size(max = 200, message = "备注长度不能超过200个字符")
    private String remark;
}
