package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.StoreMemoService;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.StoreMemoCreateRequest;
import io.by.store.controller.request.StoreMemoQueryRequest;
import io.by.store.controller.request.StoreMemoStatusUpdateRequest;
import io.by.store.controller.response.StoreMemoResponse;
import io.by.store.controller.response.StoreMemoStatisticsResponse;
import io.by.store.infrastructure.entity.StoreMemo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店备忘录控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/memos")
@RequiredArgsConstructor
public class StoreMemoController {

    private final StoreMemoService storeMemoService;

    /**
     * 创建备忘录
     */
    @PostMapping("/create")
    public ApiResponse<StoreMemoResponse> createMemo(@Valid @RequestBody StoreMemoCreateRequest request) {
        try {
            // 构建备忘录对象
            StoreMemo memo = new StoreMemo();
            BeanUtils.copyProperties(request, memo);

            // 创建备忘录
            boolean success = storeMemoService.createMemo(memo);
            if (success) {
                StoreMemoResponse response = StoreMemoResponse.from(memo);
                return ApiResponse.success("备忘录创建成功", response);
            } else {
                return ApiResponse.error("备忘录创建失败");
            }
        } catch (Exception e) {
            log.error("创建备忘录失败", e);
            return ApiResponse.error("创建备忘录失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询备忘录
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<StoreMemoResponse>> queryMemos(@Valid @RequestBody StoreMemoQueryRequest request) {
        try {
            IPage<StoreMemo> page = storeMemoService.getMemosPage(
                    request.getCurrent(),
                    request.getSize(),
                    request.getStoreId(),
                    request.getStatus(),
                    request.getCreatorId(),
                    request.getContent()
            );

            List<StoreMemoResponse> responses = page.getRecords().stream()
                    .map(StoreMemoResponse::from)
                    .collect(Collectors.toList());

            PageResponse<StoreMemoResponse> pageResponse = new PageResponse<>(
                    page.getCurrent(),
                    page.getSize(),
                    page.getTotal(),
                    page.getPages(),
                    responses
            );

            return ApiResponse.success("查询成功", pageResponse);
        } catch (Exception e) {
            log.error("查询备忘录失败", e);
            return ApiResponse.error("查询备忘录失败: " + e.getMessage());
        }
    }

    /**
     * 更新备忘录状态
     */
    @PostMapping("/update-status")
    public ApiResponse<String> updateMemoStatus(@Valid @RequestBody StoreMemoStatusUpdateRequest request) {
        try {
            boolean success;
            if (request.getIds().size() == 1) {
                // 单个更新
                success = storeMemoService.updateMemoStatus(request.getIds().get(0), request.getStatus());
            } else {
                // 批量更新
                success = storeMemoService.updateMemoStatusBatch(request.getIds(), request.getStatus());
            }

            if (success) {
                return ApiResponse.success("状态更新成功");
            } else {
                return ApiResponse.error("状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新备忘录状态失败", e);
            return ApiResponse.error("更新状态失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询备忘录
     */
    @GetMapping("/{id}")
    public ApiResponse<StoreMemoResponse> getMemoById(@PathVariable Long id) {
        try {
            StoreMemo memo = storeMemoService.getMemoById(id);
            StoreMemoResponse response = StoreMemoResponse.from(memo);
            return ApiResponse.success("查询成功", response);
        } catch (Exception e) {
            log.error("查询备忘录失败", e);
            return ApiResponse.error("查询备忘录失败: " + e.getMessage());
        }
    }

    /**
     * 删除备忘录
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteMemo(@PathVariable Long id) {
        try {
            boolean success = storeMemoService.deleteMemo(id);
            if (success) {
                return ApiResponse.success("删除成功");
            } else {
                return ApiResponse.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除备忘录失败", e);
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取备忘录统计信息
     */
    @GetMapping("/statistics")
    public ApiResponse<StoreMemoStatisticsResponse> getMemoStatistics(@RequestParam(required = false) Long storeId) {
        try {
            StoreMemoService.MemoStatistics statistics = storeMemoService.getMemoStatistics(storeId);
            StoreMemoStatisticsResponse response = StoreMemoStatisticsResponse.from(statistics);
            return ApiResponse.success("统计信息", response);
        } catch (Exception e) {
            log.error("获取备忘录统计信息失败", e);
            return ApiResponse.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据状态查询备忘录列表
     */
    @GetMapping("/by-status/{status}")
    public ApiResponse<List<StoreMemoResponse>> getMemosByStatus(@PathVariable String status) {
        try {
            List<StoreMemo> memos = storeMemoService.getMemosByStatus(status);
            List<StoreMemoResponse> responses = memos.stream()
                    .map(StoreMemoResponse::from)
                    .collect(Collectors.toList());
            return ApiResponse.success("查询成功", responses);
        } catch (Exception e) {
            log.error("根据状态查询备忘录失败", e);
            return ApiResponse.error("查询失败: " + e.getMessage());
        }
    }
}
