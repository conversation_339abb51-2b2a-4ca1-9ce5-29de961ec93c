package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 查询备忘录请求
 */
@Data
public class StoreMemoQueryRequest {

    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 门店ID（可选）
     */
    private Long storeId;

    /**
     * 状态（可选）
     */
    private String status;

    /**
     * 创建人ID（可选）
     */
    private Long creatorId;

    /**
     * 内容关键词（可选，模糊查询）
     */
    private String content;
}
