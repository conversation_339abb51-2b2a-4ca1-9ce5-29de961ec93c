package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;

/**
 * 订单状态更新请求
 */
@Data
public class OrderStatusUpdateRequest {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    @Positive(message = "订单ID必须为正数")
    private Long orderId;

    /**
     * 新状态
     * PENDING_PAYMENT: 待支付
     * PROCESSING: 制作中
     * COMPLETED: 已完成
     * CANCELLED: 已取消
     */
    @NotBlank(message = "订单状态不能为空")
    @Pattern(regexp = "^(PENDING_PAYMENT|PROCESSING|COMPLETED|CANCELLED)$", 
             message = "订单状态必须是: PENDING_PAYMENT, PROCESSING, COMPLETED, CANCELLED")
    private String status;
}
