package io.by.store.controller.v1.mini;

import cn.binarywang.wx.miniapp.api.WxMaCodeService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.*;
import io.by.store.application.util.DistanceUtil;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.mini.*;
import io.by.store.controller.response.mini.*;
import io.by.store.infrastructure.entity.*;
import io.by.store.infrastructure.vo.ProductWithCategoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 小程序公开接口控制器
 * 无需认证即可访问的接口
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/mini/public")
@RequiredArgsConstructor
public class MiniPublicController {

    private final StoreService storeService;
    private final ProductService productService;
    private final ProductCategoryService categoryService;
    private final StoreTableService storeTableService;
    private final MembershipLevelService membershipLevelService;

    private final WxMaService wxMaService;

    // ==================== 1. 用户认证模块 ====================

    /**
     * 微信登录 - 获取OpenID
     */
    @PostMapping("/auth/wx-login")
    public ApiResponse<MiniWxLoginResponse> wxLogin(@Valid @RequestBody MiniWxLoginRequest request) {
        try {

            WxMaUserService userService = wxMaService.getUserService();
            WxMaJscode2SessionResult sessionInfo = userService.getSessionInfo(request.getCode());
            String unionid = "mock_unionid";
            String token = "mock_jwt_token";


            MiniWxLoginResponse response = MiniWxLoginResponse.create(sessionInfo.getOpenid(), sessionInfo.getSessionKey(), unionid, token);

            log.info("微信登录成功，code: {}", request.getCode());
            return ApiResponse.success("登录成功", response);

        } catch (Exception e) {
            log.error("微信登录失败", e);
            return ApiResponse.error("微信登录失败: " + e.getMessage());
        }
    }

    // ==================== 2. 门店模块 ====================

    /**
     * 查询所有激活的门店
     */
    @PostMapping("/stores")
    public ApiResponse<List<MiniStoreResponse>> getStores(@Valid @RequestBody MiniDistanceCalculateRequest request) {
        try {
            List<Store> stores = storeService.getActiveStores();

            List<MiniStoreResponse> result = stores.stream()
                    .map(store -> {
                        double distance = 0;
                        try {
                            if (StrUtil.isAllNotBlank(request.getUserX(), request.getUserY())) {
                                distance = DistanceUtil.calculateDistance(
                                        Double.parseDouble(request.getUserY()),
                                        Double.parseDouble(request.getUserX()),
                                        Double.parseDouble(store.getY()),
                                        Double.parseDouble(store.getX())
                                );
                            }
                        } catch (Exception e) {
                            log.warn("计算门店距离失败，门店ID: {}", store.getId(), e);
                        }
                        return MiniStoreResponse.from(store, distance);
                    })
                    .collect(Collectors.toList());

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("查询门店列表失败", e);
            return ApiResponse.error("查询门店列表失败: " + e.getMessage());
        }
    }

    /**
     * 计算门店距离
     */
    @PostMapping("/stores/distance")
    public ApiResponse<List<MiniStoreDistanceResponse>> calculateStoreDistance(@Valid @RequestBody MiniDistanceCalculateRequest request) {
        try {
            double userLat = Double.parseDouble(request.getUserY());
            double userLng = Double.parseDouble(request.getUserX());

            List<MiniStoreDistanceResponse> result = request.getStoreIds().stream().map(storeId -> {
                try {
                    Store store = storeService.getStoreById(storeId);
                    double storeLat = Double.parseDouble(store.getY());
                    double storeLng = Double.parseDouble(store.getX());

                    // 计算距离（使用Haversine公式）
                    double distance = DistanceUtil.calculateDistance(userLat, userLng, storeLat, storeLng);

                    return MiniStoreDistanceResponse.create(store.getId(), store.getName(), distance);
                } catch (Exception e) {
                    log.warn("计算门店距离失败，门店ID: {}", storeId, e);
                    return null;
                }
            }).filter(item -> item != null).collect(Collectors.toList());

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("计算门店距离失败", e);
            return ApiResponse.error("计算门店距离失败: " + e.getMessage());
        }
    }


    // ==================== 3. 商品模块 ====================

    /**
     * 查询门店商品分类
     */
    @GetMapping("/stores/{storeId}/categories")
    public ApiResponse<List<MiniCategoryResponse>> getStoreCategories(
            @PathVariable Long storeId) {
        try {
            List<ProductCategory> categories = categoryService.getTopLevelCategories(storeId);

            List<MiniCategoryResponse> result = categories.stream().map(category -> {
                return MiniCategoryResponse.from(category, false);
            }).collect(Collectors.toList());

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("查询门店商品分类失败，门店ID: {}", storeId, e);
            return ApiResponse.error("查询商品分类失败: " + e.getMessage());
        }
    }

    /**
     * 通过分类查询商品（分页）
     */
    @GetMapping("/stores/{storeId}/categories/{categoryId}/products")
    public ApiResponse<PageResponse<MiniProductResponse>> getProductsByCategory(
            @PathVariable Long storeId,
            @PathVariable Long categoryId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size) {
        try {
            // 限制分页大小
            if (size > 50) {
                size = 50;
            }

            IPage<ProductWithCategoryVO> productPage = productService.getProductsByCategoryIdWithCategoryPage(
                    page, size, categoryId, storeId, Product.Status.PUBLISHED.getCode());

            List<MiniProductResponse> products = productPage.getRecords().stream()
                    .map(MiniProductResponse::from)
                    .collect(Collectors.toList());

            PageResponse<MiniProductResponse> pageResponse = new PageResponse<>(
                    productPage.getCurrent(),
                    productPage.getSize(),
                    productPage.getTotal(),
                    productPage.getPages(),
                    products
            );

            return ApiResponse.success(pageResponse);

        } catch (Exception e) {
            log.error("查询分类商品失败，门店ID: {}, 分类ID: {}", storeId, categoryId, e);
            return ApiResponse.error("查询商品失败: " + e.getMessage());
        }
    }

    /**
     * 商品搜索
     */
    @GetMapping("/stores/{storeId}/products/search")
    public ApiResponse<PageResponse<MiniProductResponse>> searchProducts(
            @PathVariable Long storeId,
            @RequestParam("keyword") String keyword,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "20") Integer size) {
        try {
            if (keyword == null || keyword.trim().isEmpty()) {
                return ApiResponse.badRequest("搜索关键词不能为空");
            }

            // 限制分页大小
            if (size > 50) {
                size = 50;
            }

            IPage<ProductWithCategoryVO> productPage = productService.searchProductsWithCategoryPage(
                    page, size, storeId, keyword.trim());

            List<MiniProductResponse> products = productPage.getRecords().stream()
                    .map(MiniProductResponse::from)
                    .collect(Collectors.toList());

            PageResponse<MiniProductResponse> pageResponse = new PageResponse<>(
                    productPage.getCurrent(),
                    productPage.getSize(),
                    productPage.getTotal(),
                    productPage.getPages(),
                    products
            );

            return ApiResponse.success(pageResponse);

        } catch (Exception e) {
            log.error("搜索商品失败，门店ID: {}, 关键词: {}", storeId, keyword, e);
            return ApiResponse.error("搜索商品失败: " + e.getMessage());
        }
    }

    // ==================== 4. 门店桌台模块 ====================

    /**
     * 查询门店桌台
     */
    @GetMapping("/stores/{storeId}/tables")
    public ApiResponse<List<MiniTableResponse>> getStoreTables(@PathVariable Long storeId) {
        try {
            List<StoreTable> tables = storeTableService.getTablesByStoreId(storeId);

            List<MiniTableResponse> result = tables.stream()
                    .map(MiniTableResponse::from)
                    .collect(Collectors.toList());

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("查询门店桌台失败，门店ID: {}", storeId, e);
            return ApiResponse.error("查询门店桌台失败: " + e.getMessage());
        }
    }

    /**
     * 扫码获取桌台信息
     */
    @GetMapping("/tables/qr/{qrCode}")
    public ApiResponse<MiniTableDetailResponse> getTableByQrCode(@PathVariable String qrCode) {
        try {
            //todo
            StoreTable table = storeTableService.getTableByQrCode(qrCode);
            if (table == null) {
                return ApiResponse.notFound("桌台不存在或二维码无效");
            }

            // 获取门店信息
            Store store = storeService.getStoreById(table.getStoreId());

            MiniTableDetailResponse result = MiniTableDetailResponse.from(table, store);

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("扫码获取桌台信息失败，二维码: {}", qrCode, e);
            return ApiResponse.error("获取桌台信息失败: " + e.getMessage());
        }
    }

    // ==================== 5. 会员等级模块 ====================

    /**
     * 查询会员等级列表
     */
    @GetMapping("/membership/levels")
    public ApiResponse<List<MiniMembershipLevelResponse>> getMembershipLevels() {
        try {
            List<MembershipLevel> levels = membershipLevelService.getAllActiveLevels();

            List<MiniMembershipLevelResponse> result = levels.stream()
                    .map(MiniMembershipLevelResponse::from)
                    .collect(Collectors.toList());

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("查询会员等级列表失败", e);
            return ApiResponse.error("查询会员等级列表失败: " + e.getMessage());
        }
    }

    // ==================== 6. 工具接口 ====================

    /**
     * 距离计算工具
     */
    @PostMapping("/utils/calculate-distance")
    public ApiResponse<MiniDistanceResponse> calculateDistanceUtil(@Valid @RequestBody MiniCoordinateDistanceRequest request) {
        try {
            double distance = DistanceUtil.calculateDistance(request.getLat1(), request.getLng1(), request.getLat2(), request.getLng2());

            MiniDistanceResponse result = MiniDistanceResponse.create(distance);

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("计算距离失败", e);
            return ApiResponse.error("计算距离失败: " + e.getMessage());
        }
    }
}
