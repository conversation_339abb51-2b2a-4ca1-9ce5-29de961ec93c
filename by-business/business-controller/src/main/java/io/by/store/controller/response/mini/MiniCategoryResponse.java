package io.by.store.controller.response.mini;

import io.by.store.infrastructure.entity.ProductCategory;
import lombok.Data;

/**
 * 小程序商品分类响应
 */
@Data
public class MiniCategoryResponse {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父分类ID
     */
    private Long parentId;

    /**
     * 排序值
     */
    private Integer sortOrder;

    /**
     * 是否有子分类
     */
    private Boolean hasChildren;

    /**
     * 从ProductCategory实体转换
     */
    public static MiniCategoryResponse from(ProductCategory category, Boolean hasChildren) {
        MiniCategoryResponse response = new MiniCategoryResponse();
        response.setId(category.getId());
        response.setName(category.getName());
        response.setParentId(category.getParentId());
        response.setSortOrder(category.getSortOrder());
        response.setHasChildren(hasChildren);
        return response;
    }
}
