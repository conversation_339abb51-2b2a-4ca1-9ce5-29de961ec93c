package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.StoreTableService;
import io.by.store.application.TableUsageService;
import io.by.store.application.common.UserContext;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.*;
import io.by.store.controller.response.StoreTableResponse;
import io.by.store.controller.response.TableStatusResponse;
import io.by.store.controller.response.TableUsageResponse;
import io.by.store.infrastructure.entity.StoreTable;
import io.by.store.infrastructure.entity.TableUsage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店桌台控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/store-tables")
@RequiredArgsConstructor
public class StoreTableController {

    private final StoreTableService storeTableService;
    private final TableUsageService tableUsageService;

    /**
     * 创建桌台
     */
    @PostMapping("/create")
    public ApiResponse<StoreTableResponse> createStoreTable(@Valid @RequestBody StoreTableCreateRequest request) {
        StoreTable storeTable = new StoreTable();
        BeanUtils.copyProperties(request, storeTable);
        storeTable.setStoreId(UserContext.getCurrentStoreId());

        boolean success = storeTableService.createStoreTable(storeTable);
        if (success) {
            StoreTableResponse response = StoreTableResponse.from(storeTable);
            return ApiResponse.success("桌台创建成功", response);
        } else {
            return ApiResponse.error("桌台创建失败");
        }
    }

    /**
     * 批量创建桌台
     */
    @PostMapping("/batch-create")
    public ApiResponse<List<StoreTableResponse>> batchCreateStoreTables(@Valid @RequestBody StoreTableBatchCreateRequest request) {
        List<StoreTable> storeTables = request.getTables().stream()
                .map(req -> {
                    StoreTable storeTable = new StoreTable();
                    BeanUtils.copyProperties(req, storeTable);
                    return storeTable;
                })
                .collect(Collectors.toList());

        boolean success = storeTableService.batchCreateStoreTables(storeTables);
        if (success) {
            List<StoreTableResponse> responses = storeTables.stream()
                    .map(StoreTableResponse::from)
                    .collect(Collectors.toList());
            return ApiResponse.success("桌台批量创建成功", responses);
        } else {
            return ApiResponse.error("桌台批量创建失败");
        }
    }

    /**
     * 更新桌台信息
     */
    @PostMapping("/update")
    public ApiResponse<StoreTableResponse> updateStoreTable(@Valid @RequestBody StoreTableUpdateRequest request) {
        StoreTable storeTable = new StoreTable();
        BeanUtils.copyProperties(request, storeTable);
        storeTable.setStoreId(UserContext.getCurrentStoreId());

        boolean success = storeTableService.updateStoreTable(storeTable);
        if (success) {
            StoreTable updatedStoreTable = storeTableService.getStoreTableById(request.getId());
            StoreTableResponse response = StoreTableResponse.from(updatedStoreTable);
            return ApiResponse.success("桌台更新成功", response);
        } else {
            return ApiResponse.error("桌台更新失败");
        }
    }

    /**
     * 删除桌台
     */
    @PostMapping("/delete")
    public ApiResponse<Void> deleteStoreTable(@Valid @RequestBody StoreTableDeleteRequest request) {
        boolean success = storeTableService.deleteStoreTable(request.getId());
        if (success) {
            return ApiResponse.<Void>success("桌台删除成功", null);
        } else {
            return ApiResponse.error("桌台删除失败或桌台不存在");
        }
    }

    /**
     * 根据ID查询桌台
     */
    @GetMapping("/{id}")
    public ApiResponse<StoreTableResponse> getStoreTableById(@PathVariable Long id) {
        StoreTable storeTable = storeTableService.getStoreTableById(id);
        if (storeTable != null) {
            StoreTableResponse response = StoreTableResponse.from(storeTable);
            // 填充开桌状态信息
            fillTableUsageInfo(response);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("桌台不存在");
        }
    }

    /**
     * 根据门店ID查询激活的桌台列表
     */
    @GetMapping("/store")
    public ApiResponse<List<StoreTableResponse>> getActiveTablesByStoreId() {
        Long storeId = UserContext.getCurrentStoreId();
        List<StoreTable> storeTables = storeTableService.getActiveTablesByStoreId(storeId);
        List<StoreTableResponse> responses = storeTables.stream()
                .map(table -> {
                    StoreTableResponse response = StoreTableResponse.from(table);
                    // 填充开桌状态信息
                    fillTableUsageInfo(response);
                    return response;
                })
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 分页查询桌台
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<StoreTableResponse>> queryStoreTables(@Valid @RequestBody StoreTableQueryRequest request) {
        IPage<StoreTable> page = storeTableService.getStoreTablesPage(
                request.getCurrent(),
                request.getSize(),
                request.getStoreId(),
                request.getTableNumber(),
                request.getIsActive()
        );

        List<StoreTableResponse> responses = page.getRecords().stream()
                .map(table -> {
                    StoreTableResponse response = StoreTableResponse.from(table);
                    // 填充开桌状态信息
                    fillTableUsageInfo(response);
                    return response;
                })
                .collect(Collectors.toList());

        PageResponse<StoreTableResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 根据二维码URL查询桌台
     */
    @GetMapping("/qrcode")
    public ApiResponse<StoreTableResponse> getStoreTableByQrcodeUrl(@RequestParam String qrcodeUrl) {
        StoreTable storeTable = storeTableService.getStoreTableByQrcodeUrl(qrcodeUrl);
        if (storeTable != null) {
            StoreTableResponse response = StoreTableResponse.from(storeTable);
            // 填充开桌状态信息
            fillTableUsageInfo(response);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("桌台不存在");
        }
    }

    // ==================== 桌台使用记录相关接口 ====================

    /**
     * 查询桌台开桌状态
     */
    @GetMapping("/{tableId}/status")
    public ApiResponse<TableStatusResponse> getTableStatus(@PathVariable Long tableId) {
        StoreTable storeTable = storeTableService.getStoreTableById(tableId);
        if (storeTable == null) {
            return ApiResponse.notFound("桌台不存在");
        }

        TableStatusResponse response = new TableStatusResponse();
        response.setTableId(tableId);
        response.setTableNumber(storeTable.getTableNumber());
        response.setStoreId(storeTable.getStoreId());

        // 查询开桌状态
        boolean isOpen = tableUsageService.isTableOpen(tableId);
        response.setIsOpen(isOpen);

        if (isOpen) {
            // 查询当前开桌信息
            TableUsage currentUsage = tableUsageService.getCurrentTableUsage(tableId);
            if (currentUsage != null) {
                response.setCurrentOrderId(currentUsage.getOrderId());
                response.setOpenedAt(currentUsage.getOpenedAt());
                response.setCurrentDurationMinutes(tableUsageService.getCurrentUsageDuration(tableId));
            }
        }

        // 查询历史统计信息
        response.setTotalUsageCount(tableUsageService.getTableUsageCount(tableId));
        response.setAverageDurationMinutes(tableUsageService.getAverageUsageDuration(tableId));

        return ApiResponse.success(response);
    }

    /**
     * 查询桌台历史使用记录
     */
    @GetMapping("/{tableId}/usage-history")
    public ApiResponse<List<TableUsageResponse>> getTableUsageHistory(@PathVariable Long tableId) {
        List<TableUsage> usages = tableUsageService.getTableUsageHistory(tableId);
        List<TableUsageResponse> responses = usages.stream()
                .map(TableUsageResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 分页查询桌台使用记录
     */
    @PostMapping("/usage/query")
    public ApiResponse<PageResponse<TableUsageResponse>> queryTableUsages(@Valid @RequestBody TableUsageQueryRequest request) {
        IPage<TableUsage> page = tableUsageService.getTableUsagesPage(
                request.getCurrent(),
                request.getSize(),
                UserContext.getCurrentStoreId(),
                request.getTableId(),
                request.getStatus(),
                request.getStartTime(),
                request.getEndTime()
        );

        List<TableUsageResponse> responses = page.getRecords().stream()
                .map(TableUsageResponse::from)
                .collect(Collectors.toList());

        PageResponse<TableUsageResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 查询门店当前开桌数量
     */
    @GetMapping("/store/active-count")
    public ApiResponse<Integer> getActiveTableCount() {
        Long storeId = UserContext.getCurrentStoreId();
        Integer count = tableUsageService.getActiveTableCount(storeId);
        return ApiResponse.success(count);
    }

    /**
     * 查询门店当前开桌列表
     */
    @GetMapping("/store/active-tables")
    public ApiResponse<List<TableUsageResponse>> getActiveTableUsages() {
        Long storeId = UserContext.getCurrentStoreId();
        List<TableUsage> usages = tableUsageService.getActiveTableUsages(storeId);
        List<TableUsageResponse> responses = usages.stream()
                .map(TableUsageResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 开桌
     */
    @PostMapping("/{tableId}/open")
    public ApiResponse<Void> openTable(@PathVariable Long tableId, @RequestParam String orderId) {
        boolean success = tableUsageService.openTable(tableId, orderId);
        if (success) {
            return ApiResponse.success("开桌成功", null);
        } else {
            return ApiResponse.error("开桌失败");
        }
    }

    /**
     * 关桌
     */
    @PostMapping("/{tableId}/close")
    public ApiResponse<Void> closeTable(@PathVariable Long tableId) {
        boolean success = tableUsageService.closeTable(tableId);
        if (success) {
            return ApiResponse.success("关桌成功", null);
        } else {
            return ApiResponse.error("关桌失败");
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 填充桌台使用状态信息
     */
    private void fillTableUsageInfo(StoreTableResponse response) {
        if (response == null || response.getId() == null) {
            return;
        }

        // 查询开桌状态
        boolean isOpen = tableUsageService.isTableOpen(response.getId());
        response.setIsOpen(isOpen);

        if (isOpen) {
            // 查询当前开桌信息
            TableUsage currentUsage = tableUsageService.getCurrentTableUsage(response.getId());
            if (currentUsage != null) {
                response.setCurrentOrderId(currentUsage.getOrderId());
                response.setOpenedAt(currentUsage.getOpenedAt());
                response.setCurrentDurationMinutes(tableUsageService.getCurrentUsageDuration(response.getId()));
            }
        }
    }
}
