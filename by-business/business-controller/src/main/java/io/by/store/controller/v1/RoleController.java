package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.RoleService;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.request.RoleCreateRequest;
import io.by.store.controller.request.RoleQueryRequest;
import io.by.store.controller.request.RoleUpdateRequest;
import io.by.store.controller.response.RoleResponse;
import io.by.store.infrastructure.entity.Role;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/roles")
@RequiredArgsConstructor
public class RoleController {

    private final RoleService roleService;

    /**
     * 创建角色
     */
    @PostMapping("/create")
    public ApiResponse<RoleResponse> createRole(@Valid @RequestBody RoleCreateRequest request) {
        Role role = new Role();
        BeanUtils.copyProperties(request, role);

        boolean success = roleService.createRole(role);
        if (success) {
            RoleResponse response = RoleResponse.from(role);
            return ApiResponse.success("角色创建成功", response);
        } else {
            return ApiResponse.error("角色创建失败");
        }
    }

    /**
     * 更新角色
     */
    @PostMapping("/update")
    public ApiResponse<RoleResponse> updateRole(@Valid @RequestBody RoleUpdateRequest request) {
        Role role = new Role();
        BeanUtils.copyProperties(request, role);

        boolean success = roleService.updateRole(role);
        if (success) {
            Role updatedRole = roleService.getRoleById(role.getId());
            RoleResponse response = RoleResponse.from(updatedRole);
            return ApiResponse.success("角色更新成功", response);
        } else {
            return ApiResponse.error("角色更新失败");
        }
    }

    /**
     * 删除角色
     */
    @PostMapping("/delete/{id}")
    public ApiResponse<Void> deleteRole(@PathVariable Long id) {
        boolean success = roleService.deleteRole(id);
        if (success) {
            return ApiResponse.<Void>success("角色删除成功", null);
        } else {
            return ApiResponse.error("角色删除失败");
        }
    }

    /**
     * 根据ID查询角色
     */
    @GetMapping("/{id}")
    public ApiResponse<RoleResponse> getRoleById(@PathVariable Long id) {
        Role role = roleService.getRoleById(id);
        if (role != null) {
            RoleResponse response = RoleResponse.from(role);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("角色不存在");
        }
    }

    /**
     * 查询所有角色
     */
    @GetMapping("/list")
    public ApiResponse<List<RoleResponse>> getAllRoles() {
        List<Role> roles = roleService.getAllRoles();
        List<RoleResponse> responses = roles.stream()
                .map(RoleResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 分页查询角色
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<RoleResponse>> queryRoles(@Valid @RequestBody RoleQueryRequest request) {
        IPage<Role> page = roleService.getRolesPage(
                request.getCurrent(),
                request.getSize(),
                request.getName(),
                request.getDescription()
        );

        List<RoleResponse> responses = page.getRecords().stream()
                .map(RoleResponse::from)
                .collect(Collectors.toList());

        PageResponse<RoleResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 根据权限查询角色列表
     */
    @GetMapping("/by-permission")
    public ApiResponse<List<RoleResponse>> getRolesByPermission(@RequestParam String permission) {
        List<Role> roles = roleService.getRolesByPermission(permission);
        List<RoleResponse> responses = roles.stream()
                .map(RoleResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }
}
