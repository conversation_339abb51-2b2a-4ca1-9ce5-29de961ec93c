package io.by.store.controller.response;

import io.by.store.infrastructure.entity.PointsLog;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 积分记录响应
 */
@Data
public class PointsLogResponse {
    
    /**
     * 积分记录ID
     */
    private Long id;
    
    /**
     * 会员ID
     */
    private Long memberId;
    
    /**
     * 积分变动类型
     */
    private String changeType;
    
    /**
     * 积分变动类型名称
     */
    private String changeTypeName;
    
    /**
     * 积分变动数量
     */
    private Long pointsChange;
    
    /**
     * 变动前积分
     */
    private Long pointsBefore;
    
    /**
     * 变动后积分
     */
    private Long pointsAfter;
    
    /**
     * 关联的订单ID
     */
    private Long orderId;
    
    /**
     * 变动描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 从实体类转换
     */
    public static PointsLogResponse from(PointsLog pointsLog) {
        if (pointsLog == null) {
            return null;
        }
        
        PointsLogResponse response = new PointsLogResponse();
        response.setId(pointsLog.getId());
        response.setMemberId(pointsLog.getMemberId());
        response.setChangeType(pointsLog.getChangeType());
        response.setPointsChange(pointsLog.getPointsChange());
        response.setPointsBefore(pointsLog.getPointsBefore());
        response.setPointsAfter(pointsLog.getPointsAfter());
        response.setOrderId(pointsLog.getOrderId());
        response.setDescription(pointsLog.getDescription());
        response.setCreatedAt(pointsLog.getCreatedAt());
        
        // 设置变动类型名称
        try {
            io.by.store.application.common.PointsChangeType changeType = 
                io.by.store.application.common.PointsChangeType.fromCode(pointsLog.getChangeType());
            response.setChangeTypeName(changeType.getName());
        } catch (Exception e) {
            response.setChangeTypeName(pointsLog.getChangeType());
        }
        
        return response;
    }
}
