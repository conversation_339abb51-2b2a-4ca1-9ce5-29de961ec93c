package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;

/**
 * 查询商品请求
 */
@Data
public class ProductQueryRequest {

    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 商品名称（模糊查询）
     */
    private String name;

    /**
     * 分类ID
     */
    @Positive(message = "分类ID必须为正数")
    private Long categoryId;

    /**
     * 商品状态
     */
    @Pattern(regexp = "^(PUBLISHED|ARCHIVED|SOLDOUT)$", 
             message = "状态只能是PUBLISHED、ARCHIVED或SOLDOUT")
    private String status;

    /**
     * 商品编码（模糊查询）
     */
    private String productCode;
}
