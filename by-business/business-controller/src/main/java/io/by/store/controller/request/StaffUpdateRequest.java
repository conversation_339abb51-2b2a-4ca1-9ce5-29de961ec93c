package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 更新员工请求
 */
@Data
public class StaffUpdateRequest {
    
    /**
     * 员工ID
     */
    @NotNull(message = "员工ID不能为空")
    private Long id;
    
    /**
     * 登录用户名, 必须唯一
     */
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;
    
    /**
     * 员工真实姓名
     */
    @Size(max = 100, message = "真实姓名长度不能超过100个字符")
    private String fullName;
    
    /**
     * 员工联系电话
     */
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @Pattern(regexp = "^[0-9-+()\\s]*$", message = "联系电话格式不正确")
    private String phoneNumber;
    
    /**
     * 角色ID (逻辑外键, 对应 roles.id)
     */
    private Long roleId;
    
    /**
     * 所属门店ID (逻辑外键, 对应 stores.id)
     */
    private Long storeId;
    
    /**
     * 账户状态 (true: 启用, false: 禁用)
     */
    private Boolean isActive;
}
