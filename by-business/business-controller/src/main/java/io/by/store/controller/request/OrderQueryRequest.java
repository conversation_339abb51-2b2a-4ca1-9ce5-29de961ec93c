package io.by.store.controller.request;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;
import java.time.LocalDateTime;

/**
 * 订单查询请求
 */
@Data
public class OrderQueryRequest {

    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 订单号 (精确查询)
     */
    private String orderNo;

    /**
     * 会员ID
     */
    @Positive(message = "会员ID必须为正数")
    private Long memberId;

    /**
     * 桌台ID
     */
    @Positive(message = "桌台ID必须为正数")
    private Long tableId;

    /**
     * 订单类型
     * DINE_IN: 堂食
     * TAKE_AWAY: 外带
     */
    @Pattern(regexp = "^(DINE_IN|TAKE_AWAY)$", 
             message = "订单类型必须是: DINE_IN, TAKE_AWAY")
    private String orderType;

    /**
     * 订单状态
     * PENDING_PAYMENT: 待支付
     * PROCESSING: 制作中
     * COMPLETED: 已完成
     * CANCELLED: 已取消
     * 空值表示查询所有状态
     */
    @Pattern(regexp = "^(PENDING_PAYMENT|PROCESSING|COMPLETED|CANCELLED|)$",
             message = "订单状态必须是: PENDING_PAYMENT, PROCESSING, COMPLETED, CANCELLED")
    private String status;

    /**
     * 支付状态
     * UNPAID: 未支付
     * PAID: 已支付
     * REFUNDED: 已退款
     */
    @Pattern(regexp = "^(UNPAID|PAID|REFUNDED|)$",
             message = "支付状态必须是: UNPAID, PAID, REFUNDED")
    private String paymentStatus;

    /**
     * 支付方式
     * cash: 现金支付
     * balance: 余额支付
     * wechat_pay: 微信支付
     * offline_scan: 线下扫码
     */
    @Pattern(regexp = "^(cash|balance|wechat_pay|offline_scan|)$",
             message = "支付方式必须是: cash, balance, wechat_pay, offline_scan")
    private String paymentMethod;

    /**
     * 开始时间 (订单创建时间范围查询)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间 (订单创建时间范围查询)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    private String phoneNumber;
}
