package io.by.store.controller.response;

import io.by.store.infrastructure.entity.Store;
import lombok.Data;

/**
 * 门店响应
 */
@Data
public class StoreResponse {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 门店名称
     */
    private String name;

    /**
     * 门店地址
     */
    private String address;

    /**
     * 门店电话
     */
    private String phoneNumber;

    /**
     * 是否激活：true-激活，false-停用
     */
    private Boolean isActive;

    /**
     * 门店ID
     */
    private Long storeId;

    private String x;

    private String y;

    /**
     * 从实体类转换
     */
    public static StoreResponse from(Store store) {
        if (store == null) {
            return null;
        }

        StoreResponse response = new StoreResponse();
        response.setId(store.getId());
        response.setStoreId(store.getId());
        response.setName(store.getName());
        response.setAddress(store.getAddress());
        response.setPhoneNumber(store.getPhoneNumber());
        response.setIsActive(store.getIsActive());
        response.setX(store.getX());
        response.setY(store.getY());

        return response;
    }
}
