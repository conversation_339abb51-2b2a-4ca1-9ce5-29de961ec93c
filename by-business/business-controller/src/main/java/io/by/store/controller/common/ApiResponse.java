package io.by.store.controller.common;

import lombok.Data;

/**
 * 统一API响应结果
 */
@Data
public class ApiResponse<T> {
    
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    public ApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public ApiResponse(Integer code, String msg) {
        this();
        this.code = code;
        this.msg = msg;
    }
    
    public ApiResponse(Integer code, String msg, T data) {
        this(code, msg);
        this.data = data;
    }
    
    /**
     * 成功响应
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(200, "操作成功");
    }
    
    /**
     * 成功响应带数据
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "操作成功", data);
    }
    
    /**
     * 成功响应带消息和数据
     */
    public static <T> ApiResponse<T> success(String msg, T data) {
        return new ApiResponse<>(200, msg, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> ApiResponse<T> error(String msg) {
        return new ApiResponse<>(500, msg);
    }
    
    /**
     * 失败响应带错误码
     */
    public static <T> ApiResponse<T> error(Integer code, String msg) {
        return new ApiResponse<>(code, msg);
    }
    
    /**
     * 参数错误
     */
    public static <T> ApiResponse<T> badRequest(String msg) {
        return new ApiResponse<>(400, msg);
    }
    
    /**
     * 未找到
     */
    public static <T> ApiResponse<T> notFound(String msg) {
        return new ApiResponse<>(404, msg);
    }
}
