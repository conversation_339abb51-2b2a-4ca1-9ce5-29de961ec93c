package io.by.store.controller.response;

import io.by.store.infrastructure.entity.ProductCategory;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品分类响应
 */
@Data
public class ProductCategoryResponse {

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 父分类ID (0表示顶级分类)
     */
    private Long parentId;

    /**
     * 所属门店ID
     */
    private Long storeId;

    /**
     * 排序权重，数字越小越靠前
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 子分类列表（可选，用于树形结构展示）
     */
    private List<ProductCategoryResponse> children;

    /**
     * 商品数量（可选，统计信息）
     */
    private Long productCount;

    /**
     * 从实体转换为响应对象
     */
    public static ProductCategoryResponse from(ProductCategory category) {
        if (category == null) {
            return null;
        }
        ProductCategoryResponse response = new ProductCategoryResponse();
        BeanUtils.copyProperties(category, response);
        return response;
    }

    /**
     * 批量转换
     */
    public static List<ProductCategoryResponse> from(List<ProductCategory> categories) {
        if (categories == null) {
            return null;
        }
        return categories.stream()
                .map(ProductCategoryResponse::from)
                .collect(Collectors.toList());
    }
}
