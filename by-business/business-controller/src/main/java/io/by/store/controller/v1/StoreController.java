package io.by.store.controller.v1;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.StoreService;
import io.by.store.controller.request.StoreCreateRequest;
import io.by.store.controller.request.StoreDeleteRequest;
import io.by.store.controller.request.StoreQueryRequest;
import io.by.store.controller.request.StoreUpdateRequest;
import io.by.store.controller.common.ApiResponse;
import io.by.store.controller.common.PageResponse;
import io.by.store.controller.response.StoreResponse;
import io.by.store.infrastructure.entity.Store;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店控制器 v1
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/stores")
@RequiredArgsConstructor
public class StoreController {

    private final StoreService storeService;

    /**
     * 创建门店
     */
    @PostMapping("/create")
    public ApiResponse<StoreResponse> createStore(@Valid @RequestBody StoreCreateRequest request) {
        Store store = new Store();
        BeanUtils.copyProperties(request, store);

        boolean success = storeService.createStore(store);
        if (success) {
            StoreResponse response = StoreResponse.from(store);
            return ApiResponse.success("门店创建成功", response);
        } else {
            return ApiResponse.error("门店创建失败");
        }
    }

    /**
     * 更新门店信息
     */
    @PostMapping("/update")
    public ApiResponse<StoreResponse> updateStore(@Valid @RequestBody StoreUpdateRequest request) {
        Store store = new Store();
        BeanUtils.copyProperties(request, store);

        boolean success = storeService.updateStore(store);
        if (success) {
            Store updatedStore = storeService.getStoreById(request.getId());
            StoreResponse response = StoreResponse.from(updatedStore);
            return ApiResponse.success("门店更新成功", response);
        } else {
            return ApiResponse.error("门店更新失败");
        }
    }

    /**
     * 删除门店
     */
    @PostMapping("/delete")
    public ApiResponse<Void> deleteStore(@Valid @RequestBody StoreDeleteRequest request) {
        boolean success = storeService.deleteStore(request.getId());
        if (success) {
            return ApiResponse.<Void>success("门店删除成功", null);
        } else {
            return ApiResponse.error("门店删除失败或门店不存在");
        }
    }

    /**
     * 根据ID查询门店
     */
    @GetMapping("/{id}")
    public ApiResponse<StoreResponse> getStoreById(@PathVariable Long id) {
        Store store = storeService.getStoreById(id);
        if (store != null) {
            StoreResponse response = StoreResponse.from(store);
            return ApiResponse.success(response);
        } else {
            return ApiResponse.notFound("门店不存在");
        }
    }

    /**
     * 查询激活的门店列表
     */
    @GetMapping("/active")
    public ApiResponse<List<StoreResponse>> getActiveStores() {
        List<Store> stores = storeService.getActiveStores();
        List<StoreResponse> responses = stores.stream()
                .map(StoreResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }

    /**
     * 分页查询门店
     */
    @PostMapping("/query")
    public ApiResponse<PageResponse<StoreResponse>> queryStores(@Valid @RequestBody StoreQueryRequest request) {
        IPage<Store> page = storeService.getStoresPage(
                request.getCurrent(),
                request.getSize(),
                request.getName(),
                request.getIsActive()
        );

        List<StoreResponse> responses = page.getRecords().stream()
                .map(StoreResponse::from)
                .collect(Collectors.toList());

        PageResponse<StoreResponse> pageResponse = new PageResponse<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal(),
                page.getPages(),
                responses
        );

        return ApiResponse.success(pageResponse);
    }

    /**
     * 根据名称搜索门店
     */
    @GetMapping("/search")
    public ApiResponse<List<StoreResponse>> searchStoresByName(@RequestParam String name) {
        List<Store> stores = storeService.searchStoresByName(name);
        List<StoreResponse> responses = stores.stream()
                .map(StoreResponse::from)
                .collect(Collectors.toList());
        return ApiResponse.success(responses);
    }
}
