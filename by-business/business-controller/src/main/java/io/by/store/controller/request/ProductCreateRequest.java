package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 创建商品请求
 */
@Data
public class ProductCreateRequest {

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    @Size(max = 100, message = "商品名称长度不能超过100个字符")
    private String name;

    /**
     * 商品的详细描述
     */
    @Size(max = 1000, message = "商品描述长度不能超过1000个字符")
    private String description;

    /**
     * 商品图片URL
     */
    @Size(max = 1000, message = "图片URL长度不能超过1000个字符")
    private String imageUrl;

    /**
     * 所属分类ID
     */
    @NotNull(message = "分类ID不能为空")
    @Positive(message = "分类ID必须为正数")
    private Long categoryId;

    /**
     * 销售价格
     */
    @NotNull(message = "销售价格不能为空")
    @DecimalMin(value = "0.00", message = "销售价格不能为负数")
    @Digits(integer = 8, fraction = 2, message = "价格格式不正确，最多8位整数，2位小数")
    private BigDecimal price;

    /**
     * 当前库存数量
     */
    @NotNull(message = "库存数量不能为空")
    @PositiveOrZero(message = "库存数量不能为负数")
    private Integer stockQuantity;

    /**
     * 库存预警阈值
     */
    @NotNull(message = "预警阈值不能为空")
    @PositiveOrZero(message = "预警阈值不能为负数")
    private Integer alertQuantity;

    /**
     * 商品编码，方便POS机扫码或速记
     */
    @Size(max = 50, message = "商品编码长度不能超过50个字符")
    private String productCode;

    /**
     * 状态 (PUBLISHED:已上架, ARCHIVED:已下架, SOLDOUT:售罄)
     */
    @Pattern(regexp = "^(PUBLISHED|ARCHIVED|SOLDOUT)$", 
             message = "状态只能是PUBLISHED、ARCHIVED或SOLDOUT")
    private String status = "PUBLISHED";
}
