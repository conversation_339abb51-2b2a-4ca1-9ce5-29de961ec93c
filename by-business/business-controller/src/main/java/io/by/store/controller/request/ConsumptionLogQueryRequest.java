package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.Positive;

/**
 * 查询消费记录请求
 */
@Data
public class ConsumptionLogQueryRequest {
    
    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;
    
    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;
    
    /**
     * 会员ID
     */
    @Positive(message = "会员ID必须为正数")
    private Long memberId;
    
    /**
     * 订单ID
     */
    @Positive(message = "订单ID必须为正数")
    private Long orderId;
    
    /**
     * 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String startTime;
    
    /**
     * 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    private String endTime;
}
