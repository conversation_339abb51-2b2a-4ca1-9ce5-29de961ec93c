package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.PositiveOrZero;

/**
 * 查询商品分类请求
 */
@Data
public class ProductCategoryQueryRequest {

    /**
     * 当前页码，默认第1页
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer current = 1;

    /**
     * 每页大小，默认10条
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer size = 10;

    /**
     * 分类名称（模糊查询）
     */
    private String name;

    /**
     * 父分类ID
     */
    @PositiveOrZero(message = "父分类ID必须为非负数")
    private Long parentId;
}
