package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Positive;

/**
 * 订单支付状态更新请求
 */
@Data
public class OrderPaymentUpdateRequest {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    @Positive(message = "订单ID必须为正数")
    private Long orderId;

    /**
     * 支付状态
     * UNPAID: 未支付
     * PAID: 已支付
     * REFUNDED: 已退款
     */
    @NotBlank(message = "支付状态不能为空")
    @Pattern(regexp = "^(UNPAID|PAID|REFUNDED)$", 
             message = "支付状态必须是: UNPAID, PAID, REFUNDED")
    private String paymentStatus;
}
