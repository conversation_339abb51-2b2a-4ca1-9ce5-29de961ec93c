package io.by.store.controller.request;

import lombok.Data;

import javax.validation.constraints.*;

/**
 * 更新会员积分请求
 */
@Data
public class MemberPointsUpdateRequest {
    
    /**
     * 会员ID
     */
    @NotNull(message = "会员ID不能为空")
    @Positive(message = "会员ID必须为正数")
    private Long memberId;
    
    /**
     * 变动积分（正数为增加，负数为扣减）
     */
    @NotNull(message = "变动积分不能为空")
    private Long points;
    
    /**
     * 变动原因
     */
    @NotBlank(message = "变动原因不能为空")
    @Size(max = 200, message = "变动原因长度不能超过200个字符")
    private String reason;
}
