package io.by.store.controller.response.mini;

import io.by.store.infrastructure.entity.Store;
import io.by.store.infrastructure.entity.StoreTable;
import lombok.Data;

/**
 * 小程序桌台详情响应
 */
@Data
public class MiniTableDetailResponse {

    /**
     * 桌台ID
     */
    private Long tableId;

    /**
     * 桌台编号
     */
    private String tableNumber;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 容量
     */
    private Integer capacity;

    /**
     * 状态
     */
    private String status;

    /**
     * 从StoreTable和Store实体转换
     */
    public static MiniTableDetailResponse from(StoreTable table, Store store) {
        MiniTableDetailResponse response = new MiniTableDetailResponse();
        response.setTableId(table.getId());
        response.setTableNumber(table.getTableNumber());
        response.setStoreId(store.getId());
        response.setStoreName(store.getName());
        response.setCapacity(4); // 默认容量
        response.setStatus(table.getIsActive() ? "AVAILABLE" : "UNAVAILABLE");
        return response;
    }
}
