package io.by.store.controller.response;

import io.by.store.infrastructure.entity.ConsumptionLog;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 消费记录响应
 */
@Data
public class ConsumptionLogResponse {
    
    /**
     * 消费记录ID
     */
    private Long id;
    
    /**
     * 消费会员ID
     */
    private Long memberId;
    
    /**
     * 关联的订单ID
     */
    private Long orderId;
    
    /**
     * 消费金额
     */
    private BigDecimal amountConsumed;
    
    /**
     * 消费前余额
     */
    private BigDecimal balanceBefore;
    
    /**
     * 消费后余额
     */
    private BigDecimal balanceAfter;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 从实体类转换
     */
    public static ConsumptionLogResponse from(ConsumptionLog consumptionLog) {
        if (consumptionLog == null) {
            return null;
        }
        
        ConsumptionLogResponse response = new ConsumptionLogResponse();
        response.setId(consumptionLog.getId());
        response.setMemberId(consumptionLog.getMemberId());
        response.setOrderId(consumptionLog.getOrderId());
        response.setAmountConsumed(consumptionLog.getAmountConsumed());
        response.setBalanceBefore(consumptionLog.getBalanceBefore());
        response.setBalanceAfter(consumptionLog.getBalanceAfter());
        response.setCreatedAt(consumptionLog.getCreatedAt());
        
        return response;
    }
}
