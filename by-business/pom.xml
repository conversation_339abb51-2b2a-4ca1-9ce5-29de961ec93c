<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>io.by.store</groupId>
        <artifactId>by-store-parent</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <artifactId>by-business</artifactId>
    <packaging>pom</packaging>
    <name>by-business</name>
    <description>Business modules for by-store project</description>
    
    <modules>
        <module>business-controller</module>
        <module>business-application</module>
        <module>business-infrastructure</module>
    </modules>
    
</project>
