package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.MembershipLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 会员等级Mapper接口
 */
@Mapper
public interface MembershipLevelMapper extends BaseMapper<MembershipLevel>, MPJBaseMapper<MembershipLevel> {

    /**
     * 根据等级标签查询会员等级
     */
    @Select("SELECT * FROM membership_levels WHERE level_tag = #{levelTag}")
    MembershipLevel findByLevelTag(String levelTag);

    /**
     * 查询所有启用的会员等级，按积分阈值升序排列
     */
    @Select("SELECT * FROM membership_levels WHERE is_active = true ORDER BY upgrade_points_threshold ASC")
    List<MembershipLevel> findAllActiveLevels();

    /**
     * 查询所有会员等级，按积分阈值升序排列
     */
    @Select("SELECT * FROM membership_levels ORDER BY upgrade_points_threshold ASC")
    List<MembershipLevel> findAllLevelsOrderByThreshold();

    /**
     * 根据积分查询对应的会员等级
     */
    @Select("SELECT * FROM membership_levels WHERE is_active = true AND upgrade_points_threshold <= #{points} ORDER BY upgrade_points_threshold DESC LIMIT 1")
    MembershipLevel findLevelByPoints(Long points);

    /**
     * 查询比指定积分阈值高的下一个等级
     */
    @Select("SELECT * FROM membership_levels WHERE is_active = true AND upgrade_points_threshold > #{currentThreshold} ORDER BY upgrade_points_threshold ASC LIMIT 1")
    MembershipLevel findNextLevel(Long currentThreshold);

    /**
     * 查询启用的等级数量
     */
    @Select("SELECT COUNT(*) FROM membership_levels WHERE is_active = true")
    Integer countActiveLevels();

    /**
     * 查询最高等级
     */
    @Select("SELECT * FROM membership_levels WHERE is_active = true ORDER BY upgrade_points_threshold DESC LIMIT 1")
    MembershipLevel findMaxLevel();

    /**
     * 查询最低等级
     */
    @Select("SELECT * FROM membership_levels WHERE is_active = true ORDER BY upgrade_points_threshold ASC LIMIT 1")
    MembershipLevel findMinLevel();

    /**
     * 根据等级名称模糊查询
     */
    @Select("SELECT * FROM membership_levels WHERE level_name LIKE CONCAT('%', #{levelName}, '%') ORDER BY upgrade_points_threshold ASC")
    List<MembershipLevel> findByLevelNameLike(String levelName);

    /**
     * 查询积分阈值在指定范围内的等级
     */
    @Select("SELECT * FROM membership_levels WHERE upgrade_points_threshold BETWEEN #{minPoints} AND #{maxPoints} ORDER BY upgrade_points_threshold ASC")
    List<MembershipLevel> findByPointsRange(Long minPoints, Long maxPoints);
}
