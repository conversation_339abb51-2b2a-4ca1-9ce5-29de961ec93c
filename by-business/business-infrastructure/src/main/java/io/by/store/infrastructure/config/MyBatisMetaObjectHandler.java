package io.by.store.infrastructure.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis-Plus 自动填充处理器
 * 自动填充创建时间和更新时间
 */
@Slf4j
@Component
public class MyBatisMetaObjectHandler implements MetaObjectHandler {

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始插入填充...");

        
        // 自动填充创建时间
        this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());
        
        // 自动填充更新时间
        this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
        
        // 如果是继承了BaseEntity的实体，且没有设置storeId，则从上下文获取
        if (metaObject.hasGetter("storeId") && getFieldValByName("storeId", metaObject) == null) {
            Long currentStoreId = getCurrentStoreId();
            if (currentStoreId != null) {
                this.strictInsertFill(metaObject, "storeId", Long.class, currentStoreId);
                log.debug("自动填充门店ID: {}", currentStoreId);
            }
        }
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始更新填充...");
        
        // 自动填充更新时间
        this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
    }

    /**
     * 获取当前门店ID
     * 从用户上下文中获取当前用户的门店ID
     */
    private Long getCurrentStoreId() {
        try {
            // 使用反射调用UserContext，避免循环依赖
            Class<?> userContextClass = Class.forName("io.by.store.application.common.UserContext");
            java.lang.reflect.Method method = userContextClass.getMethod("getCurrentStoreId");
            return (Long) method.invoke(null);
        } catch (Exception e) {
            log.debug("获取当前门店ID失败: {}", e.getMessage());
            return null;
        }
    }
}
