package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.infrastructure.entity.TableUsage;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 桌台使用记录数据访问接口
 */
public interface TableUsageRepository {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 保存桌台使用记录
     */
    boolean save(TableUsage tableUsage);

    /**
     * 根据ID更新桌台使用记录
     */
    boolean updateById(TableUsage tableUsage);

    /**
     * 根据ID删除桌台使用记录
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询桌台使用记录
     */
    Optional<TableUsage> findById(Long id);

    /**
     * 查询所有桌台使用记录
     */
    List<TableUsage> findAll();

    // ==================== 业务查询 ====================

    /**
     * 根据桌台ID查询当前活跃的使用记录
     */
    Optional<TableUsage> findActiveUsageByTableId(Long tableId);

    /**
     * 根据桌台ID查询所有使用记录
     */
    List<TableUsage> findAllUsagesByTableId(Long tableId);

    /**
     * 根据订单ID查询使用记录
     */
    Optional<TableUsage> findByOrderId(String orderId);

    /**
     * 根据门店ID查询当前所有活跃的使用记录
     */
    List<TableUsage> findActiveUsagesByStoreId(Long storeId);

    /**
     * 分页查询桌台使用记录
     */
    IPage<TableUsage> findPage(
            Integer current,
            Integer size,
            Long storeId,
            Long tableId,
            String status,
            LocalDateTime startTime,
            LocalDateTime endTime
    );

    // ==================== 状态管理 ====================

    /**
     * 开桌 - 创建新的使用记录
     */
    boolean openTable(Long tableId, String orderId, Long storeId);

    /**
     * 关桌 - 完成当前使用记录
     */
    boolean closeTable(Long tableId);

    /**
     * 根据订单ID关桌
     */
    boolean closeTableByOrderId(String orderId);

    /**
     * 更新使用记录状态
     */
    boolean updateStatus(Long id, String status);

    // ==================== 统计查询 ====================

    /**
     * 统计门店当前开桌数量
     */
    Integer countActiveTablesByStoreId(Long storeId);

    /**
     * 统计桌台历史使用次数
     */
    Integer countUsagesByTableId(Long tableId);

    /**
     * 统计指定时间范围内的桌台使用次数
     */
    Integer countUsagesByDateRange(
            Long tableId,
            LocalDateTime startTime,
            LocalDateTime endTime
    );

    /**
     * 查询桌台平均使用时长（分钟）
     */
    Double getAverageUsageDurationByTableId(Long tableId);

    /**
     * 查询门店桌台平均使用时长（分钟）
     */
    Double getAverageUsageDurationByStoreId(Long storeId);

    // ==================== 批量操作 ====================

    /**
     * 批量保存桌台使用记录
     */
    boolean saveBatch(List<TableUsage> tableUsages);

    /**
     * 批量更新桌台使用记录
     */
    boolean updateBatchById(List<TableUsage> tableUsages);

    /**
     * 根据条件批量删除桌台使用记录
     */
    boolean deleteByIds(List<Long> ids);
}
