package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.Staff;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 员工Mapper接口
 */
@Mapper
public interface StaffMapper extends BaseMapper<Staff>, MPJBaseMapper<Staff> {

    /**
     * 根据用户名查询员工
     */
    @Select("SELECT * FROM staff WHERE username = #{username}")
    Staff findByUsername(String username);

    /**
     * 根据门店ID查询激活的员工列表
     */
    @Select("SELECT * FROM staff WHERE store_id = #{storeId} AND is_active = true ORDER BY created_at DESC")
    List<Staff> findActiveStaffByStoreId(Long storeId);

    /**
     * 根据角色ID查询员工列表
     */
    @Select("SELECT * FROM staff WHERE role_id = #{roleId} ORDER BY created_at DESC")
    List<Staff> findByRoleId(Long roleId);

    /**
     * 根据门店ID和角色ID查询员工列表
     */
    @Select("SELECT * FROM staff WHERE store_id = #{storeId} AND role_id = #{roleId} AND is_active = true")
    List<Staff> findByStoreIdAndRoleId(Long storeId, Long roleId);

    /**
     * 统计门店员工总数
     */
    @Select("SELECT COUNT(*) FROM staff WHERE store_id = #{storeId} AND is_active = true")
    Integer countActiveStaffByStoreId(Long storeId);

    /**
     * 根据手机号查询员工
     */
    @Select("SELECT * FROM staff WHERE phone_number = #{phoneNumber}")
    Staff findByPhoneNumber(String phoneNumber);
}
