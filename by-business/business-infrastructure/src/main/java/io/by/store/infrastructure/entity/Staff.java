package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 员工实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("staff")
public class Staff {

    /**
     * 员工ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 登录用户名, 必须唯一
     */
    @TableField("username")
    private String username;

    /**
     * 加密后的密码哈希值, 严禁存储明文密码
     */
    @TableField("password_hash")
    private String passwordHash;

    /**
     * 员工真实姓名
     */
    @TableField("full_name")
    private String fullName;

    /**
     * 员工联系电话
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 角色ID (逻辑外键, 对应 roles.id)
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 所属门店ID (逻辑外键, 对应 stores.id)
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 账户状态 (true: 启用, false: 禁用)
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
