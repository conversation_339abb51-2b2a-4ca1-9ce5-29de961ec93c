package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("recharge_logs")
public class RechargeLog {

    /**
     * 充值记录ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 充值订单号 (程序生成, 保证唯一)
     */
    @TableField("recharge_order_number")
    private String rechargeOrderNumber;

    /**
     * 充值会员ID (逻辑外键, 对应 users.memberId)
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 充值金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 充值前账户余额
     */
    @TableField("balance_before")
    private BigDecimal balanceBefore;

    /**
     * 充值后账户余额
     */
    @TableField("balance_after")
    private BigDecimal balanceAfter;

    /**
     * 支付方式 (cash, wechat_pay 等)
     */
    @TableField("payment_method")
    private String paymentMethod;

    /**
     * 第三方支付网关的交易流水号 (如微信支付订单号)
     */
    @TableField("payment_transaction_id")
    private String paymentTransactionId;

    /**
     * 充值订单状态: pending(待处理), successful(成功), failed(失败), refunded(退款)
     */
    @TableField("status")
    private String status;

    /**
     * 操作员工ID (适用于线下现金充值, 逻辑外键, 对应 staff.id)
     */
    @TableField("staff_id")
    private Long staffId;

    /**
     * 来源 (offline、online)
     */
    @TableField("source")
    private String source;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 记录创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

}
