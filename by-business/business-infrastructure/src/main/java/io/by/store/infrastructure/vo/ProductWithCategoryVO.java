package io.by.store.infrastructure.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品与分类联表查询VO
 */
@Data
public class ProductWithCategoryVO {

    /**
     * 商品ID
     */
    private Long id;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品的详细描述
     */
    private String description;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 所属分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 销售价格
     */
    private BigDecimal price;

    /**
     * 当前库存数量
     */
    private Integer stockQuantity;

    /**
     * 库存预警阈值
     */
    private Integer alertQuantity;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 状态 (PUBLISHED:已上架, ARCHIVED:已下架, SOLDOUT:售罄)
     */
    private String status;

    /**
     * 所属门店ID
     */
    private Long storeId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
