package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.Member;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 会员数据访问接口
 * 提供会员相关的数据操作方法，使用MyBatis Plus语法
 */
public interface MemberRepository {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 保存会员
     */
    boolean save(Member member);

    /**
     * 根据ID更新会员
     */
    boolean updateById(Member member);

    /**
     * 根据ID删除会员
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询会员
     */
    Optional<Member> findById(Long id);

    // ==================== 唯一性查询 ====================

    /**
     * 根据会员业务ID查询会员
     */
    Optional<Member> findByMemberId(Long memberId);

    /**
     * 根据微信OpenID查询会员
     */
    Optional<Member> findByWxOpenid(String wxOpenid);

    /**
     * 根据微信UnionID查询会员
     */
    Optional<Member> findByWxUnionid(String wxUnionid);

    /**
     * 根据手机号查询会员
     */
    Optional<Member> findByPhoneNumber(String phoneNumber);

    // ==================== 条件查询 ====================

    /**
     * 根据会员等级查询会员列表
     */
    List<Member> findByMembershipLevelId(Long membershipLevelId);

    /**
     * 查询余额大于等于指定金额的会员
     */
    List<Member> findByMinBalance(BigDecimal minBalance);

    /**
     * 查询积分大于等于指定数量的会员
     */
    List<Member> findByMinPoints(Long minPoints);

    /**
     * 根据昵称模糊查询会员
     */
    List<Member> findByNicknameLike(String nickname);

    // ==================== 分页查询 ====================

    /**
     * 分页查询会员
     * @param page 分页对象
     * @param nickname 昵称（可选，模糊查询）
     * @param phoneNumber 手机号（可选，模糊查询）
     * @param membershipLevelId 会员等级ID（可选）
     * @param membershipLevelName 会员等级名称（可选，模糊查询）
     * @return 分页结果
     */
    IPage<Member> findPage(Page<Member> page, String nickname, String phoneNumber, Long membershipLevelId, String membershipLevelName);

    // ==================== 排序查询 ====================

    /**
     * 查询最近注册的会员
     */
    List<Member> findRecentMembers(Integer limit);

    /**
     * 查询所有会员，按创建时间倒序
     */
    List<Member> findAllOrderByCreatedAtDesc();

    // ==================== 更新操作 ====================

    /**
     * 更新会员余额（增加或减少）
     * @param memberId 会员ID
     * @param amount 变更金额（正数为增加，负数为减少）
     * @return 是否更新成功
     */
    boolean updateBalance(Long memberId, BigDecimal amount);

    /**
     * 更新会员积分（增加或减少）
     * @param memberId 会员ID
     * @param points 变更积分（正数为增加，负数为减少）
     * @return 是否更新成功
     */
    boolean updatePoints(Long memberId, Long points);

    // ==================== 统计查询 ====================

    /**
     * 统计会员总数
     */
    Long countMembers();

    /**
     * 统计指定等级的会员数量
     */
    Long countMembersByLevel(Long membershipLevelId);

    /**
     * 统计余额大于等于指定金额的会员数量
     */
    Long countByMinBalance(BigDecimal minBalance);

    /**
     * 统计积分大于等于指定数量的会员数量
     */
    Long countByMinPoints(Long minPoints);

    /**
     * 统计指定日期范围内的新增会员数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 新增会员数量
     */
    Long countMembersByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    // ==================== 批量操作 ====================

    /**
     * 批量保存会员
     */
    boolean saveBatch(List<Member> members);

    /**
     * 批量更新会员
     */
    boolean updateBatchById(List<Member> members);

    /**
     * 根据条件批量删除会员
     */
    boolean deleteByIds(List<Long> ids);
}
