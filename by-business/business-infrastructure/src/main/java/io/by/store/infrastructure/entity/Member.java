package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 会员(用户)实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
public class Member {

    /**
     * 用户内部唯一ID (自增主键), 用于程序内部关联
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员业务ID (由Snowflake算法生成), 用于对外展示和业务流转
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 微信小程序用户的唯一标识 (针对单一小程序)
     */
    @TableField("wx_openid")
    private String wxOpenid;

    /**
     * 微信开放平台UnionID, 用于关联同一公司下的多个应用(小程序、公众号、App)
     */
    @TableField("wx_unionid")
    private String wxUnionid;

    /**
     * 手机号, 可作为登录凭证, 必须唯一
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 用户昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 用户头像URL
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 会员等级ID (逻辑外键, 对应 membership_levels.id)
     */
    @TableField("membership_level_id")
    private Long membershipLevelId;

    /**
     * 会员等级名称
     */
    @TableField("membership_level_name")
    private String membershipLevelName;

    /**
     * 账户余额
     */
    @TableField("balance")
    private BigDecimal balance;

    /**
     * 会员积分
     */
    @TableField("points")
    private Long points;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
