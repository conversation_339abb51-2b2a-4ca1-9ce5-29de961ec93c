package io.by.store.infrastructure.config;

import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import io.by.store.infrastructure.entity.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

/**
 * 门店数据隔离拦截器
 * 自动为继承了BaseEntity的实体查询添加门店ID条件
 */
@Slf4j
public class StoreDataScopeInterceptor implements InnerInterceptor {

    /**
     * 需要进行门店数据隔离的表名列表
     */
    private static final List<String> STORE_SCOPE_TABLES = Arrays.asList(
        "store_tables"  // store_tables表需要门店数据隔离
        // 可以添加更多需要门店隔离的表
    );

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, 
                           RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        
        // 获取当前门店ID
        Long currentStoreId = getCurrentStoreId();
        if (currentStoreId == null) {
            log.debug("当前门店ID为空，跳过数据隔离");
            return;
        }

        try {
            String originalSql = boundSql.getSql();
            log.debug("原始SQL: {}", originalSql);

            // 解析SQL
            Statement statement = CCJSqlParserUtil.parse(originalSql);
            if (statement instanceof Select) {
                Select select = (Select) statement;
                PlainSelect plainSelect = (PlainSelect) select.getSelectBody();
                
                // 检查是否需要添加门店条件
                if (needStoreScope(plainSelect)) {
                    addStoreCondition(plainSelect, currentStoreId);
                    String newSql = select.toString();
                    log.debug("修改后SQL: {}", newSql);
                    
                    // 替换SQL
                    PluginUtils.MPBoundSql mpBoundSql = PluginUtils.mpBoundSql(boundSql);
                    mpBoundSql.sql(newSql);
                }
            }
        } catch (JSQLParserException e) {
            log.warn("SQL解析失败，跳过门店数据隔离: {}", e.getMessage());
        }
    }

    /**
     * 判断是否需要添加门店数据隔离条件
     */
    private boolean needStoreScope(PlainSelect plainSelect) {
        if (plainSelect.getFromItem() == null) {
            return false;
        }
        
        String tableName = plainSelect.getFromItem().toString().toLowerCase();
        // 移除可能的别名
        if (tableName.contains(" ")) {
            tableName = tableName.split(" ")[0];
        }
        
        return STORE_SCOPE_TABLES.contains(tableName);
    }

    /**
     * 添加门店条件到WHERE子句
     */
    private void addStoreCondition(PlainSelect plainSelect, Long storeId) {
        // 创建门店条件: store_id = storeId
        Column storeIdColumn = new Column("store_id");
        LongValue storeIdValue = new LongValue(storeId);
        EqualsTo storeCondition = new EqualsTo();
        storeCondition.setLeftExpression(storeIdColumn);
        storeCondition.setRightExpression(storeIdValue);

        // 获取现有的WHERE条件
        Expression whereExpression = plainSelect.getWhere();
        
        if (whereExpression == null) {
            // 如果没有WHERE条件，直接设置门店条件
            plainSelect.setWhere(storeCondition);
        } else {
            // 如果已有WHERE条件，使用AND连接
            AndExpression andExpression = new AndExpression(whereExpression, storeCondition);
            plainSelect.setWhere(andExpression);
        }
    }

    /**
     * 获取当前门店ID
     * 这里可以从ThreadLocal、Session或其他上下文中获取
     */
    private Long getCurrentStoreId() {
        try {
            // 使用反射调用UserContext，避免循环依赖
            Class<?> userContextClass = Class.forName("io.by.store.application.common.UserContext");
            java.lang.reflect.Method method = userContextClass.getMethod("getCurrentStoreId");
            return (Long) method.invoke(null);
        } catch (Exception e) {
            log.debug("获取当前门店ID失败: {}", e.getMessage());
        }
        return null;
    }
}
