package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品实体类 (含库存预警)
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("products")
public class Product {

    /**
     * 商品ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品名称，如"25年新绿茶"
     */
    @TableField("name")
    private String name;

    /**
     * 商品的详细描述
     */
    @TableField("description")
    private String description;

    /**
     * 商品图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 所属分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 销售价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 当前库存数量
     */
    @TableField("stock_quantity")
    private Integer stockQuantity;

    /**
     * 库存预警阈值
     */
    @TableField("alert_quantity")
    private Integer alertQuantity;

    /**
     * 商品编码，方便POS机扫码或速记
     */
    @TableField("product_code")
    private String productCode;

    /**
     * 状态 (PUBLISHED:已上架, ARCHIVED:已下架, SOLDOUT:售罄)
     */
    @TableField("status")
    private String status;

    /**
     * 所属门店ID
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 商品状态枚举
     */
    public enum Status {
        PUBLISHED("PUBLISHED", "已上架"),
        ARCHIVED("ARCHIVED", "已下架"),
        SOLDOUT("SOLDOUT", "售罄");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
