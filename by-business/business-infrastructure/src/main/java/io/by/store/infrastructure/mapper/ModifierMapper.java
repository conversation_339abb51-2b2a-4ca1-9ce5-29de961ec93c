package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.Modifier;
import org.apache.ibatis.annotations.Mapper;

/**
 * 加料Mapper接口
 * 使用MyBatis Plus提供的基础功能，不再包含自定义SQL
 * 所有业务查询逻辑移至Repository层实现
 */
@Mapper
public interface ModifierMapper extends BaseMapper<Modifier>, MPJBaseMapper<Modifier> {
    // 继承BaseMapper和MPJBaseMapper提供的所有基础CRUD操作
    // 包括：insert, deleteById, updateById, selectById, selectList, selectPage等
    // 复杂查询逻辑通过Repository层使用LambdaQueryWrapper实现
}
