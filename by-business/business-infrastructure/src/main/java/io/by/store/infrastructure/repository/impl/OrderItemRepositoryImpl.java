package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.by.store.infrastructure.entity.OrderItem;
import io.by.store.infrastructure.mapper.OrderItemMapper;
import io.by.store.infrastructure.repository.OrderItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 订单商品项Repository实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderItemRepositoryImpl implements OrderItemRepository {

    private final OrderItemMapper orderItemMapper;

    @Override
    public boolean save(OrderItem orderItem) {
        try {
            return orderItemMapper.insert(orderItem) > 0;
        } catch (Exception e) {
            log.error("保存订单商品项失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean saveBatch(List<OrderItem> orderItems) {
        try {
            if (orderItems == null || orderItems.isEmpty()) {
                return false;
            }
            // 使用 MyBatis Plus 的批量插入
            for (OrderItem orderItem : orderItems) {
                orderItemMapper.insert(orderItem);
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存订单商品项失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateById(OrderItem orderItem) {
        try {
            return orderItemMapper.updateById(orderItem) > 0;
        } catch (Exception e) {
            log.error("更新订单商品项失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Optional<OrderItem> findById(Long id) {
        try {
            OrderItem orderItem = orderItemMapper.selectById(id);
            return Optional.ofNullable(orderItem);
        } catch (Exception e) {
            log.error("根据ID查询订单商品项失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<OrderItem> findByOrderId(Long orderId) {
        try {
            LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderItem::getOrderId, orderId)
                    .orderByAsc(OrderItem::getId);
            return orderItemMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据订单ID查询订单商品项失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<OrderItem> findByOrderIds(List<Long> orderIds) {
        try {
            if (orderIds == null || orderIds.isEmpty()) {
                return Collections.emptyList();
            }
            LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OrderItem::getOrderId, orderIds)
                    .orderByAsc(OrderItem::getOrderId, OrderItem::getId);
            return orderItemMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据订单ID列表查询订单商品项失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<OrderItem> findByProductId(Long productId) {
        try {
            LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderItem::getProductId, productId)
                    .orderByDesc(OrderItem::getId);
            return orderItemMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据商品ID查询订单商品项失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public Integer sumQuantityByProductId(
            Long productId,
            LocalDateTime startTime,
            LocalDateTime endTime) {
        try {
            // 这个方法需要跨表查询，暂时使用简单的实现
            // 如果需要更复杂的统计，建议在Service层实现
            LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderItem::getProductId, productId);

            List<OrderItem> orderItems = orderItemMapper.selectList(wrapper);
            return orderItems.stream()
                    .mapToInt(OrderItem::getQuantity)
                    .sum();
        } catch (Exception e) {
            log.error("统计商品销售数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<OrderItem> getProductSalesRanking(
            LocalDateTime startTime,
            LocalDateTime endTime,
            Integer limit) {
        try {
            // 这个方法需要复杂的分组统计，暂时返回空列表
            // 建议在Service层使用更复杂的查询逻辑实现
            log.warn("getProductSalesRanking方法需要在Service层实现复杂的分组统计逻辑");
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("查询商品销售排行失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean deleteById(Long id) {
        try {
            return orderItemMapper.deleteById(id) > 0;
        } catch (Exception e) {
            log.error("删除订单商品项失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteByOrderId(Long orderId) {
        try {
            LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderItem::getOrderId, orderId);
            return orderItemMapper.delete(wrapper) > 0;
        } catch (Exception e) {
            log.error("根据订单ID删除订单商品项失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
