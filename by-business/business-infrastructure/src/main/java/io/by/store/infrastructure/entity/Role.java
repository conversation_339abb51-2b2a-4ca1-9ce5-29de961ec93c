package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "roles", autoResultMap = true)
public class Role {

    /**
     * 角色ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色名称 (例如: 店长, 收银员, 后厨), 必须唯一
     */
    @TableField("name")
    private String name;

    /**
     * 角色描述, 说明该角色的主要职责
     */
    @TableField("description")
    private String description;

    /**
     * 权限配置 (JSONB格式), 用于存储该角色拥有的操作权限码
     * 例如: ["order:create", "product:edit"]
     */
    @TableField(value = "permissions", typeHandler = JacksonTypeHandler.class)
    private List<String> permissions;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
