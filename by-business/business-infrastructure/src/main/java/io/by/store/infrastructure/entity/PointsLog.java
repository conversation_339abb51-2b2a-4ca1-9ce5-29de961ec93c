package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 积分变动记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("points_logs")
public class PointsLog {

    /**
     * 积分记录ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 会员ID (逻辑外键, 对应 users.member_id)
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 积分变动类型
     */
    @TableField("change_type")
    private String changeType;

    /**
     * 积分变动数量 (正数增加, 负数减少)
     */
    @TableField("points_change")
    private Long pointsChange;

    /**
     * 变动前积分
     */
    @TableField("points_before")
    private Long pointsBefore;

    /**
     * 变动后积分
     */
    @TableField("points_after")
    private Long pointsAfter;

    /**
     * 关联的商品订单ID (如果适用, 逻辑外键, 对应 orders.id)
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 变动描述
     */
    @TableField("description")
    private String description;

    /**
     * 记录创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;
}
