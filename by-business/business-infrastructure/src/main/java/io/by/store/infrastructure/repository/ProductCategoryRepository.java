package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.infrastructure.entity.ProductCategory;

import java.util.List;
import java.util.Optional;

/**
 * 商品分类数据访问接口
 */
public interface ProductCategoryRepository {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 保存分类
     */
    boolean save(ProductCategory category);

    /**
     * 根据ID更新分类
     */
    boolean updateById(ProductCategory category);

    /**
     * 根据ID删除分类
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询分类
     */
    Optional<ProductCategory> findById(Long id);

    /**
     * 查询所有分类
     */
    List<ProductCategory> findAll();

    // ==================== 业务查询 ====================

    /**
     * 根据门店ID查询分类列表
     */
    List<ProductCategory> findByStoreId(Long storeId);

    /**
     * 根据父分类ID查询子分类列表
     */
    List<ProductCategory> findByParentId(Long parentId);

    /**
     * 根据门店ID和父分类ID查询子分类列表
     */
    List<ProductCategory> findByStoreIdAndParentId(Long storeId, Long parentId);

    /**
     * 根据名称查询分类（精确匹配）
     */
    Optional<ProductCategory> findByNameAndStoreId(String name, Long storeId);

    /**
     * 根据名称模糊查询分类
     */
    List<ProductCategory> findByNameLike(String name, Long storeId);

    /**
     * 查询顶级分类（parent_id = 0）
     */
    List<ProductCategory> findTopLevelCategories(Long storeId);

    /**
     * 分页查询分类
     */
    IPage<ProductCategory> findPage(Integer current, Integer size, String name, Long parentId, Long storeId);

    // ==================== 排序相关 ====================

    /**
     * 根据门店ID和父分类ID查询最大排序值
     */
    Integer findMaxSortOrder(Long storeId, Long parentId);

    /**
     * 更新排序值
     */
    boolean updateSortOrder(Long id, Integer sortOrder);

    // ==================== 统计查询 ====================

    /**
     * 统计门店下的分类总数
     */
    Long countByStoreId(Long storeId);

    /**
     * 统计某分类下的子分类数量
     */
    Long countByParentId(Long parentId);

    /**
     * 检查分类名称是否存在（排除指定ID）
     */
    boolean existsByNameAndStoreIdAndIdNot(String name, Long storeId, Long excludeId);

    // ==================== 批量操作 ====================

    /**
     * 批量保存分类
     */
    boolean saveBatch(List<ProductCategory> categories);

    /**
     * 批量删除分类
     */
    boolean deleteBatchByIds(List<Long> ids);
}
