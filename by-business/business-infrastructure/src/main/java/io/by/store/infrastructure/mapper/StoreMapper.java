package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.Store;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 门店Mapper接口
 */
@Mapper
public interface StoreMapper extends BaseMapper<Store>, MPJBaseMapper<Store> {

    /**
     * 查询激活的门店列表
     */
    @Select("SELECT * FROM stores WHERE is_active = true ORDER BY created_at DESC")
    List<Store> findActiveStores();

    /**
     * 根据门店名称查询门店
     */
    @Select("SELECT * FROM stores WHERE name LIKE CONCAT('%', #{name}, '%') AND is_active = true")
    List<Store> findByNameLike(String name);

    /**
     * 根据电话号码查询门店
     */
    @Select("SELECT * FROM stores WHERE phone_number = #{phoneNumber}")
    Store findByPhoneNumber(String phoneNumber);
}
