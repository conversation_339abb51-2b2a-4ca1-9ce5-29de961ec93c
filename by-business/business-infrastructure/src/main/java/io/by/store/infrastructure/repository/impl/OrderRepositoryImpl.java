package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.Order;
import io.by.store.infrastructure.mapper.OrderMapper;
import io.by.store.infrastructure.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 订单Repository实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderRepositoryImpl implements OrderRepository {

    private final OrderMapper orderMapper;

    @Override
    public boolean save(Order order) {
        try {
            return orderMapper.insert(order) > 0;
        } catch (Exception e) {
            log.error("保存订单失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateById(Order order) {
        try {
            return orderMapper.updateById(order) > 0;
        } catch (Exception e) {
            log.error("更新订单失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Optional<Order> findById(Long id) {
        try {
            Order order = orderMapper.selectById(id);
            return Optional.ofNullable(order);
        } catch (Exception e) {
            log.error("根据ID查询订单失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<Order> findByOrderNo(String orderNo) {
        try {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Order::getOrderNo, orderNo);
            Order order = orderMapper.selectOne(wrapper);
            return Optional.ofNullable(order);
        } catch (Exception e) {
            log.error("根据订单号查询订单失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<Order> findByMemberId(Long memberId) {
        try {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Order::getMemberId, memberId)
                    .orderByDesc(Order::getCreatedAt);
            return orderMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据会员ID查询订单失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Order> findByTableId(Long tableId) {
        try {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Order::getTableId, tableId)
                    .orderByDesc(Order::getCreatedAt);
            return orderMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据桌台ID查询订单失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Order> findByStatus(String status) {
        try {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Order::getStatus, status)
                    .orderByDesc(Order::getCreatedAt);
            return orderMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据状态查询订单失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public IPage<Order> findOrdersPage(
            Integer current,
            Integer size,
            String orderNo,
            Long memberId,
            Long tableId,
            String orderType,
            String status,
            String paymentStatus,
            String paymentMethod,
            LocalDateTime startTime,
            LocalDateTime endTime,
            String phoneNumber) {
        try {
            Page<Order> page = new Page<>(current, size);
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();

            // 构建查询条件
            if (StringUtils.hasText(orderNo)) {
                wrapper.eq(Order::getOrderNo, orderNo);
            }
            if (memberId != null) {
                wrapper.eq(Order::getMemberId, memberId);
            }
            if (tableId != null) {
                wrapper.eq(Order::getTableId, tableId);
            }
            if (StringUtils.hasText(orderType)) {
                wrapper.eq(Order::getOrderType, orderType);
            }
            if (StringUtils.hasText(status)) {
                wrapper.eq(Order::getStatus, status);
            }
            if (StringUtils.hasText(paymentStatus)) {
                wrapper.eq(Order::getPaymentStatus, paymentStatus);
            }
            if (StringUtils.hasText(paymentMethod)) {
                wrapper.eq(Order::getPaymentMethod, paymentMethod);
            }
            if (StringUtils.hasText(phoneNumber)) {
                wrapper.eq(Order::getPhoneNumber, phoneNumber);
            }
            if (startTime != null) {
                wrapper.ge(Order::getCreatedAt, startTime);
            }
            if (endTime != null) {
                wrapper.le(Order::getCreatedAt, endTime);
            }

            // 按创建时间倒序排列
            wrapper.orderByDesc(Order::getCreatedAt);

            return orderMapper.selectPage(page, wrapper);
        } catch (Exception e) {
            log.error("分页查询订单失败: {}", e.getMessage(), e);
            return new Page<>(current, size);
        }
    }

    @Override
    public Long countOrdersByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            if (startTime != null) {
                wrapper.ge(Order::getCreatedAt, startTime);
            }
            if (endTime != null) {
                wrapper.le(Order::getCreatedAt, endTime);
            }
            // 只统计已支付订单
            wrapper.eq(Order::getPaymentStatus, Order.PaymentStatus.PAID.getCode());
            return orderMapper.selectCount(wrapper);
        } catch (Exception e) {
            log.error("统计订单数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    /**
     * 统计指定日期范围内的订单销售总额（只统计已支付订单）
     */
    public BigDecimal sumPayableAmountByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            if (startTime != null) {
                wrapper.ge(Order::getCreatedAt, startTime);
            }
            if (endTime != null) {
                wrapper.le(Order::getCreatedAt, endTime);
            }
            // 只统计已支付订单
            wrapper.eq(Order::getPaymentStatus, Order.PaymentStatus.PAID.getCode());

            List<Order> orders = orderMapper.selectList(wrapper);
            return orders.stream()
                    .map(Order::getPayableAmount)
                    .filter(amount -> amount != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("统计订单销售总额失败: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public List<Order> findRecentOrders(Integer limit) {
        try {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            wrapper.orderByDesc(Order::getCreatedAt);
            if (limit != null && limit > 0) {
                wrapper.last("LIMIT " + limit);
            }
            return orderMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("查询最近订单失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public BigDecimal sumOrderAmountByDateRange(
            LocalDateTime startTime,
            LocalDateTime endTime,
            String status) {
        try {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            if (startTime != null) {
                wrapper.ge(Order::getCreatedAt, startTime);
            }
            if (endTime != null) {
                wrapper.le(Order::getCreatedAt, endTime);
            }
            if (StringUtils.hasText(status)) {
                wrapper.eq(Order::getStatus, status);
            }

            List<Order> orders = orderMapper.selectList(wrapper);
            return orders.stream()
                    .map(Order::getPayableAmount)
                    .filter(amount -> amount != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("统计订单金额失败: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public boolean updateOrderStatus(Long orderId, String status) {
        try {
            LambdaUpdateWrapper<Order> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(Order::getId, orderId)
                    .set(Order::getStatus, status)
                    .set(Order::getUpdatedAt, LocalDateTime.now());
            return orderMapper.update(null, wrapper) > 0;
        } catch (Exception e) {
            log.error("更新订单状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updatePaymentStatus(Long orderId, String paymentStatus) {
        try {
            LambdaUpdateWrapper<Order> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(Order::getId, orderId)
                    .set(Order::getPaymentStatus, paymentStatus)
                    .set(Order::getUpdatedAt, LocalDateTime.now());
            return orderMapper.update(null, wrapper) > 0;
        } catch (Exception e) {
            log.error("更新订单支付状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean batchUpdateOrderStatus(List<Long> orderIds, String status) {
        try {
            LambdaUpdateWrapper<Order> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(Order::getId, orderIds)
                    .set(Order::getStatus, status)
                    .set(Order::getUpdatedAt, LocalDateTime.now());
            return orderMapper.update(null, wrapper) > 0;
        } catch (Exception e) {
            log.error("批量更新订单状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateOrderAmounts(
            Long orderId,
            BigDecimal originalAmount,
            BigDecimal discountAmount,
            BigDecimal payableAmount) {
        try {
            LambdaUpdateWrapper<Order> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(Order::getId, orderId)
                    .set(Order::getOriginalAmount, originalAmount)
                    .set(Order::getDiscountAmount, discountAmount)
                    .set(Order::getPayableAmount, payableAmount)
                    .set(Order::getUpdatedAt, LocalDateTime.now());
            return orderMapper.update(null, wrapper) > 0;
        } catch (Exception e) {
            log.error("更新订单金额失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteById(Long id) {
        try {
            return orderMapper.deleteById(id) > 0;
        } catch (Exception e) {
            log.error("删除订单失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
