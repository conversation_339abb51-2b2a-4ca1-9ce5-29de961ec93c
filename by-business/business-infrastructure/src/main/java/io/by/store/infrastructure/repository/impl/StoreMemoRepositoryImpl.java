package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.StoreMemo;
import io.by.store.infrastructure.mapper.StoreMemoMapper;
import io.by.store.infrastructure.repository.StoreMemoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 门店备忘录Repository实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class StoreMemoRepositoryImpl implements StoreMemoRepository {

    private final StoreMemoMapper storeMemoMapper;

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public boolean save(StoreMemo memo) {
        try {
            return storeMemoMapper.insert(memo) > 0;
        } catch (Exception e) {
            log.error("保存备忘录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateById(StoreMemo memo) {
        try {
            return storeMemoMapper.updateById(memo) > 0;
        } catch (Exception e) {
            log.error("更新备忘录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteById(Long id) {
        try {
            return storeMemoMapper.deleteById(id) > 0;
        } catch (Exception e) {
            log.error("删除备忘录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Optional<StoreMemo> findById(Long id) {
        try {
            StoreMemo memo = storeMemoMapper.selectById(id);
            return Optional.ofNullable(memo);
        } catch (Exception e) {
            log.error("根据ID查询备忘录失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    // ==================== 分页查询 ====================

    @Override
    public IPage<StoreMemo> findPage(Page<StoreMemo> page, Long storeId, String status, 
                                    Long creatorId, String content) {
        try {
            LambdaQueryWrapper<StoreMemo> wrapper = buildQueryWrapper(storeId, status, creatorId, content);
            wrapper.orderByDesc(StoreMemo::getCreatedAt);
            return storeMemoMapper.selectPage(page, wrapper);
        } catch (Exception e) {
            log.error("分页查询备忘录失败: {}", e.getMessage(), e);
            return new Page<>(page.getCurrent(), page.getSize());
        }
    }

    // ==================== 条件查询 ====================

    @Override
    public List<StoreMemo> findByStoreId(Long storeId) {
        try {
            LambdaQueryWrapper<StoreMemo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StoreMemo::getStoreId, storeId)
                    .orderByDesc(StoreMemo::getCreatedAt);
            return storeMemoMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据门店ID查询备忘录失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<StoreMemo> findByStatus(String status) {
        try {
            LambdaQueryWrapper<StoreMemo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StoreMemo::getStatus, status)
                    .orderByDesc(StoreMemo::getCreatedAt);
            return storeMemoMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据状态查询备忘录失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<StoreMemo> findByCreatorId(Long creatorId) {
        try {
            LambdaQueryWrapper<StoreMemo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StoreMemo::getCreatorId, creatorId)
                    .orderByDesc(StoreMemo::getCreatedAt);
            return storeMemoMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据创建人ID查询备忘录失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<StoreMemo> findByStoreIdAndStatus(Long storeId, String status) {
        try {
            LambdaQueryWrapper<StoreMemo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StoreMemo::getStoreId, storeId)
                    .eq(StoreMemo::getStatus, status)
                    .orderByDesc(StoreMemo::getCreatedAt);
            return storeMemoMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据门店ID和状态查询备忘录失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    // ==================== 状态更新 ====================

    @Override
    public boolean updateStatus(Long id, String status, LocalDateTime completedAt) {
        try {
            LambdaUpdateWrapper<StoreMemo> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(StoreMemo::getId, id)
                    .set(StoreMemo::getStatus, status)
                    .set(StoreMemo::getUpdatedAt, LocalDateTime.now());
            
            if (StoreMemo.Status.DONE.getCode().equals(status)) {
                wrapper.set(StoreMemo::getCompletedAt, completedAt != null ? completedAt : LocalDateTime.now());
            } else {
                wrapper.set(StoreMemo::getCompletedAt, null);
            }
            
            return storeMemoMapper.update(null, wrapper) > 0;
        } catch (Exception e) {
            log.error("更新备忘录状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateStatusBatch(List<Long> ids, String status, LocalDateTime completedAt) {
        try {
            if (ids == null || ids.isEmpty()) {
                return false;
            }
            
            LambdaUpdateWrapper<StoreMemo> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(StoreMemo::getId, ids)
                    .set(StoreMemo::getStatus, status)
                    .set(StoreMemo::getUpdatedAt, LocalDateTime.now());
            
            if (StoreMemo.Status.DONE.getCode().equals(status)) {
                wrapper.set(StoreMemo::getCompletedAt, completedAt != null ? completedAt : LocalDateTime.now());
            } else {
                wrapper.set(StoreMemo::getCompletedAt, null);
            }
            
            return storeMemoMapper.update(null, wrapper) > 0;
        } catch (Exception e) {
            log.error("批量更新备忘录状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 统计查询 ====================

    @Override
    public Long countByStoreId(Long storeId) {
        try {
            LambdaQueryWrapper<StoreMemo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StoreMemo::getStoreId, storeId);
            return storeMemoMapper.selectCount(wrapper);
        } catch (Exception e) {
            log.error("统计门店备忘录数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Long countByStoreIdAndStatus(Long storeId, String status) {
        try {
            LambdaQueryWrapper<StoreMemo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StoreMemo::getStoreId, storeId)
                    .eq(StoreMemo::getStatus, status);
            return storeMemoMapper.selectCount(wrapper);
        } catch (Exception e) {
            log.error("统计门店指定状态备忘录数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Long countByCreatorId(Long creatorId) {
        try {
            LambdaQueryWrapper<StoreMemo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StoreMemo::getCreatorId, creatorId);
            return storeMemoMapper.selectCount(wrapper);
        } catch (Exception e) {
            log.error("统计创建人备忘录数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<StoreMemo> buildQueryWrapper(Long storeId, String status, 
                                                           Long creatorId, String content) {
        LambdaQueryWrapper<StoreMemo> wrapper = new LambdaQueryWrapper<>();
        
        if (storeId != null) {
            wrapper.eq(StoreMemo::getStoreId, storeId);
        }
        
        if (StringUtils.hasText(status)) {
            wrapper.eq(StoreMemo::getStatus, status);
        }
        
        if (creatorId != null) {
            wrapper.eq(StoreMemo::getCreatorId, creatorId);
        }
        
        if (StringUtils.hasText(content)) {
            wrapper.like(StoreMemo::getContent, content);
        }
        
        return wrapper;
    }
}
