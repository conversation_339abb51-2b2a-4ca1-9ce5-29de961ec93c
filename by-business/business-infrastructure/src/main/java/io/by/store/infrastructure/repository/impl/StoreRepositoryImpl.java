package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.Store;
import io.by.store.infrastructure.mapper.StoreMapper;
import io.by.store.infrastructure.repository.StoreRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 门店数据访问实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class StoreRepositoryImpl implements StoreRepository {

    private final StoreMapper storeMapper;

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public boolean save(Store store) {
        try {
            return storeMapper.insert(store) > 0;
        } catch (Exception e) {
            log.error("保存门店失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateById(Store store) {
        try {
            store.setUpdatedAt(LocalDateTime.now());
            return storeMapper.updateById(store) > 0;
        } catch (Exception e) {
            log.error("更新门店失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteById(Long id) {
        try {
            // 逻辑删除：设置 isActive 为 false
            LambdaUpdateWrapper<Store> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Store::getId, id)
                    .set(Store::getIsActive, false)
                    .set(Store::getUpdatedAt, LocalDateTime.now());
            return storeMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("删除门店失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Optional<Store> findById(Long id) {
        try {
            Store store = storeMapper.selectById(id);
            return Optional.ofNullable(store);
        } catch (Exception e) {
            log.error("根据ID查询门店失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<Store> findAll() {
        try {
            LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByDesc(Store::getCreatedAt);
            return storeMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("查询所有门店失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    // ==================== 业务查询 ====================

    @Override
    public List<Store> findActiveStores() {
        try {
            return storeMapper.findActiveStores();
        } catch (Exception e) {
            log.error("查询激活门店失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Store> findByNameLike(String name) {
        try {
            if (!StringUtils.hasText(name)) {
                return findActiveStores();
            }
            return storeMapper.findByNameLike(name);
        } catch (Exception e) {
            log.error("根据名称模糊查询门店失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public Optional<Store> findByPhoneNumber(String phoneNumber) {
        try {
            Store store = storeMapper.findByPhoneNumber(phoneNumber);
            return Optional.ofNullable(store);
        } catch (Exception e) {
            log.error("根据电话号码查询门店失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<Store> findByName(String name) {
        try {
            LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Store::getName, name)
                    .eq(Store::getIsActive, true);
            Store store = storeMapper.selectOne(queryWrapper);
            return Optional.ofNullable(store);
        } catch (Exception e) {
            log.error("根据名称精确查询门店失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public IPage<Store> findPage(Integer current, Integer size, String name, Boolean isActive) {
        try {
            Page<Store> page = new Page<>(current, size);
            LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
            
            if (StringUtils.hasText(name)) {
                queryWrapper.like(Store::getName, name);
            }
            if (isActive != null) {
                queryWrapper.eq(Store::getIsActive, isActive);
            }
            
            queryWrapper.orderByDesc(Store::getCreatedAt);
            return storeMapper.selectPage(page, queryWrapper);
        } catch (Exception e) {
            log.error("分页查询门店失败: {}", e.getMessage(), e);
            return new Page<>(current, size);
        }
    }

    // ==================== 状态管理 ====================

    @Override
    public boolean activateStore(Long id) {
        return updateStatus(id, true);
    }

    @Override
    public boolean deactivateStore(Long id) {
        return updateStatus(id, false);
    }

    @Override
    public boolean updateStatus(Long id, Boolean isActive) {
        try {
            LambdaUpdateWrapper<Store> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Store::getId, id)
                    .set(Store::getIsActive, isActive)
                    .set(Store::getUpdatedAt, LocalDateTime.now());
            return storeMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("更新门店状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateStatusBatch(List<Long> ids, Boolean isActive) {
        try {
            LambdaUpdateWrapper<Store> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(Store::getId, ids)
                    .set(Store::getIsActive, isActive)
                    .set(Store::getUpdatedAt, LocalDateTime.now());
            return storeMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("批量更新门店状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 统计查询 ====================

    @Override
    public Long countActiveStores() {
        try {
            LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Store::getIsActive, true);
            return storeMapper.selectCount(queryWrapper);
        } catch (Exception e) {
            log.error("统计激活门店数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Long countAllStores() {
        try {
            return storeMapper.selectCount(null);
        } catch (Exception e) {
            log.error("统计所有门店数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public boolean existsByName(String name) {
        try {
            LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Store::getName, name);
            return storeMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查门店名称是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByPhoneNumber(String phoneNumber) {
        try {
            LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Store::getPhoneNumber, phoneNumber);
            return storeMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查电话号码是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByNameAndIdNot(String name, Long id) {
        try {
            LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Store::getName, name)
                    .ne(Store::getId, id);
            return storeMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查门店名称是否存在（排除指定ID）失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByPhoneNumberAndIdNot(String phoneNumber, Long id) {
        try {
            LambdaQueryWrapper<Store> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Store::getPhoneNumber, phoneNumber)
                    .ne(Store::getId, id);
            return storeMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查电话号码是否存在（排除指定ID）失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
