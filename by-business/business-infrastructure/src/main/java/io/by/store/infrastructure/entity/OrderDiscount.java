package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单优惠明细实体类
 */
@Data
@TableName("order_discounts")
public class OrderDiscount {

    /**
     * 订单优惠明细ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 优惠类型：COUPON(优惠券), MEMBER(会员折扣), MANUAL(手动改价), PROMOTION(营销活动)
     */
    @TableField("discount_type")
    private String discountType;

    /**
     * 优惠描述，如"满20减5优惠券"或"店长手动减免"
     */
    @TableField("description")
    private String description;

    /**
     * 该项优惠的金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 可选的关联ID，如优惠券ID、活动ID
     */
    @TableField("reference_id")
    private Long referenceId;

    /**
     * 优惠类型枚举
     */
    public enum DiscountType {
        COUPON("COUPON", "优惠券"),
        MEMBER("MEMBER", "会员折扣"),
        MANUAL("MANUAL", "手动改价"),
        PROMOTION("PROMOTION", "营销活动");

        private final String code;
        private final String description;

        DiscountType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
