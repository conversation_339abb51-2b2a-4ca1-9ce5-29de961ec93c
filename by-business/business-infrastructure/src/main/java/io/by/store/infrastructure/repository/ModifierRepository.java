package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.infrastructure.entity.Modifier;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 加料数据访问接口
 */
public interface ModifierRepository {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 保存加料
     */
    boolean save(Modifier modifier);

    /**
     * 根据ID更新加料
     */
    boolean updateById(Modifier modifier);

    /**
     * 根据ID删除加料
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询加料
     */
    Optional<Modifier> findById(Long id);

    /**
     * 查询所有加料
     */
    List<Modifier> findAll();

    // ==================== 业务查询 ====================

    /**
     * 根据门店ID查询加料列表
     */
    List<Modifier> findByStoreId(Long storeId);

    /**
     * 根据名称查询加料（精确匹配）
     */
    Optional<Modifier> findByNameAndStoreId(String name, Long storeId);

    /**
     * 根据名称模糊查询加料
     */
    List<Modifier> findByNameLike(String name, Long storeId);

    /**
     * 分页查询加料
     */
    IPage<Modifier> findPage(Integer current, Integer size, String name, Long storeId);

    // ==================== 价格相关 ====================

    /**
     * 更新加料价格
     */
    boolean updatePriceChange(Long id, BigDecimal priceChange);

    /**
     * 根据价格范围查询加料
     */
    List<Modifier> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Long storeId);

    /**
     * 查询免费加料（价格变动为0）
     */
    List<Modifier> findFreeModifiers(Long storeId);

    /**
     * 查询收费加料（价格变动大于0）
     */
    List<Modifier> findPaidModifiers(Long storeId);

    // ==================== 统计查询 ====================

    /**
     * 统计门店加料总数
     */
    Long countByStoreId(Long storeId);

    /**
     * 检查加料名称是否存在（排除指定ID）
     */
    boolean existsByNameAndStoreIdAndIdNot(String name, Long storeId, Long excludeId);

    /**
     * 统计免费加料数量
     */
    Long countFreeModifiers(Long storeId);

    /**
     * 统计收费加料数量
     */
    Long countPaidModifiers(Long storeId);

    // ==================== 批量操作 ====================

    /**
     * 批量保存加料
     */
    boolean saveBatch(List<Modifier> modifiers);

    /**
     * 批量删除加料
     */
    boolean deleteBatchByIds(List<Long> ids);

    /**
     * 批量更新价格
     */
    boolean updatePriceChangeBatch(List<Long> ids, BigDecimal priceChange);
}
