package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 会员等级定义实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("membership_levels")
public class MembershipLevel {

    /**
     * 等级ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 等级名称 (例如: 普通会员, 白银会员, 黄金会员)
     */
    @TableField("level_name")
    private String levelName;

    /**
     * 等级标签/编码 (例如: normal, silver, gold), 用于程序内部识别
     */
    @TableField("level_tag")
    private String levelTag;

    /**
     * 升级到此等级所需满足的累计积分阈值
     */
    @TableField("upgrade_points_threshold")
    private Long upgradePointsThreshold;

    /**
     * 等级权益描述
     */
    @TableField("description")
    private String description;

    /**
     * 等级图标URL
     */
    @TableField("icon_url")
    private String iconUrl;

    /**
     * 该等级是否启用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 记录创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
