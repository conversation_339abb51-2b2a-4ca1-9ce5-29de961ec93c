package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色Mapper接口
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role>, MPJBaseMapper<Role> {

    /**
     * 根据角色名称查询角色
     */
    @Select("SELECT * FROM roles WHERE name = #{name}")
    Role findByName(String name);

    /**
     * 查询所有角色列表
     */
    @Select("SELECT * FROM roles ORDER BY created_at DESC")
    List<Role> findAllRoles();

    /**
     * 根据权限查询角色列表
     */
    @Select("SELECT * FROM roles WHERE permissions::text LIKE CONCAT('%', #{permission}, '%')")
    List<Role> findByPermission(String permission);

    /**
     * 统计角色总数
     */
    @Select("SELECT COUNT(*) FROM roles")
    Integer countRoles();
}
