package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品加料实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("modifiers")
public class Modifier {

    /**
     * 加料ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 加料名称，如"珍珠"
     */
    @TableField("name")
    private String name;

    /**
     * 价格变动 (通常为正数，表示加价)
     */
    @TableField("price_change")
    private BigDecimal priceChange;

    /**
     * 所属门店ID
     */
    @TableField("store_id")
    private Long storeId;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
