package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.infrastructure.entity.Order;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 订单Repository接口
 */
public interface OrderRepository {

    /**
     * 保存订单
     */
    boolean save(Order order);

    /**
     * 更新订单
     */
    boolean updateById(Order order);

    /**
     * 根据ID查询订单
     */
    Optional<Order> findById(Long id);

    /**
     * 根据订单号查询订单
     */
    Optional<Order> findByOrderNo(String orderNo);

    /**
     * 根据会员ID查询订单列表
     */
    List<Order> findByMemberId(Long memberId);

    /**
     * 根据桌台ID查询订单列表
     */
    List<Order> findByTableId(Long tableId);

    /**
     * 根据状态查询订单列表
     */
    List<Order> findByStatus(String status);

    /**
     * 分页查询订单
     */
    IPage<Order> findOrdersPage(
            Integer current,
            Integer size,
            String orderNo,
            Long memberId,
            Long tableId,
            String orderType,
            String status,
            String paymentStatus,
            String paymentMethod,
            LocalDateTime startTime,
            LocalDateTime endTime,
            String phoneNumber
    );

    // ==================== 统计查询 ====================

    /**
     * 统计指定日期范围内的订单数量
     */
    Long countOrdersByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定日期范围内的订单销售总额（只统计已支付订单）
     */
    BigDecimal sumPayableAmountByDateRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定时间范围内的订单总金额
     */
    BigDecimal sumOrderAmountByDateRange(
            LocalDateTime startTime,
            LocalDateTime endTime,
            String status
    );

    /**
     * 查询最近的订单列表（按创建时间倒序）
     * @param limit 限制数量
     * @return 最近的订单列表
     */
    List<Order> findRecentOrders(Integer limit);

    /**
     * 更新订单状态
     */
    boolean updateOrderStatus(Long orderId, String status);

    /**
     * 更新订单支付状态
     */
    boolean updatePaymentStatus(Long orderId, String paymentStatus);

    /**
     * 批量更新订单状态
     */
    boolean batchUpdateOrderStatus(List<Long> orderIds, String status);

    /**
     * 更新订单金额信息
     */
    boolean updateOrderAmounts(
            Long orderId,
            BigDecimal originalAmount,
            BigDecimal discountAmount,
            BigDecimal payableAmount
    );

    /**
     * 删除订单
     */
    boolean deleteById(Long id);
}
