package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import io.by.store.infrastructure.entity.Product;
import io.by.store.infrastructure.entity.ProductCategory;
import io.by.store.infrastructure.mapper.ProductMapper;
import io.by.store.infrastructure.repository.ProductRepository;
import io.by.store.infrastructure.vo.ProductWithCategoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 商品数据访问实现类
 * 使用MyBatis Plus语法实现数据操作
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ProductRepositoryImpl implements ProductRepository {

    private final ProductMapper productMapper;

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public boolean save(Product product) {
        return productMapper.insert(product) > 0;
    }

    @Override
    public boolean updateById(Product product) {
        return productMapper.updateById(product) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return productMapper.deleteById(id) > 0;
    }

    @Override
    public Optional<Product> findById(Long id) {
        return Optional.ofNullable(productMapper.selectById(id));
    }

    @Override
    public Optional<ProductWithCategoryVO> findByIdWithCategory(Long id) {
        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<Product>()
                .selectAll(Product.class)
                .select("pc.name as categoryName")
                .leftJoin(ProductCategory.class, "pc", ProductCategory::getId, Product::getCategoryId)
                .eq(Product::getId, id);

        ProductWithCategoryVO result = productMapper.selectJoinOne(ProductWithCategoryVO.class, wrapper);
        return Optional.ofNullable(result);
    }

    @Override
    public List<Product> findAll() {
        return productMapper.selectList(null);
    }

    // ==================== 业务查询 ====================

    @Override
    public List<Product> findByStoreId(Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStoreId, storeId)
                .orderByDesc(Product::getCreatedAt);
        return productMapper.selectList(wrapper);
    }

    @Override
    public List<ProductWithCategoryVO> findByStoreIdWithCategory(Long storeId) {
        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<Product>()
                .selectAll(Product.class)
                .select("pc.name as categoryName")
                .leftJoin(ProductCategory.class, "pc", ProductCategory::getId, Product::getCategoryId)
                .eq(Product::getStoreId, storeId)
                .orderByDesc(Product::getCreatedAt);

        return productMapper.selectJoinList(ProductWithCategoryVO.class, wrapper);
    }

    @Override
    public List<Product> findByCategoryId(Long categoryId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getCategoryId, categoryId)
                .orderByDesc(Product::getCreatedAt);
        return productMapper.selectList(wrapper);
    }

    @Override
    public List<ProductWithCategoryVO> findByCategoryIdWithCategory(Long categoryId) {
        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<Product>()
                .selectAll(Product.class)
                .select("pc.name as categoryName")
                .leftJoin(ProductCategory.class, "pc", ProductCategory::getId, Product::getCategoryId)
                .eq(Product::getCategoryId, categoryId)
                .orderByDesc(Product::getCreatedAt);

        return productMapper.selectJoinList(ProductWithCategoryVO.class, wrapper);
    }

    @Override
    public List<Product> findByStoreIdAndCategoryId(Long storeId, Long categoryId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStoreId, storeId)
                .eq(Product::getCategoryId, categoryId)
                .orderByDesc(Product::getCreatedAt);
        return productMapper.selectList(wrapper);
    }

    @Override
    public List<ProductWithCategoryVO> findByStoreIdAndCategoryIdWithCategory(Long storeId, Long categoryId) {
        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<Product>()
                .selectAll(Product.class)
                .select("pc.name as categoryName")
                .leftJoin(ProductCategory.class, "pc", ProductCategory::getId, Product::getCategoryId)
                .eq(Product::getStoreId, storeId)
                .eq(Product::getCategoryId, categoryId)
                .orderByDesc(Product::getCreatedAt);

        return productMapper.selectJoinList(ProductWithCategoryVO.class, wrapper);
    }

    @Override
    public Optional<Product> findByProductCodeAndStoreId(String productCode, Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getProductCode, productCode)
                .eq(Product::getStoreId, storeId);
        return Optional.ofNullable(productMapper.selectOne(wrapper));
    }

    @Override
    public List<Product> findByNameLike(String name, Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Product::getName, name)
                .eq(Product::getStoreId, storeId)
                .orderByDesc(Product::getCreatedAt);
        return productMapper.selectList(wrapper);
    }

    @Override
    public List<Product> findByStatus(String status, Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStatus, status)
                .eq(Product::getStoreId, storeId)
                .orderByDesc(Product::getCreatedAt);
        return productMapper.selectList(wrapper);
    }

    @Override
    public IPage<Product> findPage(Integer current, Integer size, String name, Long categoryId,
                                  String status, String productCode, Long storeId) {
        Page<Product> page = new Page<>(current, size);
        LambdaQueryWrapper<Product> wrapper = buildQueryWrapper(name, categoryId, status, productCode, storeId);
        return productMapper.selectPage(page, wrapper);
    }

    @Override
    public IPage<ProductWithCategoryVO> findPageWithCategory(Integer current, Integer size, String name,
                                                           Long categoryId, String status, String productCode, Long storeId) {
        Page<ProductWithCategoryVO> page = new Page<>(current, size);

        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<Product>()
                .selectAll(Product.class)
                .select("pc.name as categoryName")
                .leftJoin(ProductCategory.class, "pc", ProductCategory::getId, Product::getCategoryId);

        // 添加查询条件
        if (storeId != null) {
            wrapper.eq(Product::getStoreId, storeId);
        }

        if (StringUtils.hasText(name)) {
            wrapper.like(Product::getName, name);
        }

        if (categoryId != null) {
            wrapper.eq(Product::getCategoryId, categoryId);
        }

        if (StringUtils.hasText(status)) {
            wrapper.eq(Product::getStatus, status);
        }

        if (StringUtils.hasText(productCode)) {
            wrapper.like(Product::getProductCode, productCode);
        }

        wrapper.orderByDesc(Product::getCreatedAt);

        return productMapper.selectJoinPage(page, ProductWithCategoryVO.class, wrapper);
    }

    @Override
    public IPage<ProductWithCategoryVO> findPageByCategoryIdWithCategory(Integer current, Integer size,
                                                                        Long categoryId, Long storeId, String status) {
        Page<ProductWithCategoryVO> page = new Page<>(current, size);

        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<Product>()
                .selectAll(Product.class)
                .select("pc.name as categoryName")
                .leftJoin(ProductCategory.class, "pc", ProductCategory::getId, Product::getCategoryId)
                .eq(Product::getCategoryId, categoryId)
                .eq(Product::getStoreId, storeId);

        if (StringUtils.hasText(status)) {
            wrapper.eq(Product::getStatus, status);
        }

        wrapper.orderByDesc(Product::getCreatedAt);

        return productMapper.selectJoinPage(page, ProductWithCategoryVO.class, wrapper);
    }

    @Override
    public IPage<ProductWithCategoryVO> searchProductsWithCategoryPage(Integer current, Integer size,
                                                                      Long storeId, String keyword) {
        Page<ProductWithCategoryVO> page = new Page<>(current, size);

        MPJLambdaWrapper<Product> wrapper = new MPJLambdaWrapper<Product>()
                .selectAll(Product.class)
                .select("pc.name as categoryName")
                .leftJoin(ProductCategory.class, "pc", ProductCategory::getId, Product::getCategoryId)
                .eq(Product::getStoreId, storeId)
                .eq(Product::getStatus, "PUBLISHED"); // 只搜索已上架的商品

        if (StringUtils.hasText(keyword)) {
            wrapper.and(w -> w.like(Product::getName, keyword)
                            .or()
                            .like(Product::getDescription, keyword)
                            .or()
                            .like(Product::getProductCode, keyword));
        }

        wrapper.orderByDesc(Product::getCreatedAt);

        return productMapper.selectJoinPage(page, ProductWithCategoryVO.class, wrapper);
    }

    // ==================== 库存相关 ====================

    @Override
    public boolean updateStockQuantity(Long id, Integer quantity) {
        LambdaUpdateWrapper<Product> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Product::getId, id)
                .set(Product::getStockQuantity, quantity)
                .set(Product::getUpdatedAt, LocalDateTime.now());
        return productMapper.update(null, wrapper) > 0;
    }

    @Override
    public boolean increaseStock(Long id, Integer quantity) {
        LambdaUpdateWrapper<Product> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Product::getId, id)
                .setSql("stock_quantity = stock_quantity + " + quantity)
                .set(Product::getUpdatedAt, LocalDateTime.now());
        return productMapper.update(null, wrapper) > 0;
    }

    @Override
    public boolean decreaseStock(Long id, Integer quantity) {
        LambdaUpdateWrapper<Product> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Product::getId, id)
                .setSql("stock_quantity = stock_quantity - " + quantity)
                .set(Product::getUpdatedAt, LocalDateTime.now());
        return productMapper.update(null, wrapper) > 0;
    }

    @Override
    public List<Product> findLowStockProducts(Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStoreId, storeId)
                .apply("stock_quantity <= alert_quantity")
                .orderByAsc(Product::getStockQuantity);
        return productMapper.selectList(wrapper);
    }

    @Override
    public List<Product> findZeroStockProducts(Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStoreId, storeId)
                .eq(Product::getStockQuantity, 0)
                .orderByDesc(Product::getCreatedAt);
        return productMapper.selectList(wrapper);
    }

    @Override
    public List<Product> findByStockRange(Integer minStock, Integer maxStock, Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStoreId, storeId);
        
        if (minStock != null) {
            wrapper.ge(Product::getStockQuantity, minStock);
        }
        if (maxStock != null) {
            wrapper.le(Product::getStockQuantity, maxStock);
        }
        
        wrapper.orderByDesc(Product::getStockQuantity);
        return productMapper.selectList(wrapper);
    }

    // ==================== 价格相关 ====================

    @Override
    public boolean updatePrice(Long id, BigDecimal price) {
        LambdaUpdateWrapper<Product> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Product::getId, id)
                .set(Product::getPrice, price)
                .set(Product::getUpdatedAt, LocalDateTime.now());
        return productMapper.update(null, wrapper) > 0;
    }

    @Override
    public List<Product> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStoreId, storeId);
        
        if (minPrice != null) {
            wrapper.ge(Product::getPrice, minPrice);
        }
        if (maxPrice != null) {
            wrapper.le(Product::getPrice, maxPrice);
        }
        
        wrapper.orderByAsc(Product::getPrice);
        return productMapper.selectList(wrapper);
    }

    // ==================== 状态管理 ====================

    @Override
    public boolean updateStatus(Long id, String status) {
        LambdaUpdateWrapper<Product> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Product::getId, id)
                .set(Product::getStatus, status)
                .set(Product::getUpdatedAt, LocalDateTime.now());
        return productMapper.update(null, wrapper) > 0;
    }

    @Override
    public boolean updateStatusBatch(List<Long> ids, String status) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        LambdaUpdateWrapper<Product> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(Product::getId, ids)
                .set(Product::getStatus, status)
                .set(Product::getUpdatedAt, LocalDateTime.now());
        return productMapper.update(null, wrapper) > 0;
    }

    @Override
    public boolean publishProduct(Long id) {
        return updateStatus(id, Product.Status.PUBLISHED.getCode());
    }

    @Override
    public boolean archiveProduct(Long id) {
        return updateStatus(id, Product.Status.ARCHIVED.getCode());
    }

    @Override
    public boolean markAsSoldOut(Long id) {
        return updateStatus(id, Product.Status.SOLDOUT.getCode());
    }

    // ==================== 统计查询 ====================

    @Override
    public Long countByStoreId(Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStoreId, storeId);
        return productMapper.selectCount(wrapper);
    }

    @Override
    public Long countByCategoryId(Long categoryId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getCategoryId, categoryId);
        return productMapper.selectCount(wrapper);
    }

    @Override
    public Long countByStatus(String status, Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getStatus, status)
                .eq(Product::getStoreId, storeId);
        return productMapper.selectCount(wrapper);
    }

    @Override
    public boolean existsByProductCodeAndStoreIdAndIdNot(String productCode, Long storeId, Long excludeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getProductCode, productCode)
                .eq(Product::getStoreId, storeId)
                .ne(Product::getId, excludeId);
        return productMapper.selectCount(wrapper) > 0;
    }

    // ==================== 批量操作 ====================

    @Override
    public boolean saveBatch(List<Product> products) {
        if (products == null || products.isEmpty()) {
            return false;
        }
        for (Product product : products) {
            productMapper.insert(product);
        }
        return true;
    }

    @Override
    public boolean deleteBatchByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return productMapper.deleteBatchIds(ids) > 0;
    }

    // ==================== 私有方法 ====================

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Product> buildQueryWrapper(String name, Long categoryId, 
                                                         String status, String productCode, Long storeId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        
        if (storeId != null) {
            wrapper.eq(Product::getStoreId, storeId);
        }
        
        if (StringUtils.hasText(name)) {
            wrapper.like(Product::getName, name);
        }
        
        if (categoryId != null) {
            wrapper.eq(Product::getCategoryId, categoryId);
        }
        
        if (StringUtils.hasText(status)) {
            wrapper.eq(Product::getStatus, status);
        }
        
        if (StringUtils.hasText(productCode)) {
            wrapper.like(Product::getProductCode, productCode);
        }
        
        wrapper.orderByDesc(Product::getCreatedAt);
        
        return wrapper;
    }
}
