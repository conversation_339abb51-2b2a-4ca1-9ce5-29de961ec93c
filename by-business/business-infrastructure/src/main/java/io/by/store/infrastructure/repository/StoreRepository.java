package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.infrastructure.entity.Store;

import java.util.List;
import java.util.Optional;

/**
 * 门店数据访问接口
 */
public interface StoreRepository {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 保存门店
     */
    boolean save(Store store);

    /**
     * 根据ID更新门店
     */
    boolean updateById(Store store);

    /**
     * 根据ID删除门店（逻辑删除）
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询门店
     */
    Optional<Store> findById(Long id);

    /**
     * 查询所有门店
     */
    List<Store> findAll();

    // ==================== 业务查询 ====================

    /**
     * 查询激活的门店列表
     */
    List<Store> findActiveStores();

    /**
     * 根据门店名称模糊查询门店
     */
    List<Store> findByNameLike(String name);

    /**
     * 根据电话号码查询门店
     */
    Optional<Store> findByPhoneNumber(String phoneNumber);

    /**
     * 根据门店名称精确查询门店
     */
    Optional<Store> findByName(String name);

    /**
     * 分页查询门店
     */
    IPage<Store> findPage(Integer current, Integer size, String name, Boolean isActive);

    // ==================== 状态管理 ====================

    /**
     * 激活门店
     */
    boolean activateStore(Long id);

    /**
     * 停用门店
     */
    boolean deactivateStore(Long id);

    /**
     * 更新门店状态
     */
    boolean updateStatus(Long id, Boolean isActive);

    /**
     * 批量更新门店状态
     */
    boolean updateStatusBatch(List<Long> ids, Boolean isActive);

    // ==================== 统计查询 ====================

    /**
     * 统计激活的门店总数
     */
    Long countActiveStores();

    /**
     * 统计所有门店总数
     */
    Long countAllStores();

    /**
     * 检查门店名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查电话号码是否存在
     */
    boolean existsByPhoneNumber(String phoneNumber);

    /**
     * 检查门店名称是否存在（排除指定ID）
     */
    boolean existsByNameAndIdNot(String name, Long id);

    /**
     * 检查电话号码是否存在（排除指定ID）
     */
    boolean existsByPhoneNumberAndIdNot(String phoneNumber, Long id);
}
