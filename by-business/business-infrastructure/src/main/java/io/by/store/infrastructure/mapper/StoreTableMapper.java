package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.StoreTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 门店桌台Mapper接口
 */
@Mapper
public interface StoreTableMapper extends BaseMapper<StoreTable>, MPJBaseMapper<StoreTable> {

    /**
     * 根据门店ID查询激活的桌台列表
     */
    @Select("SELECT * FROM store_tables WHERE store_id = #{storeId} AND is_active = true ORDER BY table_number")
    List<StoreTable> findActiveTablesByStoreId(Long storeId);

    /**
     * 根据门店ID和桌台号查询桌台
     */
    @Select("SELECT * FROM store_tables WHERE store_id = #{storeId} AND table_number = #{tableNumber}")
    StoreTable findByStoreIdAndTableNumber(Long storeId, String tableNumber);

    /**
     * 查询门店的桌台总数
     */
    @Select("SELECT COUNT(*) FROM store_tables WHERE store_id = #{storeId} AND is_active = true")
    Integer countActiveTablesByStoreId(Long storeId);

    /**
     * 根据二维码URL查询桌台
     */
    @Select("SELECT * FROM store_tables WHERE qrcode_url = #{qrcodeUrl} AND is_active = true")
    StoreTable findByQrcodeUrl(String qrcodeUrl);
}
