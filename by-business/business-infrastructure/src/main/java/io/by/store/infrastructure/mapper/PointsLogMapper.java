package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.PointsLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 积分变动记录Mapper接口
 */
@Mapper
public interface PointsLogMapper extends BaseMapper<PointsLog>, MPJBaseMapper<PointsLog> {

    /**
     * 根据会员ID查询积分记录
     */
    @Select("SELECT * FROM points_logs WHERE member_id = #{memberId} ORDER BY created_at DESC")
    List<PointsLog> findByMemberId(Long memberId);

    /**
     * 根据变动类型查询积分记录
     */
    @Select("SELECT * FROM points_logs WHERE change_type = #{changeType} ORDER BY created_at DESC")
    List<PointsLog> findByChangeType(String changeType);

    /**
     * 根据订单ID查询积分记录
     */
    @Select("SELECT * FROM points_logs WHERE order_id = #{orderId} ORDER BY created_at DESC")
    List<PointsLog> findByOrderId(Long orderId);

    /**
     * 查询指定时间范围内的积分记录
     */
    @Select("SELECT * FROM points_logs WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<PointsLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询指定积分变动范围内的记录
     */
    @Select("SELECT * FROM points_logs WHERE points_change BETWEEN #{minChange} AND #{maxChange} ORDER BY points_change DESC")
    List<PointsLog> findByPointsChangeRange(Long minChange, Long maxChange);

    /**
     * 统计会员总获得积分
     */
    @Select("SELECT COALESCE(SUM(points_change), 0) FROM points_logs WHERE member_id = #{memberId} AND points_change > 0")
    Long sumEarnedPointsByMemberId(Long memberId);

    /**
     * 统计会员总消费积分
     */
    @Select("SELECT COALESCE(SUM(ABS(points_change)), 0) FROM points_logs WHERE member_id = #{memberId} AND points_change < 0")
    Long sumSpentPointsByMemberId(Long memberId);

    /**
     * 统计今日积分变动总量
     */
    @Select("SELECT COALESCE(SUM(points_change), 0) FROM points_logs WHERE DATE(created_at) = CURRENT_DATE")
    Long sumTodayPointsChange();

    /**
     * 统计本月积分变动总量
     */
    @Select("SELECT COALESCE(SUM(points_change), 0) FROM points_logs WHERE DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE)")
    Long sumMonthPointsChange();

    /**
     * 查询会员最近的积分记录
     */
    @Select("SELECT * FROM points_logs WHERE member_id = #{memberId} ORDER BY created_at DESC LIMIT #{limit}")
    List<PointsLog> findRecentByMemberId(Long memberId, Integer limit);

    /**
     * 查询最近的积分记录
     */
    @Select("SELECT * FROM points_logs ORDER BY created_at DESC LIMIT #{limit}")
    List<PointsLog> findRecentLogs(Integer limit);

    /**
     * 统计积分记录总数
     */
    @Select("SELECT COUNT(*) FROM points_logs")
    Integer countPointsLogs();

    /**
     * 统计会员积分记录数量
     */
    @Select("SELECT COUNT(*) FROM points_logs WHERE member_id = #{memberId}")
    Integer countByMemberId(Long memberId);

    /**
     * 统计指定变动类型的记录数量
     */
    @Select("SELECT COUNT(*) FROM points_logs WHERE change_type = #{changeType}")
    Integer countByChangeType(String changeType);

    /**
     * 查询会员最大单次积分获得
     */
    @Select("SELECT COALESCE(MAX(points_change), 0) FROM points_logs WHERE member_id = #{memberId} AND points_change > 0")
    Long findMaxEarnedPointsByMemberId(Long memberId);

    /**
     * 查询会员最大单次积分消费
     */
    @Select("SELECT COALESCE(MAX(ABS(points_change)), 0) FROM points_logs WHERE member_id = #{memberId} AND points_change < 0")
    Long findMaxSpentPointsByMemberId(Long memberId);

    /**
     * 查询指定会员在指定时间范围内的积分记录
     */
    @Select("SELECT * FROM points_logs WHERE member_id = #{memberId} AND created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<PointsLog> findByMemberIdAndTimeRange(Long memberId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询指定会员指定变动类型的积分记录
     */
    @Select("SELECT * FROM points_logs WHERE member_id = #{memberId} AND change_type = #{changeType} ORDER BY created_at DESC")
    List<PointsLog> findByMemberIdAndChangeType(Long memberId, String changeType);
}
