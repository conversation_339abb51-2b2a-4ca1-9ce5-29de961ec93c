package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.RechargeLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 充值记录Mapper接口
 */
@Mapper
public interface RechargeLogMapper extends BaseMapper<RechargeLog>, MPJBaseMapper<RechargeLog> {

    /**
     * 根据充值订单号查询记录
     */
    @Select("SELECT * FROM recharge_logs WHERE recharge_order_number = #{orderNumber}")
    RechargeLog findByOrderNumber(String orderNumber);

    /**
     * 根据会员ID查询充值记录
     */
    @Select("SELECT * FROM recharge_logs WHERE member_id = #{memberId} ORDER BY created_at DESC")
    List<RechargeLog> findByMemberId(Long memberId);

    /**
     * 根据状态查询充值记录
     */
    @Select("SELECT * FROM recharge_logs WHERE status = #{status} ORDER BY created_at DESC")
    List<RechargeLog> findByStatus(String status);

    /**
     * 根据支付方式查询充值记录
     */
    @Select("SELECT * FROM recharge_logs WHERE payment_method = #{paymentMethod} ORDER BY created_at DESC")
    List<RechargeLog> findByPaymentMethod(String paymentMethod);

    /**
     * 根据员工ID查询充值记录（线下充值）
     */
    @Select("SELECT * FROM recharge_logs WHERE staff_id = #{staffId} ORDER BY created_at DESC")
    List<RechargeLog> findByStaffId(Long staffId);

    /**
     * 根据第三方交易流水号查询记录
     */
    @Select("SELECT * FROM recharge_logs WHERE payment_transaction_id = #{transactionId}")
    RechargeLog findByTransactionId(String transactionId);

    /**
     * 查询指定时间范围内的充值记录
     */
    @Select("SELECT * FROM recharge_logs WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<RechargeLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询指定金额范围内的充值记录
     */
    @Select("SELECT * FROM recharge_logs WHERE amount BETWEEN #{minAmount} AND #{maxAmount} ORDER BY amount DESC")
    List<RechargeLog> findByAmountRange(BigDecimal minAmount, BigDecimal maxAmount);

    /**
     * 统计会员总充值金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM recharge_logs WHERE member_id = #{memberId} AND status = 'successful'")
    BigDecimal sumSuccessfulAmountByMemberId(Long memberId);

    /**
     * 统计指定状态的充值记录数量
     */
    @Select("SELECT COUNT(*) FROM recharge_logs WHERE status = #{status}")
    Integer countByStatus(String status);

    /**
     * 统计指定支付方式的充值记录数量
     */
    @Select("SELECT COUNT(*) FROM recharge_logs WHERE payment_method = #{paymentMethod}")
    Integer countByPaymentMethod(String paymentMethod);

    /**
     * 查询最近的充值记录
     */
    @Select("SELECT * FROM recharge_logs ORDER BY created_at DESC LIMIT #{limit}")
    List<RechargeLog> findRecentLogs(Integer limit);

    /**
     * 查询待处理的充值记录
     */
    @Select("SELECT * FROM recharge_logs WHERE status = 'pending' ORDER BY created_at ASC")
    List<RechargeLog> findPendingLogs();

    /**
     * 统计今日充值总金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM recharge_logs WHERE DATE(created_at) = CURRENT_DATE AND status = 'successful'")
    BigDecimal sumTodaySuccessfulAmount();

    /**
     * 统计本月充值总金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM recharge_logs WHERE DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE) AND status = 'successful'")
    BigDecimal sumMonthSuccessfulAmount();

    /**
     * 查询会员最近一次成功充值记录
     */
    @Select("SELECT * FROM recharge_logs WHERE member_id = #{memberId} AND status = 'successful' ORDER BY created_at DESC LIMIT 1")
    RechargeLog findLastSuccessfulByMemberId(Long memberId);
}
