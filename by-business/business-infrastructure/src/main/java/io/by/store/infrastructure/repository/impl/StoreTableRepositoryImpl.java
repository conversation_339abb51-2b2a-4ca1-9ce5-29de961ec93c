package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.StoreTable;
import io.by.store.infrastructure.mapper.StoreTableMapper;
import io.by.store.infrastructure.repository.StoreTableRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 门店桌台数据访问实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class StoreTableRepositoryImpl implements StoreTableRepository {

    private final StoreTableMapper storeTableMapper;

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public boolean save(StoreTable storeTable) {
        try {
            return storeTableMapper.insert(storeTable) > 0;
        } catch (Exception e) {
            log.error("保存桌台失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateById(StoreTable storeTable) {
        try {
            storeTable.setUpdatedAt(LocalDateTime.now());
            return storeTableMapper.updateById(storeTable) > 0;
        } catch (Exception e) {
            log.error("更新桌台失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteById(Long id) {
        try {
            // 逻辑删除：设置 isActive 为 false
            LambdaUpdateWrapper<StoreTable> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(StoreTable::getId, id)
                    .set(StoreTable::getIsActive, false)
                    .set(StoreTable::getUpdatedAt, LocalDateTime.now());
            return storeTableMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("删除桌台失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Optional<StoreTable> findById(Long id) {
        try {
            StoreTable storeTable = storeTableMapper.selectById(id);
            return Optional.ofNullable(storeTable);
        } catch (Exception e) {
            log.error("根据ID查询桌台失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<StoreTable> findAll() {
        try {
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.orderByAsc(StoreTable::getStoreId)
                    .orderByAsc(StoreTable::getTableNumber);
            return storeTableMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("查询所有桌台失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    // ==================== 业务查询 ====================

    @Override
    public List<StoreTable> findActiveTablesByStoreId(Long storeId) {
        try {
            return storeTableMapper.findActiveTablesByStoreId(storeId);
        } catch (Exception e) {
            log.error("根据门店ID查询激活桌台失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<StoreTable> findAllTablesByStoreId(Long storeId) {
        try {
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreTable::getStoreId, storeId)
                    .orderByAsc(StoreTable::getTableNumber);
            return storeTableMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("根据门店ID查询所有桌台失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public Optional<StoreTable> findByStoreIdAndTableNumber(Long storeId, String tableNumber) {
        try {
            StoreTable storeTable = storeTableMapper.findByStoreIdAndTableNumber(storeId, tableNumber);
            return Optional.ofNullable(storeTable);
        } catch (Exception e) {
            log.error("根据门店ID和桌台号查询桌台失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public Optional<StoreTable> findByQrcodeUrl(String qrcodeUrl) {
        try {
            StoreTable storeTable = storeTableMapper.findByQrcodeUrl(qrcodeUrl);
            return Optional.ofNullable(storeTable);
        } catch (Exception e) {
            log.error("根据二维码URL查询桌台失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public IPage<StoreTable> findPage(Integer current, Integer size, Long storeId, String tableNumber, Boolean isActive) {
        try {
            Page<StoreTable> page = new Page<>(current, size);
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            
            if (storeId != null) {
                queryWrapper.eq(StoreTable::getStoreId, storeId);
            }
            if (StringUtils.hasText(tableNumber)) {
                queryWrapper.like(StoreTable::getTableNumber, tableNumber);
            }
            if (isActive != null) {
                queryWrapper.eq(StoreTable::getIsActive, isActive);
            }
            
            queryWrapper.orderByAsc(StoreTable::getStoreId)
                    .orderByAsc(StoreTable::getTableNumber);
            return storeTableMapper.selectPage(page, queryWrapper);
        } catch (Exception e) {
            log.error("分页查询桌台失败: {}", e.getMessage(), e);
            return new Page<>(current, size);
        }
    }

    // ==================== 状态管理 ====================

    @Override
    public boolean activateTable(Long id) {
        return updateStatus(id, true);
    }

    @Override
    public boolean deactivateTable(Long id) {
        return updateStatus(id, false);
    }

    @Override
    public boolean updateStatus(Long id, Boolean isActive) {
        try {
            LambdaUpdateWrapper<StoreTable> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(StoreTable::getId, id)
                    .set(StoreTable::getIsActive, isActive)
                    .set(StoreTable::getUpdatedAt, LocalDateTime.now());
            return storeTableMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("更新桌台状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateStatusBatch(List<Long> ids, Boolean isActive) {
        try {
            LambdaUpdateWrapper<StoreTable> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(StoreTable::getId, ids)
                    .set(StoreTable::getIsActive, isActive)
                    .set(StoreTable::getUpdatedAt, LocalDateTime.now());
            return storeTableMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("批量更新桌台状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateStatusByStoreId(Long storeId, Boolean isActive) {
        try {
            LambdaUpdateWrapper<StoreTable> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(StoreTable::getStoreId, storeId)
                    .set(StoreTable::getIsActive, isActive)
                    .set(StoreTable::getUpdatedAt, LocalDateTime.now());
            return storeTableMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("根据门店ID批量更新桌台状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 统计查询 ====================

    @Override
    public Integer countActiveTablesByStoreId(Long storeId) {
        try {
            return storeTableMapper.countActiveTablesByStoreId(storeId);
        } catch (Exception e) {
            log.error("统计门店激活桌台数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Integer countAllTablesByStoreId(Long storeId) {
        try {
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreTable::getStoreId, storeId);
            return Math.toIntExact(storeTableMapper.selectCount(queryWrapper));
        } catch (Exception e) {
            log.error("统计门店所有桌台数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Long countAllActiveTables() {
        try {
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreTable::getIsActive, true);
            return storeTableMapper.selectCount(queryWrapper);
        } catch (Exception e) {
            log.error("统计所有激活桌台数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    @Override
    public Long countAllTables() {
        try {
            return storeTableMapper.selectCount(null);
        } catch (Exception e) {
            log.error("统计所有桌台数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    // ==================== 唯一性检查 ====================

    @Override
    public boolean existsByStoreIdAndTableNumber(Long storeId, String tableNumber) {
        try {
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreTable::getStoreId, storeId)
                    .eq(StoreTable::getTableNumber, tableNumber);
            return storeTableMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查门店内桌台号是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByStoreIdAndTableNumberAndIdNot(Long storeId, String tableNumber, Long id) {
        try {
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreTable::getStoreId, storeId)
                    .eq(StoreTable::getTableNumber, tableNumber)
                    .ne(StoreTable::getId, id);
            return storeTableMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查门店内桌台号是否存在（排除指定ID）失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByQrcodeUrl(String qrcodeUrl) {
        try {
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreTable::getQrcodeUrl, qrcodeUrl);
            return storeTableMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查二维码URL是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean existsByQrcodeUrlAndIdNot(String qrcodeUrl, Long id) {
        try {
            LambdaQueryWrapper<StoreTable> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreTable::getQrcodeUrl, qrcodeUrl)
                    .ne(StoreTable::getId, id);
            return storeTableMapper.selectCount(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查二维码URL是否存在（排除指定ID）失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 批量操作 ====================

    @Override
    public boolean saveBatch(List<StoreTable> storeTables) {
        try {
            for (StoreTable storeTable : storeTables) {
                storeTableMapper.insert(storeTable);
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存桌台失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteByStoreId(Long storeId) {
        try {
            // 逻辑删除：设置门店下所有桌台的 isActive 为 false
            LambdaUpdateWrapper<StoreTable> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(StoreTable::getStoreId, storeId)
                    .set(StoreTable::getIsActive, false)
                    .set(StoreTable::getUpdatedAt, LocalDateTime.now());
            return storeTableMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("根据门店ID删除所有桌台失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
