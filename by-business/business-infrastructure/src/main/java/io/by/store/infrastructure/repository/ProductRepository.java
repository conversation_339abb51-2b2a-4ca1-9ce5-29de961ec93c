package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.infrastructure.entity.Product;
import io.by.store.infrastructure.vo.ProductWithCategoryVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 商品数据访问接口
 */
public interface ProductRepository {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 保存商品
     */
    boolean save(Product product);

    /**
     * 根据ID更新商品
     */
    boolean updateById(Product product);

    /**
     * 根据ID删除商品
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询商品
     */
    Optional<Product> findById(Long id);

    /**
     * 根据ID查询商品（联表查询，包含分类名称）
     */
    Optional<ProductWithCategoryVO> findByIdWithCategory(Long id);

    /**
     * 查询所有商品
     */
    List<Product> findAll();

    // ==================== 业务查询 ====================

    /**
     * 根据门店ID查询商品列表
     */
    List<Product> findByStoreId(Long storeId);

    /**
     * 根据门店ID查询商品列表（联表查询，包含分类名称）
     */
    List<ProductWithCategoryVO> findByStoreIdWithCategory(Long storeId);

    /**
     * 根据分类ID查询商品列表
     */
    List<Product> findByCategoryId(Long categoryId);

    /**
     * 根据分类ID查询商品列表（联表查询，包含分类名称）
     */
    List<ProductWithCategoryVO> findByCategoryIdWithCategory(Long categoryId);

    /**
     * 根据门店ID和分类ID查询商品列表
     */
    List<Product> findByStoreIdAndCategoryId(Long storeId, Long categoryId);

    /**
     * 根据门店ID和分类ID查询商品列表（联表查询，包含分类名称）
     */
    List<ProductWithCategoryVO> findByStoreIdAndCategoryIdWithCategory(Long storeId, Long categoryId);

    /**
     * 根据商品编码查询商品
     */
    Optional<Product> findByProductCodeAndStoreId(String productCode, Long storeId);

    /**
     * 根据名称模糊查询商品
     */
    List<Product> findByNameLike(String name, Long storeId);

    /**
     * 根据状态查询商品
     */
    List<Product> findByStatus(String status, Long storeId);

    /**
     * 分页查询商品
     */
    IPage<Product> findPage(Integer current, Integer size, String name, Long categoryId,
                           String status, String productCode, Long storeId);

    /**
     * 分页查询商品（联表查询，包含分类名称）
     */
    IPage<ProductWithCategoryVO> findPageWithCategory(Integer current, Integer size, String name,
                                                     Long categoryId, String status, String productCode, Long storeId);

    /**
     * 根据分类ID分页查询商品（联表查询，包含分类名称）
     */
    IPage<ProductWithCategoryVO> findPageByCategoryIdWithCategory(Integer current, Integer size,
                                                                 Long categoryId, Long storeId, String status);

    /**
     * 搜索商品（分页，联表查询，包含分类名称）
     */
    IPage<ProductWithCategoryVO> searchProductsWithCategoryPage(Integer current, Integer size,
                                                               Long storeId, String keyword);

    // ==================== 库存相关 ====================

    /**
     * 更新库存数量
     */
    boolean updateStockQuantity(Long id, Integer quantity);

    /**
     * 增加库存数量
     */
    boolean increaseStock(Long id, Integer quantity);

    /**
     * 减少库存数量
     */
    boolean decreaseStock(Long id, Integer quantity);

    /**
     * 查询库存不足的商品（库存 <= 预警阈值）
     */
    List<Product> findLowStockProducts(Long storeId);

    /**
     * 查询零库存商品
     */
    List<Product> findZeroStockProducts(Long storeId);

    /**
     * 根据库存范围查询商品
     */
    List<Product> findByStockRange(Integer minStock, Integer maxStock, Long storeId);

    // ==================== 价格相关 ====================

    /**
     * 更新商品价格
     */
    boolean updatePrice(Long id, BigDecimal price);

    /**
     * 根据价格范围查询商品
     */
    List<Product> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Long storeId);

    // ==================== 状态管理 ====================

    /**
     * 更新商品状态
     */
    boolean updateStatus(Long id, String status);

    /**
     * 批量更新商品状态
     */
    boolean updateStatusBatch(List<Long> ids, String status);

    /**
     * 上架商品
     */
    boolean publishProduct(Long id);

    /**
     * 下架商品
     */
    boolean archiveProduct(Long id);

    /**
     * 标记为售罄
     */
    boolean markAsSoldOut(Long id);

    // ==================== 统计查询 ====================

    /**
     * 统计门店商品总数
     */
    Long countByStoreId(Long storeId);

    /**
     * 统计分类下的商品数量
     */
    Long countByCategoryId(Long categoryId);

    /**
     * 统计各状态商品数量
     */
    Long countByStatus(String status, Long storeId);

    /**
     * 检查商品编码是否存在（排除指定ID）
     */
    boolean existsByProductCodeAndStoreIdAndIdNot(String productCode, Long storeId, Long excludeId);

    // ==================== 批量操作 ====================

    /**
     * 批量保存商品
     */
    boolean saveBatch(List<Product> products);

    /**
     * 批量删除商品
     */
    boolean deleteBatchByIds(List<Long> ids);
}
