package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.ProductCategory;
import io.by.store.infrastructure.mapper.ProductCategoryMapper;
import io.by.store.infrastructure.repository.ProductCategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 商品分类数据访问实现类
 * 使用MyBatis Plus语法实现数据操作
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ProductCategoryRepositoryImpl implements ProductCategoryRepository {

    private final ProductCategoryMapper categoryMapper;

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public boolean save(ProductCategory category) {
        return categoryMapper.insert(category) > 0;
    }

    @Override
    public boolean updateById(ProductCategory category) {
        return categoryMapper.updateById(category) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return categoryMapper.deleteById(id) > 0;
    }

    @Override
    public Optional<ProductCategory> findById(Long id) {
        return Optional.ofNullable(categoryMapper.selectById(id));
    }

    @Override
    public List<ProductCategory> findAll() {
        return categoryMapper.selectList(null);
    }

    // ==================== 业务查询 ====================

    @Override
    public List<ProductCategory> findByStoreId(Long storeId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getStoreId, storeId)
                .orderByAsc(ProductCategory::getSortOrder)
                .orderByAsc(ProductCategory::getId);
        return categoryMapper.selectList(wrapper);
    }

    @Override
    public List<ProductCategory> findByParentId(Long parentId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getParentId, parentId)
                .orderByAsc(ProductCategory::getSortOrder)
                .orderByAsc(ProductCategory::getId);
        return categoryMapper.selectList(wrapper);
    }

    @Override
    public List<ProductCategory> findByStoreIdAndParentId(Long storeId, Long parentId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getStoreId, storeId)
                .eq(ProductCategory::getParentId, parentId)
                .orderByAsc(ProductCategory::getSortOrder)
                .orderByAsc(ProductCategory::getId);
        return categoryMapper.selectList(wrapper);
    }

    @Override
    public Optional<ProductCategory> findByNameAndStoreId(String name, Long storeId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getName, name)
                .eq(ProductCategory::getStoreId, storeId);
        return Optional.ofNullable(categoryMapper.selectOne(wrapper));
    }

    @Override
    public List<ProductCategory> findByNameLike(String name, Long storeId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(ProductCategory::getName, name)
                .eq(ProductCategory::getStoreId, storeId)
                .orderByAsc(ProductCategory::getSortOrder)
                .orderByAsc(ProductCategory::getId);
        return categoryMapper.selectList(wrapper);
    }

    @Override
    public List<ProductCategory> findTopLevelCategories(Long storeId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getStoreId, storeId)
                .eq(ProductCategory::getParentId, 0L)
                .orderByAsc(ProductCategory::getSortOrder)
                .orderByAsc(ProductCategory::getId);
        return categoryMapper.selectList(wrapper);
    }

    @Override
    public IPage<ProductCategory> findPage(Integer current, Integer size, String name, Long parentId, Long storeId) {
        Page<ProductCategory> page = new Page<>(current, size);
        LambdaQueryWrapper<ProductCategory> wrapper = buildQueryWrapper(name, parentId, storeId);
        return categoryMapper.selectPage(page, wrapper);
    }

    // ==================== 排序相关 ====================

    @Override
    public Integer findMaxSortOrder(Long storeId, Long parentId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getStoreId, storeId)
                .eq(ProductCategory::getParentId, parentId)
                .orderByDesc(ProductCategory::getSortOrder)
                .last("LIMIT 1");
        ProductCategory category = categoryMapper.selectOne(wrapper);
        return category != null && category.getSortOrder() != null ? category.getSortOrder() : 0;
    }

    @Override
    public boolean updateSortOrder(Long id, Integer sortOrder) {
        LambdaUpdateWrapper<ProductCategory> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProductCategory::getId, id)
                .set(ProductCategory::getSortOrder, sortOrder)
                .set(ProductCategory::getUpdatedAt, LocalDateTime.now());
        return categoryMapper.update(null, wrapper) > 0;
    }

    // ==================== 统计查询 ====================

    @Override
    public Long countByStoreId(Long storeId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getStoreId, storeId);
        return categoryMapper.selectCount(wrapper);
    }

    @Override
    public Long countByParentId(Long parentId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getParentId, parentId);
        return categoryMapper.selectCount(wrapper);
    }

    @Override
    public boolean existsByNameAndStoreIdAndIdNot(String name, Long storeId, Long excludeId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCategory::getName, name)
                .eq(ProductCategory::getStoreId, storeId)
                .ne(ProductCategory::getId, excludeId);
        return categoryMapper.selectCount(wrapper) > 0;
    }

    // ==================== 批量操作 ====================

    @Override
    public boolean saveBatch(List<ProductCategory> categories) {
        if (categories == null || categories.isEmpty()) {
            return false;
        }
        for (ProductCategory category : categories) {
            categoryMapper.insert(category);
        }
        return true;
    }

    @Override
    public boolean deleteBatchByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return categoryMapper.deleteBatchIds(ids) > 0;
    }

    // ==================== 私有方法 ====================

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ProductCategory> buildQueryWrapper(String name, Long parentId, Long storeId) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<>();
        
        if (storeId != null) {
            wrapper.eq(ProductCategory::getStoreId, storeId);
        }
        
        if (StringUtils.hasText(name)) {
            wrapper.like(ProductCategory::getName, name);
        }
        
        if (parentId != null) {
            wrapper.eq(ProductCategory::getParentId, parentId);
        }
        
        wrapper.orderByAsc(ProductCategory::getSortOrder)
                .orderByAsc(ProductCategory::getId);
        
        return wrapper;
    }
}
