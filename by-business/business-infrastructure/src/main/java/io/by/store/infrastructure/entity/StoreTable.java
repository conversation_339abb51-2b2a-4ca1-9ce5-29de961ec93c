package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 门店桌台实体类
 */
@Data
@TableName("store_tables")
public class StoreTable {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 桌台号
     */
    @TableField("table_number")
    private String tableNumber;

    /**
     * 二维码URL
     */
    @TableField("qrcode_url")
    private String qrcodeUrl;

    /**
     * 是否激活：true-激活，false-停用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 门店ID - 用于数据隔离
     */
    @TableField("store_id")
    private Long storeId;
}
