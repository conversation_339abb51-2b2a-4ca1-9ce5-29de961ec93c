package io.by.store.infrastructure.repository;

import io.by.store.infrastructure.entity.OrderDiscount;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 订单优惠明细Repository接口
 */
public interface OrderDiscountRepository {

    /**
     * 保存订单优惠明细
     */
    boolean save(OrderDiscount orderDiscount);

    /**
     * 批量保存订单优惠明细
     */
    boolean saveBatch(List<OrderDiscount> orderDiscounts);

    /**
     * 更新订单优惠明细
     */
    boolean updateById(OrderDiscount orderDiscount);

    /**
     * 根据ID查询订单优惠明细
     */
    Optional<OrderDiscount> findById(Long id);

    /**
     * 根据订单ID查询优惠明细列表
     */
    List<OrderDiscount> findByOrderId(Long orderId);

    /**
     * 根据订单ID列表查询优惠明细列表
     */
    List<OrderDiscount> findByOrderIds(List<Long> orderIds);

    /**
     * 根据优惠类型查询优惠明细列表
     */
    List<OrderDiscount> findByDiscountType(String discountType);

    /**
     * 计算订单的总优惠金额
     */
    BigDecimal sumDiscountAmountByOrderId(Long orderId);

    /**
     * 删除订单优惠明细
     */
    boolean deleteById(Long id);

    /**
     * 根据订单ID删除优惠明细
     */
    boolean deleteByOrderId(Long orderId);
}
