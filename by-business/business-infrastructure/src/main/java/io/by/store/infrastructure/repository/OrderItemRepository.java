package io.by.store.infrastructure.repository;

import io.by.store.infrastructure.entity.OrderItem;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 订单商品项Repository接口
 */
public interface OrderItemRepository {

    /**
     * 保存订单商品项
     */
    boolean save(OrderItem orderItem);

    /**
     * 批量保存订单商品项
     */
    boolean saveBatch(List<OrderItem> orderItems);

    /**
     * 更新订单商品项
     */
    boolean updateById(OrderItem orderItem);

    /**
     * 根据ID查询订单商品项
     */
    Optional<OrderItem> findById(Long id);

    /**
     * 根据订单ID查询订单商品项列表
     */
    List<OrderItem> findByOrderId(Long orderId);

    /**
     * 根据订单ID列表查询订单商品项列表
     */
    List<OrderItem> findByOrderIds(List<Long> orderIds);

    /**
     * 根据商品ID查询订单商品项列表
     */
    List<OrderItem> findByProductId(Long productId);

    /**
     * 统计指定商品的销售数量
     */
    Integer sumQuantityByProductId(
            Long productId,
            LocalDateTime startTime,
            LocalDateTime endTime
    );

    /**
     * 统计指定时间范围内的商品销售排行
     */
    List<OrderItem> getProductSalesRanking(
            LocalDateTime startTime,
            LocalDateTime endTime,
            Integer limit
    );

    /**
     * 删除订单商品项
     */
    boolean deleteById(Long id);

    /**
     * 根据订单ID删除订单商品项
     */
    boolean deleteByOrderId(Long orderId);
}
