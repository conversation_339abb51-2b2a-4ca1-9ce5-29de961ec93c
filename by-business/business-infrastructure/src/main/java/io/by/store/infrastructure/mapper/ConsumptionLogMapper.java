package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.yulichang.base.MPJBaseMapper;
import io.by.store.infrastructure.entity.ConsumptionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 余额消费记录Mapper接口
 */
@Mapper
public interface ConsumptionLogMapper extends BaseMapper<ConsumptionLog>, MPJBaseMapper<ConsumptionLog> {

    /**
     * 根据会员ID查询消费记录
     */
    @Select("SELECT * FROM consumption_logs WHERE member_id = #{memberId} ORDER BY created_at DESC")
    List<ConsumptionLog> findByMemberId(Long memberId);

    /**
     * 根据订单ID查询消费记录
     */
    @Select("SELECT * FROM consumption_logs WHERE order_id = #{orderId}")
    ConsumptionLog findByOrderId(Long orderId);

    /**
     * 查询指定时间范围内的消费记录
     */
    @Select("SELECT * FROM consumption_logs WHERE created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<ConsumptionLog> findByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询指定金额范围内的消费记录
     */
    @Select("SELECT * FROM consumption_logs WHERE amount_consumed BETWEEN #{minAmount} AND #{maxAmount} ORDER BY amount_consumed DESC")
    List<ConsumptionLog> findByAmountRange(BigDecimal minAmount, BigDecimal maxAmount);

    /**
     * 统计会员总消费金额
     */
    @Select("SELECT COALESCE(SUM(amount_consumed), 0) FROM consumption_logs WHERE member_id = #{memberId}")
    BigDecimal sumConsumedAmountByMemberId(Long memberId);

    /**
     * 统计今日消费总金额
     */
    @Select("SELECT COALESCE(SUM(amount_consumed), 0) FROM consumption_logs WHERE DATE(created_at) = CURRENT_DATE")
    BigDecimal sumTodayConsumedAmount();

    /**
     * 统计本月消费总金额
     */
    @Select("SELECT COALESCE(SUM(amount_consumed), 0) FROM consumption_logs WHERE DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE)")
    BigDecimal sumMonthConsumedAmount();

    /**
     * 查询会员最近的消费记录
     */
    @Select("SELECT * FROM consumption_logs WHERE member_id = #{memberId} ORDER BY created_at DESC LIMIT #{limit}")
    List<ConsumptionLog> findRecentByMemberId(Long memberId, Integer limit);

    /**
     * 查询最近的消费记录
     */
    @Select("SELECT * FROM consumption_logs ORDER BY created_at DESC LIMIT #{limit}")
    List<ConsumptionLog> findRecentLogs(Integer limit);

    /**
     * 统计消费记录总数
     */
    @Select("SELECT COUNT(*) FROM consumption_logs")
    Integer countConsumptionLogs();

    /**
     * 统计会员消费记录数量
     */
    @Select("SELECT COUNT(*) FROM consumption_logs WHERE member_id = #{memberId}")
    Integer countByMemberId(Long memberId);

    /**
     * 查询会员最大单次消费金额
     */
    @Select("SELECT COALESCE(MAX(amount_consumed), 0) FROM consumption_logs WHERE member_id = #{memberId}")
    BigDecimal findMaxConsumedAmountByMemberId(Long memberId);

    /**
     * 查询会员平均消费金额
     */
    @Select("SELECT COALESCE(AVG(amount_consumed), 0) FROM consumption_logs WHERE member_id = #{memberId}")
    BigDecimal findAvgConsumedAmountByMemberId(Long memberId);

    /**
     * 查询指定会员在指定时间范围内的消费记录
     */
    @Select("SELECT * FROM consumption_logs WHERE member_id = #{memberId} AND created_at BETWEEN #{startTime} AND #{endTime} ORDER BY created_at DESC")
    List<ConsumptionLog> findByMemberIdAndTimeRange(Long memberId, LocalDateTime startTime, LocalDateTime endTime);
}
