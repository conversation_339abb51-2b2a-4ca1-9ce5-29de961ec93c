package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单商品项实体类
 */
@Data
@TableName("order_items")
public class OrderItem {

    /**
     * 订单商品项ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 关联的商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品名称快照 (防止商品信息变更)
     */
    @TableField("product_name")
    private String productName;

    /**
     * 商品数量
     */
    @TableField("quantity")
    private Integer quantity;

    /**
     * 商品单价快照 (下单时的价格)
     */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * 该项商品总价 (quantity * unit_price)
     */
    @TableField("total_price")
    private BigDecimal totalPrice;
}
