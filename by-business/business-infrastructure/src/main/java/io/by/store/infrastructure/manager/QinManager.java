package io.by.store.infrastructure.manager;

import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.storage.model.FileInfo;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 七牛云文件管理器
 * 封装文件上传、下载、删除等功能
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "qiniu.enabled", havingValue = "true", matchIfMissing = false)
public class QinManager {

    private final Auth auth;
    private final UploadManager uploadManager;
    private final BucketManager bucketManager;

    @Value("${qiniu.bucket}")
    private String bucket;

    @Value("${qiniu.domain}")
    private String domain;


    /**
     * 上传文件（字节数组）
     *
     * @param data     文件数据
     * @param fileName 文件名
     * @return 文件访问URL
     */
    public String uploadFile(byte[] data, String fileName) {
        if (data == null || data.length == 0) {
            throw new IllegalArgumentException("上传数据不能为空");
        }

        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            // 获取上传凭证
            String upToken = auth.uploadToken(bucket);

            // 执行上传
            Response response = uploadManager.put(data, fileName, upToken);
            DefaultPutRet putRet = response.jsonToObject(DefaultPutRet.class);

            log.info("文件上传成功: fileName={}, key={}, hash={}", fileName, putRet.key, putRet.hash);

            return getFileUrl(putRet.key);

        } catch (QiniuException e) {
            log.error("文件上传失败: fileName={}, error={}", fileName, e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 上传文件（输入流）
     *
     * @param inputStream 输入流
     * @param fileName    文件名
     * @return 文件访问URL
     */
    public String uploadFile(InputStream inputStream, String fileName) {
        if (inputStream == null) {
            throw new IllegalArgumentException("输入流不能为空");
        }

        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            // 获取上传凭证
            String upToken = auth.uploadToken(bucket);

            // 执行上传
            Response response = uploadManager.put(inputStream, fileName, upToken, null, null);
            DefaultPutRet putRet = response.jsonToObject(DefaultPutRet.class);

            log.info("文件上传成功: fileName={}, key={}, hash={}", fileName, putRet.key, putRet.hash);

            return getFileUrl(putRet.key);

        } catch (QiniuException e) {
            log.error("文件上传失败: fileName={}, error={}", fileName, e.getMessage(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除文件
     *
     * @param fileName 文件名（key）
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            Response response = bucketManager.delete(bucket, fileName);
            boolean success = response.isOK();

            if (success) {
                log.info("文件删除成功: fileName={}", fileName);
            } else {
                log.warn("文件删除失败: fileName={}, response={}", fileName, response.toString());
            }

            return success;

        } catch (QiniuException e) {
            log.error("文件删除失败: fileName={}, error={}", fileName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取文件信息
     *
     * @param fileName 文件名（key）
     * @return 文件信息
     */
    public FileInfo getFileInfo(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        try {
            FileInfo fileInfo = bucketManager.stat(bucket, fileName);
            log.info("获取文件信息成功: fileName={}, size={}, mimeType={}",
                    fileName, fileInfo.fsize, fileInfo.mimeType);
            return fileInfo;

        } catch (QiniuException e) {
            log.error("获取文件信息失败: fileName={}, error={}", fileName, e.getMessage(), e);
            throw new RuntimeException("获取文件信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param fileName 文件名（key）
     * @return 文件是否存在
     */
    public boolean fileExists(String fileName) {
        try {
            getFileInfo(fileName);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成上传凭证
     *
     * @param fileName 文件名（可选）
     * @param expires  过期时间（秒）
     * @return 上传凭证
     */
    public String generateUploadToken(String fileName, long expires) {
        StringMap putPolicy = new StringMap();
        putPolicy.put("returnBody", "{\"key\":\"$(key)\",\"hash\":\"$(etag)\",\"bucket\":\"$(bucket)\",\"fsize\":$(fsize)}");

        if (fileName != null && !fileName.trim().isEmpty()) {
            return auth.uploadToken(bucket, fileName, expires, putPolicy);
        } else {
            return auth.uploadToken(bucket, null, expires, putPolicy);
        }
    }

    /**
     * 生成上传凭证（默认1小时过期）
     *
     * @param fileName 文件名（可选）
     * @return 上传凭证
     */
    public String generateUploadToken(String fileName) {
        return generateUploadToken(fileName, 3600); // 默认1小时
    }

    /**
     * 生成上传凭证（默认1小时过期，不指定文件名）
     *
     * @return 上传凭证
     */
    public String generateUploadToken() {
        return generateUploadToken(null, 3600);
    }

    /**
     * 获取文件的完整访问URL
     *
     * @param fileName 文件名（key）
     * @return 完整的访问URL
     */
    public String getFileUrl(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        // 确保domain以http://或https://开头
        String baseUrl = domain;
        if (!baseUrl.startsWith("http://") && !baseUrl.startsWith("https://")) {
            baseUrl = "https://" + baseUrl;
        }

        // 确保domain不以/结尾
        if (baseUrl.endsWith("/")) {
            baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
        }

        return baseUrl + "/" + fileName;
    }

    /**
     * 生成唯一的文件名
     *
     * @param originalFileName 原始文件名
     * @return 生成的唯一文件名
     */
    private String generateFileName(String originalFileName) {
        // 获取文件扩展名
        String extension = "";
        if (originalFileName != null && originalFileName.contains(".")) {
            extension = originalFileName.substring(originalFileName.lastIndexOf("."));
        }

        // 生成时间戳路径：年/月/日/UUID.扩展名
        LocalDateTime now = LocalDateTime.now();
        String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String uuid = UUID.randomUUID().toString().replace("-", "");

        return datePath + "/" + uuid + extension;
    }

    /**
     * 批量删除文件
     *
     * @param fileNames 文件名列表
     * @return 删除结果（文件名 -> 是否成功）
     */
    public java.util.Map<String, Boolean> deleteFiles(java.util.List<String> fileNames) {
        java.util.Map<String, Boolean> results = new java.util.HashMap<>();

        if (fileNames == null || fileNames.isEmpty()) {
            return results;
        }

        for (String fileName : fileNames) {
            try {
                boolean success = deleteFile(fileName);
                results.put(fileName, success);
            } catch (Exception e) {
                log.error("批量删除文件失败: fileName={}, error={}", fileName, e.getMessage(), e);
                results.put(fileName, false);
            }
        }

        return results;
    }
}
