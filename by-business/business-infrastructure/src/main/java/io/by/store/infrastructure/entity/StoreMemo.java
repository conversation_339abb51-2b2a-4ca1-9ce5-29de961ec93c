package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 门店备忘录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("store_memos")
public class StoreMemo extends BaseEntity {

    /**
     * 备忘录ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 备忘录的具体内容
     */
    @TableField("content")
    private String content;

    /**
     * 当前状态: TODO(待办), DONE(已完成)
     */
    @TableField("status")
    private String status;

    /**
     * 创建人ID (关联员工表)
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 完成时间 (仅在状态变为DONE时记录)
     */
    @TableField("completed_at")
    private LocalDateTime completedAt;

    /**
     * 备忘录状态枚举
     */
    public enum Status {
        TODO("TODO", "待办"),
        DONE("DONE", "已完成");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 根据代码获取枚举
         */
        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.getCode().equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的备忘录状态: " + code);
        }
    }
}
