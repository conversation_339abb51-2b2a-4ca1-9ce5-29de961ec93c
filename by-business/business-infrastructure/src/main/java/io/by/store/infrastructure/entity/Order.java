package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("orders")
public class Order extends BaseEntity {

    /**
     * 订单ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号，对客展示，唯一
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 关联的会员ID (可为空，非会员顾客)
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 手机号
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 关联的桌号ID (可为空，外带订单)
     */
    @TableField("table_id")
    private Long tableId;

    /**
     * 桌台号
     */
    @TableField("table_number")
    private String tableNumber;

    /**
     * 订单类型：DINE_IN (堂食), TAKE_AWAY (外带)
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 订单状态：PENDING_PAYMENT(待支付), PROCESSING(制作中), COMPLETED(已完成), CANCELLED(已取消)
     */
    @TableField("status")
    private String status;

    /**
     * 支付状态：UNPAID(未支付), PAID(已支付), REFUNDED(已退款)
     */
    @TableField("payment_status")
    private String paymentStatus;

    /**
     * 支付方式：cash(现金支付), balance(余额支付), wechat_pay(微信支付), offline_scan(线下扫码)
     */
    @TableField("payment_method")
    private String paymentMethod;

    /**
     * 原始商品总金额 (所有order_items的总价)
     */
    @TableField("original_amount")
    private BigDecimal originalAmount;

    /**
     * 优惠总金额 (所有order_discounts的总和)
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 应付金额 (original_amount - discount_amount)
     */
    @TableField("payable_amount")
    private BigDecimal payableAmount;

    /**
     * 订单备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 取单号
     */
    @TableField("get_order_no")
    private String getOrderNo;

    /**
     * 订单类型枚举
     */
    public enum OrderType {
        DINE_IN("DINE_IN", "堂食"),
        TAKE_AWAY("TAKE_AWAY", "外带");

        private final String code;
        private final String description;

        OrderType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 订单状态枚举
     */
    public enum Status {
        PENDING_PAYMENT("PENDING_PAYMENT", "待支付"),
        PROCESSING("PROCESSING", "制作中"),
        COMPLETED("COMPLETED", "已完成"),
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 支付状态枚举
     */
    public enum PaymentStatus {
        UNPAID("UNPAID", "未支付"),
        PAID("PAID", "已支付"),
        REFUNDED("REFUNDED", "已退款");

        private final String code;
        private final String description;

        PaymentStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
