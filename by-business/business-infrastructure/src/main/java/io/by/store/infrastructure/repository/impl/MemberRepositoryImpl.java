package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.Member;
import io.by.store.infrastructure.mapper.MemberMapper;
import io.by.store.infrastructure.repository.MemberRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 会员数据访问实现类
 * 使用MyBatis Plus语法实现数据操作
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class MemberRepositoryImpl implements MemberRepository {

    private final MemberMapper memberMapper;

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public boolean save(Member member) {
        return memberMapper.insert(member) > 0;
    }

    @Override
    public boolean updateById(Member member) {
        return memberMapper.updateById(member) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return memberMapper.deleteById(id) > 0;
    }

    @Override
    public Optional<Member> findById(Long id) {
        return Optional.ofNullable(memberMapper.selectById(id));
    }

    // ==================== 唯一性查询 ====================

    @Override
    public Optional<Member> findByMemberId(Long memberId) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getMemberId, memberId)
                .eq(Member::getIsDelete, 0); // 只查询未删除的记录
        return Optional.ofNullable(memberMapper.selectOne(wrapper));
    }

    @Override
    public Optional<Member> findByWxOpenid(String wxOpenid) {
        if (!StringUtils.hasText(wxOpenid)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getWxOpenid, wxOpenid)
                .eq(Member::getIsDelete, 0); // 只查询未删除的记录
        return Optional.ofNullable(memberMapper.selectOne(wrapper));
    }

    @Override
    public Optional<Member> findByWxUnionid(String wxUnionid) {
        if (!StringUtils.hasText(wxUnionid)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getWxUnionid, wxUnionid)
                .eq(Member::getIsDelete, 0); // 只查询未删除的记录
        return Optional.ofNullable(memberMapper.selectOne(wrapper));
    }

    @Override
    public Optional<Member> findByPhoneNumber(String phoneNumber) {
        if (!StringUtils.hasText(phoneNumber)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getPhoneNumber, phoneNumber)
                .eq(Member::getIsDelete, 0); // 只查询未删除的记录
        return Optional.ofNullable(memberMapper.selectOne(wrapper));
    }

    @Override
    public Optional<Member> findByWxOpenidIncludeDeleted(String wxOpenid) {
        if (!StringUtils.hasText(wxOpenid)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getWxOpenid, wxOpenid);
        return Optional.ofNullable(memberMapper.selectOne(wrapper));
    }

    @Override
    public Optional<Member> findByPhoneNumberIncludeDeleted(String phoneNumber) {
        if (!StringUtils.hasText(phoneNumber)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getPhoneNumber, phoneNumber);
        return Optional.ofNullable(memberMapper.selectOne(wrapper));
    }

    @Override
    public boolean logicalDeleteById(Long id) {
        LambdaUpdateWrapper<Member> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Member::getId, id)
                .set(Member::getIsDelete, 1)
                .set(Member::getUpdatedAt, LocalDateTime.now());
        return memberMapper.update(null, wrapper) > 0;
    }

    // ==================== 条件查询 ====================

    @Override
    public List<Member> findByMembershipLevelId(Long membershipLevelId) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getMembershipLevelId, membershipLevelId)
                .eq(Member::getIsDelete, 0) // 只查询未删除的记录
                .orderByDesc(Member::getCreatedAt);
        return memberMapper.selectList(wrapper);
    }

    @Override
    public List<Member> findByMinBalance(BigDecimal minBalance) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(Member::getBalance, minBalance)
                .orderByDesc(Member::getBalance);
        return memberMapper.selectList(wrapper);
    }

    @Override
    public List<Member> findByMinPoints(Long minPoints) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(Member::getPoints, minPoints)
                .orderByDesc(Member::getPoints);
        return memberMapper.selectList(wrapper);
    }

    @Override
    public List<Member> findByNicknameLike(String nickname) {
        if (!StringUtils.hasText(nickname)) {
            return findAllOrderByCreatedAtDesc();
        }
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Member::getNickname, nickname)
                .orderByDesc(Member::getCreatedAt);
        return memberMapper.selectList(wrapper);
    }

    // ==================== 分页查询 ====================

    @Override
    public IPage<Member> findPage(Page<Member> page, String nickname, String phoneNumber, Long membershipLevelId, String membershipLevelName) {
        LambdaQueryWrapper<Member> wrapper = buildQueryWrapper(nickname, phoneNumber, membershipLevelId, membershipLevelName);
        wrapper.orderByDesc(Member::getCreatedAt);
        return memberMapper.selectPage(page, wrapper);
    }

    // ==================== 排序查询 ====================

    @Override
    public List<Member> findRecentMembers(Integer limit) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(Member::getCreatedAt)
                .last("LIMIT " + (limit != null ? limit : 10));
        return memberMapper.selectList(wrapper);
    }

    @Override
    public List<Member> findAllOrderByCreatedAtDesc() {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(Member::getCreatedAt);
        return memberMapper.selectList(wrapper);
    }

    // ==================== 更新操作 ====================

    @Override
    public boolean updateBalance(Long memberId, BigDecimal amount) {
        LambdaUpdateWrapper<Member> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Member::getMemberId, memberId)
                .setSql("balance = balance + " + amount)
                .set(Member::getUpdatedAt, LocalDateTime.now());
        return memberMapper.update(null, wrapper) > 0;
    }

    @Override
    public boolean updatePoints(Long memberId, Long points) {
        LambdaUpdateWrapper<Member> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Member::getMemberId, memberId)
                .setSql("points = points + " + points)
                .set(Member::getUpdatedAt, LocalDateTime.now());
        return memberMapper.update(null, wrapper) > 0;
    }

    // ==================== 统计查询 ====================

    @Override
    public Long countMembers() {
        return memberMapper.selectCount(null);
    }

    @Override
    public Long countMembersByLevel(Long membershipLevelId) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Member::getMembershipLevelId, membershipLevelId);
        return memberMapper.selectCount(wrapper);
    }

    @Override
    public Long countByMinBalance(BigDecimal minBalance) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(Member::getBalance, minBalance);
        return memberMapper.selectCount(wrapper);
    }

    @Override
    public Long countByMinPoints(Long minPoints) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(Member::getPoints, minPoints);
        return memberMapper.selectCount(wrapper);
    }

    @Override
    public Long countMembersByDateRange(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
            if (startTime != null) {
                wrapper.ge(Member::getCreatedAt, startTime);
            }
            if (endTime != null) {
                wrapper.le(Member::getCreatedAt, endTime);
            }
            return memberMapper.selectCount(wrapper);
        } catch (Exception e) {
            log.error("统计会员数量失败: {}", e.getMessage(), e);
            return 0L;
        }
    }

    // ==================== 批量操作 ====================

    @Override
    public boolean saveBatch(List<Member> members) {
        if (members == null || members.isEmpty()) {
            return false;
        }
        // 使用 MyBatis Plus 的批量插入
        for (Member member : members) {
            memberMapper.insert(member);
        }
        return true;
    }

    @Override
    public boolean updateBatchById(List<Member> members) {
        if (members == null || members.isEmpty()) {
            return false;
        }
        // 使用 MyBatis Plus 的批量更新
        for (Member member : members) {
            memberMapper.updateById(member);
        }
        return true;
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return memberMapper.deleteBatchIds(ids) > 0;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建通用查询条件
     */
    private LambdaQueryWrapper<Member> buildQueryWrapper(String nickname, String phoneNumber, Long membershipLevelId, String membershipLevelName) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();

        // 只查询未删除的记录
        wrapper.eq(Member::getIsDelete, 0);

        // 根据昵称模糊查询
        if (StringUtils.hasText(nickname)) {
            wrapper.like(Member::getNickname, nickname);
        }

        // 根据手机号模糊查询
        if (StringUtils.hasText(phoneNumber)) {
            wrapper.like(Member::getPhoneNumber, phoneNumber);
        }

        // 根据会员等级ID查询
        if (membershipLevelId != null) {
            wrapper.eq(Member::getMembershipLevelId, membershipLevelId);
        }

        // 根据会员等级名称模糊查询
        if (StringUtils.hasText(membershipLevelName)) {
            wrapper.like(Member::getMembershipLevelName, membershipLevelName);
        }

        return wrapper;
    }
}
