package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.Modifier;
import io.by.store.infrastructure.mapper.ModifierMapper;
import io.by.store.infrastructure.repository.ModifierRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 加料数据访问实现类
 * 使用MyBatis Plus语法实现数据操作
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ModifierRepositoryImpl implements ModifierRepository {

    private final ModifierMapper modifierMapper;

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public boolean save(Modifier modifier) {
        return modifierMapper.insert(modifier) > 0;
    }

    @Override
    public boolean updateById(Modifier modifier) {
        return modifierMapper.updateById(modifier) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return modifierMapper.deleteById(id) > 0;
    }

    @Override
    public Optional<Modifier> findById(Long id) {
        return Optional.ofNullable(modifierMapper.selectById(id));
    }

    @Override
    public List<Modifier> findAll() {
        return modifierMapper.selectList(null);
    }

    // ==================== 业务查询 ====================

    @Override
    public List<Modifier> findByStoreId(Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getStoreId, storeId)
                .orderByAsc(Modifier::getPriceChange)
                .orderByAsc(Modifier::getName);
        return modifierMapper.selectList(wrapper);
    }

    @Override
    public Optional<Modifier> findByNameAndStoreId(String name, Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getName, name)
                .eq(Modifier::getStoreId, storeId);
        return Optional.ofNullable(modifierMapper.selectOne(wrapper));
    }

    @Override
    public List<Modifier> findByNameLike(String name, Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Modifier::getName, name)
                .eq(Modifier::getStoreId, storeId)
                .orderByAsc(Modifier::getPriceChange)
                .orderByAsc(Modifier::getName);
        return modifierMapper.selectList(wrapper);
    }

    @Override
    public IPage<Modifier> findPage(Integer current, Integer size, String name, Long storeId) {
        Page<Modifier> page = new Page<>(current, size);
        LambdaQueryWrapper<Modifier> wrapper = buildQueryWrapper(name, storeId);
        return modifierMapper.selectPage(page, wrapper);
    }

    // ==================== 价格相关 ====================

    @Override
    public boolean updatePriceChange(Long id, BigDecimal priceChange) {
        LambdaUpdateWrapper<Modifier> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Modifier::getId, id)
                .set(Modifier::getPriceChange, priceChange)
                .set(Modifier::getUpdatedAt, LocalDateTime.now());
        return modifierMapper.update(null, wrapper) > 0;
    }

    @Override
    public List<Modifier> findByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getStoreId, storeId);
        
        if (minPrice != null) {
            wrapper.ge(Modifier::getPriceChange, minPrice);
        }
        if (maxPrice != null) {
            wrapper.le(Modifier::getPriceChange, maxPrice);
        }
        
        wrapper.orderByAsc(Modifier::getPriceChange)
                .orderByAsc(Modifier::getName);
        return modifierMapper.selectList(wrapper);
    }

    @Override
    public List<Modifier> findFreeModifiers(Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getStoreId, storeId)
                .eq(Modifier::getPriceChange, BigDecimal.ZERO)
                .orderByAsc(Modifier::getName);
        return modifierMapper.selectList(wrapper);
    }

    @Override
    public List<Modifier> findPaidModifiers(Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getStoreId, storeId)
                .gt(Modifier::getPriceChange, BigDecimal.ZERO)
                .orderByAsc(Modifier::getPriceChange)
                .orderByAsc(Modifier::getName);
        return modifierMapper.selectList(wrapper);
    }

    // ==================== 统计查询 ====================

    @Override
    public Long countByStoreId(Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getStoreId, storeId);
        return modifierMapper.selectCount(wrapper);
    }

    @Override
    public boolean existsByNameAndStoreIdAndIdNot(String name, Long storeId, Long excludeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getName, name)
                .eq(Modifier::getStoreId, storeId)
                .ne(Modifier::getId, excludeId);
        return modifierMapper.selectCount(wrapper) > 0;
    }

    @Override
    public Long countFreeModifiers(Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getStoreId, storeId)
                .eq(Modifier::getPriceChange, BigDecimal.ZERO);
        return modifierMapper.selectCount(wrapper);
    }

    @Override
    public Long countPaidModifiers(Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Modifier::getStoreId, storeId)
                .gt(Modifier::getPriceChange, BigDecimal.ZERO);
        return modifierMapper.selectCount(wrapper);
    }

    // ==================== 批量操作 ====================

    @Override
    public boolean saveBatch(List<Modifier> modifiers) {
        if (modifiers == null || modifiers.isEmpty()) {
            return false;
        }
        for (Modifier modifier : modifiers) {
            modifierMapper.insert(modifier);
        }
        return true;
    }

    @Override
    public boolean deleteBatchByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return modifierMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public boolean updatePriceChangeBatch(List<Long> ids, BigDecimal priceChange) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        LambdaUpdateWrapper<Modifier> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(Modifier::getId, ids)
                .set(Modifier::getPriceChange, priceChange)
                .set(Modifier::getUpdatedAt, LocalDateTime.now());
        return modifierMapper.update(null, wrapper) > 0;
    }

    // ==================== 私有方法 ====================

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Modifier> buildQueryWrapper(String name, Long storeId) {
        LambdaQueryWrapper<Modifier> wrapper = new LambdaQueryWrapper<>();
        
        if (storeId != null) {
            wrapper.eq(Modifier::getStoreId, storeId);
        }
        
        if (StringUtils.hasText(name)) {
            wrapper.like(Modifier::getName, name);
        }
        
        wrapper.orderByAsc(Modifier::getPriceChange)
                .orderByAsc(Modifier::getName);
        
        return wrapper;
    }
}
