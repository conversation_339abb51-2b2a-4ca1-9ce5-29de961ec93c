package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.infrastructure.entity.StoreTable;

import java.util.List;
import java.util.Optional;

/**
 * 门店桌台数据访问接口
 */
public interface StoreTableRepository {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 保存桌台
     */
    boolean save(StoreTable storeTable);

    /**
     * 根据ID更新桌台
     */
    boolean updateById(StoreTable storeTable);

    /**
     * 根据ID删除桌台（逻辑删除）
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询桌台
     */
    Optional<StoreTable> findById(Long id);

    /**
     * 查询所有桌台
     */
    List<StoreTable> findAll();

    // ==================== 业务查询 ====================

    /**
     * 根据门店ID查询激活的桌台列表
     */
    List<StoreTable> findActiveTablesByStoreId(Long storeId);

    /**
     * 根据门店ID查询所有桌台列表
     */
    List<StoreTable> findAllTablesByStoreId(Long storeId);

    /**
     * 根据门店ID和桌台号查询桌台
     */
    Optional<StoreTable> findByStoreIdAndTableNumber(Long storeId, String tableNumber);

    /**
     * 根据二维码URL查询桌台
     */
    Optional<StoreTable> findByQrcodeUrl(String qrcodeUrl);

    /**
     * 分页查询桌台
     */
    IPage<StoreTable> findPage(Integer current, Integer size, Long storeId, String tableNumber, Boolean isActive);

    // ==================== 状态管理 ====================

    /**
     * 激活桌台
     */
    boolean activateTable(Long id);

    /**
     * 停用桌台
     */
    boolean deactivateTable(Long id);

    /**
     * 更新桌台状态
     */
    boolean updateStatus(Long id, Boolean isActive);

    /**
     * 批量更新桌台状态
     */
    boolean updateStatusBatch(List<Long> ids, Boolean isActive);

    /**
     * 根据门店ID批量更新桌台状态
     */
    boolean updateStatusByStoreId(Long storeId, Boolean isActive);

    // ==================== 统计查询 ====================

    /**
     * 查询门店的激活桌台总数
     */
    Integer countActiveTablesByStoreId(Long storeId);

    /**
     * 查询门店的所有桌台总数
     */
    Integer countAllTablesByStoreId(Long storeId);

    /**
     * 统计所有激活的桌台总数
     */
    Long countAllActiveTables();

    /**
     * 统计所有桌台总数
     */
    Long countAllTables();

    // ==================== 唯一性检查 ====================

    /**
     * 检查门店内桌台号是否存在
     */
    boolean existsByStoreIdAndTableNumber(Long storeId, String tableNumber);

    /**
     * 检查门店内桌台号是否存在（排除指定ID）
     */
    boolean existsByStoreIdAndTableNumberAndIdNot(Long storeId, String tableNumber, Long id);

    /**
     * 检查二维码URL是否存在
     */
    boolean existsByQrcodeUrl(String qrcodeUrl);

    /**
     * 检查二维码URL是否存在（排除指定ID）
     */
    boolean existsByQrcodeUrlAndIdNot(String qrcodeUrl, Long id);

    // ==================== 批量操作 ====================

    /**
     * 批量保存桌台
     */
    boolean saveBatch(List<StoreTable> storeTables);

    /**
     * 根据门店ID删除所有桌台（逻辑删除）
     */
    boolean deleteByStoreId(Long storeId);
}
