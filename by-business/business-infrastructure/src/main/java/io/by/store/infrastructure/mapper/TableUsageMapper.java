package io.by.store.infrastructure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import io.by.store.infrastructure.entity.TableUsage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

/**
 * 桌台使用记录Mapper接口
 */
@Mapper
public interface TableUsageMapper extends BaseMapper<TableUsage> {

    /**
     * 统计门店当前开桌数量
     */
    @Select("SELECT COUNT(*) FROM table_usages WHERE store_id = #{storeId} AND status = 'ACTIVE'")
    Integer countActiveTablesByStoreId(@Param("storeId") Long storeId);

    /**
     * 统计桌台历史使用次数
     */
    @Select("SELECT COUNT(*) FROM table_usages WHERE table_id = #{tableId}")
    Integer countUsagesByTableId(@Param("tableId") Long tableId);

    /**
     * 统计指定时间范围内的桌台使用次数
     */
    @Select("SELECT COUNT(*) FROM table_usages WHERE table_id = #{tableId} " +
            "AND opened_at >= #{startTime} AND opened_at <= #{endTime}")
    Integer countUsagesByDateRange(
            @Param("tableId") Long tableId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询桌台平均使用时长（分钟）
     */
    @Select("SELECT AVG(duration_minutes) FROM table_usages " +
            "WHERE table_id = #{tableId} AND duration_minutes IS NOT NULL")
    Double getAverageUsageDurationByTableId(@Param("tableId") Long tableId);

    /**
     * 查询门店桌台平均使用时长（分钟）
     */
    @Select("SELECT AVG(duration_minutes) FROM table_usages " +
            "WHERE store_id = #{storeId} AND duration_minutes IS NOT NULL")
    Double getAverageUsageDurationByStoreId(@Param("storeId") Long storeId);
}
