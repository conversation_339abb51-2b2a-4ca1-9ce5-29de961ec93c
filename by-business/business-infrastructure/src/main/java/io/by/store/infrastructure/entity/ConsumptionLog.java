package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 余额消费记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("consumption_logs")
public class ConsumptionLog {

    /**
     * 消费记录ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消费会员ID (逻辑外键, 对应 users.member_id)
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 关联的商品订单ID (逻辑外键, 对应 orders.id)
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 本次消费使用的余额金额
     */
    @TableField("amount_consumed")
    private BigDecimal amountConsumed;

    /**
     * 消费前账户余额
     */
    @TableField("balance_before")
    private BigDecimal balanceBefore;

    /**
     * 消费后账户余额
     */
    @TableField("balance_after")
    private BigDecimal balanceAfter;

    /**
     * 记录创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;
}
