package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.by.store.infrastructure.entity.OrderDiscount;
import io.by.store.infrastructure.mapper.OrderDiscountMapper;
import io.by.store.infrastructure.repository.OrderDiscountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 订单优惠明细Repository实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class OrderDiscountRepositoryImpl implements OrderDiscountRepository {

    private final OrderDiscountMapper orderDiscountMapper;

    @Override
    public boolean save(OrderDiscount orderDiscount) {
        try {
            return orderDiscountMapper.insert(orderDiscount) > 0;
        } catch (Exception e) {
            log.error("保存订单优惠明细失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean saveBatch(List<OrderDiscount> orderDiscounts) {
        try {
            if (orderDiscounts == null || orderDiscounts.isEmpty()) {
                return false;
            }
            // 使用 MyBatis Plus 的批量插入
            for (OrderDiscount orderDiscount : orderDiscounts) {
                orderDiscountMapper.insert(orderDiscount);
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存订单优惠明细失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateById(OrderDiscount orderDiscount) {
        try {
            return orderDiscountMapper.updateById(orderDiscount) > 0;
        } catch (Exception e) {
            log.error("更新订单优惠明细失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Optional<OrderDiscount> findById(Long id) {
        try {
            OrderDiscount orderDiscount = orderDiscountMapper.selectById(id);
            return Optional.ofNullable(orderDiscount);
        } catch (Exception e) {
            log.error("根据ID查询订单优惠明细失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<OrderDiscount> findByOrderId(Long orderId) {
        try {
            LambdaQueryWrapper<OrderDiscount> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderDiscount::getOrderId, orderId)
                    .orderByAsc(OrderDiscount::getId);
            return orderDiscountMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据订单ID查询优惠明细失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<OrderDiscount> findByOrderIds(List<Long> orderIds) {
        try {
            if (orderIds == null || orderIds.isEmpty()) {
                return Collections.emptyList();
            }
            LambdaQueryWrapper<OrderDiscount> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(OrderDiscount::getOrderId, orderIds)
                    .orderByAsc(OrderDiscount::getOrderId, OrderDiscount::getId);
            return orderDiscountMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据订单ID列表查询优惠明细失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<OrderDiscount> findByDiscountType(String discountType) {
        try {
            LambdaQueryWrapper<OrderDiscount> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderDiscount::getDiscountType, discountType)
                    .orderByDesc(OrderDiscount::getId);
            return orderDiscountMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据优惠类型查询优惠明细失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public BigDecimal sumDiscountAmountByOrderId(Long orderId) {
        try {
            LambdaQueryWrapper<OrderDiscount> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderDiscount::getOrderId, orderId);
            List<OrderDiscount> discounts = orderDiscountMapper.selectList(wrapper);
            return discounts.stream()
                    .map(OrderDiscount::getAmount)
                    .filter(amount -> amount != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("计算订单总优惠金额失败: {}", e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public boolean deleteById(Long id) {
        try {
            return orderDiscountMapper.deleteById(id) > 0;
        } catch (Exception e) {
            log.error("删除订单优惠明细失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteByOrderId(Long orderId) {
        try {
            LambdaQueryWrapper<OrderDiscount> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OrderDiscount::getOrderId, orderId);
            return orderDiscountMapper.delete(wrapper) > 0;
        } catch (Exception e) {
            log.error("根据订单ID删除优惠明细失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
