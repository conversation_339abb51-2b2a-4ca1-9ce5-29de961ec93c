package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 桌台使用记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("table_usages")
public class TableUsage extends BaseEntity {

    /**
     * 使用记录ID (自增主键)
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的桌台ID
     */
    @TableField("table_id")
    private Long tableId;

    /**
     * 关联的订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 本次使用记录的状态：ACTIVE(进行中), COMPLETED(已完成)
     */
    @TableField("status")
    private String status;

    /**
     * 开桌时间
     */
    @TableField("opened_at")
    private LocalDateTime openedAt;

    /**
     * 关桌/结账时间
     */
    @TableField("closed_at")
    private LocalDateTime closedAt;

    /**
     * 使用时长(分钟)，方便统计
     */
    @TableField("duration_minutes")
    private Integer durationMinutes;

    /**
     * 桌台使用状态枚举
     */
    public enum Status {
        ACTIVE("ACTIVE", "进行中"),
        COMPLETED("COMPLETED", "已完成");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
