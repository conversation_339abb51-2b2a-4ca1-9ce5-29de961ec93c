package io.by.store.infrastructure.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.StoreMemo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 门店备忘录数据访问接口
 */
public interface StoreMemoRepository {

    // ==================== 基础 CRUD 操作 ====================

    /**
     * 保存备忘录
     */
    boolean save(StoreMemo memo);

    /**
     * 根据ID更新备忘录
     */
    boolean updateById(StoreMemo memo);

    /**
     * 根据ID删除备忘录
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询备忘录
     */
    Optional<StoreMemo> findById(Long id);

    // ==================== 分页查询 ====================

    /**
     * 分页查询备忘录
     * @param page 分页对象
     * @param storeId 门店ID（可选）
     * @param status 状态（可选）
     * @param creatorId 创建人ID（可选）
     * @param content 内容关键词（可选，模糊查询）
     * @return 分页结果
     */
    IPage<StoreMemo> findPage(Page<StoreMemo> page, Long storeId, String status, 
                             Long creatorId, String content);

    // ==================== 条件查询 ====================

    /**
     * 根据门店ID查询备忘录列表
     */
    List<StoreMemo> findByStoreId(Long storeId);

    /**
     * 根据状态查询备忘录列表
     */
    List<StoreMemo> findByStatus(String status);

    /**
     * 根据创建人ID查询备忘录列表
     */
    List<StoreMemo> findByCreatorId(Long creatorId);

    /**
     * 根据门店ID和状态查询备忘录列表
     */
    List<StoreMemo> findByStoreIdAndStatus(Long storeId, String status);

    // ==================== 状态更新 ====================

    /**
     * 更新备忘录状态
     * @param id 备忘录ID
     * @param status 新状态
     * @param completedAt 完成时间（状态为DONE时设置）
     * @return 是否更新成功
     */
    boolean updateStatus(Long id, String status, LocalDateTime completedAt);

    /**
     * 批量更新备忘录状态
     * @param ids 备忘录ID列表
     * @param status 新状态
     * @param completedAt 完成时间（状态为DONE时设置）
     * @return 是否更新成功
     */
    boolean updateStatusBatch(List<Long> ids, String status, LocalDateTime completedAt);

    // ==================== 统计查询 ====================

    /**
     * 统计指定门店的备忘录总数
     */
    Long countByStoreId(Long storeId);

    /**
     * 统计指定门店指定状态的备忘录数量
     */
    Long countByStoreIdAndStatus(Long storeId, String status);

    /**
     * 统计指定创建人的备忘录数量
     */
    Long countByCreatorId(Long creatorId);
}
