package io.by.store.infrastructure.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 门店实体类
 */
@Data
@TableName("stores")
public class Store {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 门店名称
     */
    @TableField("name")
    private String name;

    /**
     * 门店地址
     */
    @TableField("address")
    private String address;

    /**
     * 门店电话
     */
    @TableField("phone_number")
    private String phoneNumber;

    /**
     * 是否激活：true-激活，false-停用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 门店地址
     */
    @TableField("x")
    private String x;

    /**
     * 门店地址
     */
    @TableField("y")
    private String y;
}
