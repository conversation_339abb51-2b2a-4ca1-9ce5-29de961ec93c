package io.by.store.infrastructure.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.infrastructure.entity.TableUsage;
import io.by.store.infrastructure.mapper.TableUsageMapper;
import io.by.store.infrastructure.repository.TableUsageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 桌台使用记录数据访问实现类
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class TableUsageRepositoryImpl implements TableUsageRepository {

    private final TableUsageMapper tableUsageMapper;

    // ==================== 基础 CRUD 操作 ====================

    @Override
    public boolean save(TableUsage tableUsage) {
        try {
            return tableUsageMapper.insert(tableUsage) > 0;
        } catch (Exception e) {
            log.error("保存桌台使用记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateById(TableUsage tableUsage) {
        try {
            return tableUsageMapper.updateById(tableUsage) > 0;
        } catch (Exception e) {
            log.error("更新桌台使用记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteById(Long id) {
        try {
            return tableUsageMapper.deleteById(id) > 0;
        } catch (Exception e) {
            log.error("删除桌台使用记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Optional<TableUsage> findById(Long id) {
        try {
            TableUsage tableUsage = tableUsageMapper.selectById(id);
            return Optional.ofNullable(tableUsage);
        } catch (Exception e) {
            log.error("根据ID查询桌台使用记录失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<TableUsage> findAll() {
        try {
            return tableUsageMapper.selectList(null);
        } catch (Exception e) {
            log.error("查询所有桌台使用记录失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    // ==================== 业务查询 ====================

    @Override
    public Optional<TableUsage> findActiveUsageByTableId(Long tableId) {
        try {
            LambdaQueryWrapper<TableUsage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TableUsage::getTableId, tableId)
                    .eq(TableUsage::getStatus, TableUsage.Status.ACTIVE.getCode())
                    .orderByDesc(TableUsage::getOpenedAt)
                    .last("LIMIT 1");
            TableUsage tableUsage = tableUsageMapper.selectOne(wrapper);
            return Optional.ofNullable(tableUsage);
        } catch (Exception e) {
            log.error("根据桌台ID查询当前活跃使用记录失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<TableUsage> findAllUsagesByTableId(Long tableId) {
        try {
            LambdaQueryWrapper<TableUsage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TableUsage::getTableId, tableId)
                    .orderByDesc(TableUsage::getOpenedAt);
            return tableUsageMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据桌台ID查询所有使用记录失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public Optional<TableUsage> findByOrderId(String orderId) {
        try {
            LambdaQueryWrapper<TableUsage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TableUsage::getOrderId, orderId);
            TableUsage tableUsage = tableUsageMapper.selectOne(wrapper);
            return Optional.ofNullable(tableUsage);
        } catch (Exception e) {
            log.error("根据订单ID查询桌台使用记录失败: {}", e.getMessage(), e);
            return Optional.empty();
        }
    }

    @Override
    public List<TableUsage> findActiveUsagesByStoreId(Long storeId) {
        try {
            LambdaQueryWrapper<TableUsage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TableUsage::getStoreId, storeId)
                    .eq(TableUsage::getStatus, TableUsage.Status.ACTIVE.getCode())
                    .orderByDesc(TableUsage::getOpenedAt);
            return tableUsageMapper.selectList(wrapper);
        } catch (Exception e) {
            log.error("根据门店ID查询当前活跃使用记录失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public IPage<TableUsage> findPage(Integer current, Integer size, Long storeId, Long tableId, 
                                     String status, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            Page<TableUsage> page = new Page<>(current, size);
            LambdaQueryWrapper<TableUsage> wrapper = new LambdaQueryWrapper<>();

            // 构建查询条件
            if (storeId != null) {
                wrapper.eq(TableUsage::getStoreId, storeId);
            }
            if (tableId != null) {
                wrapper.eq(TableUsage::getTableId, tableId);
            }
            if (StringUtils.hasText(status)) {
                wrapper.eq(TableUsage::getStatus, status);
            }
            if (startTime != null) {
                wrapper.ge(TableUsage::getOpenedAt, startTime);
            }
            if (endTime != null) {
                wrapper.le(TableUsage::getOpenedAt, endTime);
            }

            // 按开桌时间倒序排列
            wrapper.orderByDesc(TableUsage::getOpenedAt);

            return tableUsageMapper.selectPage(page, wrapper);
        } catch (Exception e) {
            log.error("分页查询桌台使用记录失败: {}", e.getMessage(), e);
            return new Page<>(current, size);
        }
    }

    // ==================== 状态管理 ====================

    @Override
    public boolean openTable(Long tableId, String orderId, Long storeId) {
        try {
            // 检查桌台是否已经开桌
            Optional<TableUsage> activeUsage = findActiveUsageByTableId(tableId);
            if (activeUsage.isPresent()) {
                log.warn("桌台已经开桌，无法重复开桌: tableId={}", tableId);
                return false;
            }

            TableUsage tableUsage = new TableUsage();
            tableUsage.setTableId(tableId);
            tableUsage.setOrderId(orderId);
            tableUsage.setStoreId(storeId);
            tableUsage.setStatus(TableUsage.Status.ACTIVE.getCode());
            tableUsage.setOpenedAt(LocalDateTime.now());

            return save(tableUsage);
        } catch (Exception e) {
            log.error("开桌失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean closeTable(Long tableId) {
        try {
            Optional<TableUsage> activeUsageOpt = findActiveUsageByTableId(tableId);
            if (!activeUsageOpt.isPresent()) {
                log.warn("桌台没有活跃的使用记录，无法关桌: tableId={}", tableId);
                return false;
            }

            TableUsage activeUsage = activeUsageOpt.get();
            LocalDateTime now = LocalDateTime.now();
            
            // 计算使用时长
            Duration duration = Duration.between(activeUsage.getOpenedAt(), now);
            int durationMinutes = (int) duration.toMinutes();

            // 更新使用记录
            LambdaUpdateWrapper<TableUsage> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(TableUsage::getId, activeUsage.getId())
                    .set(TableUsage::getStatus, TableUsage.Status.COMPLETED.getCode())
                    .set(TableUsage::getClosedAt, now)
                    .set(TableUsage::getDurationMinutes, durationMinutes)
                    .set(TableUsage::getUpdatedAt, now);

            return tableUsageMapper.update(null, wrapper) > 0;
        } catch (Exception e) {
            log.error("关桌失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean closeTableByOrderId(String orderId) {
        try {
            Optional<TableUsage> usageOpt = findByOrderId(orderId);
            if (!usageOpt.isPresent()) {
                log.warn("订单没有对应的桌台使用记录: orderId={}", orderId);
                return false;
            }

            TableUsage usage = usageOpt.get();
            if (!TableUsage.Status.ACTIVE.getCode().equals(usage.getStatus())) {
                log.warn("桌台使用记录不是活跃状态，无法关桌: orderId={}", orderId);
                return false;
            }

            return closeTable(usage.getTableId());
        } catch (Exception e) {
            log.error("根据订单ID关桌失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateStatus(Long id, String status) {
        try {
            LambdaUpdateWrapper<TableUsage> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(TableUsage::getId, id)
                    .set(TableUsage::getStatus, status)
                    .set(TableUsage::getUpdatedAt, LocalDateTime.now());
            return tableUsageMapper.update(null, wrapper) > 0;
        } catch (Exception e) {
            log.error("更新桌台使用记录状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 统计查询 ====================

    @Override
    public Integer countActiveTablesByStoreId(Long storeId) {
        try {
            return tableUsageMapper.countActiveTablesByStoreId(storeId);
        } catch (Exception e) {
            log.error("统计门店当前开桌数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Integer countUsagesByTableId(Long tableId) {
        try {
            return tableUsageMapper.countUsagesByTableId(tableId);
        } catch (Exception e) {
            log.error("统计桌台历史使用次数失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Integer countUsagesByDateRange(Long tableId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            return tableUsageMapper.countUsagesByDateRange(tableId, startTime, endTime);
        } catch (Exception e) {
            log.error("统计指定时间范围内桌台使用次数失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Double getAverageUsageDurationByTableId(Long tableId) {
        try {
            return tableUsageMapper.getAverageUsageDurationByTableId(tableId);
        } catch (Exception e) {
            log.error("查询桌台平均使用时长失败: {}", e.getMessage(), e);
            return 0.0;
        }
    }

    @Override
    public Double getAverageUsageDurationByStoreId(Long storeId) {
        try {
            return tableUsageMapper.getAverageUsageDurationByStoreId(storeId);
        } catch (Exception e) {
            log.error("查询门店桌台平均使用时长失败: {}", e.getMessage(), e);
            return 0.0;
        }
    }

    // ==================== 批量操作 ====================

    @Override
    public boolean saveBatch(List<TableUsage> tableUsages) {
        try {
            for (TableUsage tableUsage : tableUsages) {
                tableUsageMapper.insert(tableUsage);
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存桌台使用记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateBatchById(List<TableUsage> tableUsages) {
        try {
            for (TableUsage tableUsage : tableUsages) {
                tableUsageMapper.updateById(tableUsage);
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新桌台使用记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        try {
            return tableUsageMapper.deleteBatchIds(ids) > 0;
        } catch (Exception e) {
            log.error("批量删除桌台使用记录失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
