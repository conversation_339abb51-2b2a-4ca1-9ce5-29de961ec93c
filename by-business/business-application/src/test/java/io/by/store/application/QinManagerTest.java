package io.by.store.application;

import io.by.store.infrastructure.manager.QinManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

/**
 * 七牛云管理器测试类
 * 注意：运行测试前需要在application-test.yml中配置正确的七牛云参数
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class QinManagerTest {

    @Autowired(required = false)
    private QinManager qinManager;

    @Test
    public void testQinManagerBean() {
        if (qinManager != null) {
            log.info("QinManager bean 创建成功");
        } else {
            log.info("QinManager bean 未创建，可能是因为 qiniu.enabled=false 或配置不完整");
        }
    }

    @Test
    public void testUploadToken() {
        if (qinManager == null) {
            log.info("跳过测试：QinManager 未启用");
            return;
        }

        try {
            // 生成上传凭证
            String uploadToken = qinManager.generateUploadToken();
            log.info("生成上传凭证成功: {}", uploadToken);
            
            // 生成指定文件名的上传凭证
            String uploadTokenWithFileName = qinManager.generateUploadToken("test-file.txt");
            log.info("生成指定文件名的上传凭证成功: {}", uploadTokenWithFileName);
            
        } catch (Exception e) {
            log.error("生成上传凭证失败", e);
        }
    }

    @Test
    public void testUploadAndDelete() {
        if (qinManager == null) {
            log.info("跳过测试：QinManager 未启用");
            return;
        }

        try {
            // 测试上传文本文件
            String testContent = "这是一个测试文件内容";
            byte[] testData = testContent.getBytes(StandardCharsets.UTF_8);
            String fileName = "test/test-file.txt";
            
            // 上传文件
            String fileUrl = qinManager.uploadFile(testData, fileName);
            log.info("文件上传成功: {}", fileUrl);
            
            // 检查文件是否存在
            boolean exists = qinManager.fileExists(fileName);
            log.info("文件存在检查: {}", exists);
            
            // 获取文件URL
            String url = qinManager.getFileUrl(fileName);
            log.info("文件访问URL: {}", url);
            
            // 删除文件
            boolean deleted = qinManager.deleteFile(fileName);
            log.info("文件删除结果: {}", deleted);
            
            // 再次检查文件是否存在
            boolean existsAfterDelete = qinManager.fileExists(fileName);
            log.info("删除后文件存在检查: {}", existsAfterDelete);
            
        } catch (Exception e) {
            log.error("文件操作测试失败", e);
        }
    }

    @Test
    public void testUploadStream() {
        if (qinManager == null) {
            log.info("跳过测试：QinManager 未启用");
            return;
        }

        try {
            // 测试上传输入流
            String testContent = "这是通过输入流上传的测试文件";
            ByteArrayInputStream inputStream = new ByteArrayInputStream(
                testContent.getBytes(StandardCharsets.UTF_8));
            String fileName = "test/stream-test-file.txt";
            
            // 上传文件
            String fileUrl = qinManager.uploadFile(inputStream, fileName);
            log.info("输入流文件上传成功: {}", fileUrl);
            
            // 清理测试文件
            qinManager.deleteFile(fileName);
            log.info("测试文件已清理");
            
        } catch (Exception e) {
            log.error("输入流上传测试失败", e);
        }
    }

    @Test
    public void testFileUrl() {
        if (qinManager == null) {
            log.info("跳过测试：QinManager 未启用");
            return;
        }

        try {
            // 测试URL生成
            String fileName = "test/example.jpg";
            String fileUrl = qinManager.getFileUrl(fileName);
            log.info("生成的文件URL: {}", fileUrl);
            
        } catch (Exception e) {
            log.error("URL生成测试失败", e);
        }
    }
}
