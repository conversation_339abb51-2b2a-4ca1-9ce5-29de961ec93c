package io.by.store.application.util;

import io.by.store.infrastructure.manager.QinManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 文件上传工具类
 * 提供文件上传、删除等通用功能
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileUploadUtils {

    @Autowired(required = false)
    private QinManager qinManager;

    /**
     * 支持的图片格式
     */
    private static final List<String> SUPPORTED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/bmp"
    );

    /**
     * 最大文件大小（10MB）
     */
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024;

    /**
     * 上传图片文件
     *
     * @param file     上传的文件
     * @param fileName 自定义文件名（可选）
     * @return 文件访问URL
     * @throws IOException 上传失败时抛出异常
     */
    public String uploadImage(MultipartFile file, String fileName) throws IOException {
        if (qinManager == null) {
            throw new RuntimeException("七牛云服务未启用，无法上传文件");
        }

        // 验证文件
        validateImageFile(file);

        // 上传文件
        return qinManager.uploadFile(file.getBytes(), fileName);
    }

    /**
     * 上传图片文件（自动生成文件名）
     *
     * @param file 上传的文件
     * @return 文件访问URL
     * @throws IOException 上传失败时抛出异常
     */
    public String uploadImage(MultipartFile file) throws IOException {
        return uploadImage(file, null);
    }

    /**
     * 删除文件
     *
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileUrl) {
        if (qinManager == null) {
            log.warn("七牛云服务未启用，无法删除文件");
            return false;
        }

        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            return false;
        }

        try {
            String fileName = extractFileNameFromUrl(fileUrl);
            if (fileName != null) {
                return qinManager.deleteFile(fileName);
            }
        } catch (Exception e) {
            log.error("删除文件失败: {}", fileUrl, e);
        }

        return false;
    }

    /**
     * 验证图片文件
     *
     * @param file 上传的文件
     * @throws IllegalArgumentException 验证失败时抛出异常
     */
    private void validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new IllegalArgumentException("文件大小不能超过10MB");
        }

        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !SUPPORTED_IMAGE_TYPES.contains(contentType.toLowerCase())) {
            throw new IllegalArgumentException("不支持的文件类型，仅支持: " + String.join(", ", SUPPORTED_IMAGE_TYPES));
        }

        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename != null) {
            String extension = getFileExtension(originalFilename).toLowerCase();
            List<String> supportedExtensions = Arrays.asList("jpg", "jpeg", "png", "gif", "webp", "bmp");
            if (!extension.isEmpty() && !supportedExtensions.contains(extension)) {
                throw new IllegalArgumentException("不支持的文件扩展名: " + extension);
            }
        }
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名（不包含点号）
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1);
        }

        return "";
    }

    /**
     * 从URL中提取文件名
     *
     * @param url 文件URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }

        try {
            // 移除协议和域名部分，只保留路径
            String path = url;
            if (path.contains("://")) {
                int index = path.indexOf("://");
                path = path.substring(index + 3);
                if (path.contains("/")) {
                    path = path.substring(path.indexOf("/") + 1);
                }
            }

            return path;
        } catch (Exception e) {
            log.warn("提取文件名失败: {}", url, e);
            return null;
        }
    }

    /**
     * 检查七牛云服务是否可用
     *
     * @return 是否可用
     */
    public boolean isQiniuAvailable() {
        return qinManager != null;
    }

    /**
     * 批量删除文件
     *
     * @param fileUrls 文件URL列表
     * @return 删除结果统计
     */
    public FileDeleteResult batchDeleteFiles(List<String> fileUrls) {
        FileDeleteResult result = new FileDeleteResult();

        if (fileUrls == null || fileUrls.isEmpty()) {
            return result;
        }

        for (String fileUrl : fileUrls) {
            try {
                boolean success = deleteFile(fileUrl);
                if (success) {
                    result.incrementSuccess();
                } else {
                    result.incrementFailed();
                }
            } catch (Exception e) {
                log.error("批量删除文件失败: {}", fileUrl, e);
                result.incrementFailed();
            }
        }

        return result;
    }

    /**
     * 文件删除结果统计
     */
    public static class FileDeleteResult {
        private int successCount = 0;
        private int failedCount = 0;

        public void incrementSuccess() {
            successCount++;
        }

        public void incrementFailed() {
            failedCount++;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailedCount() {
            return failedCount;
        }

        public int getTotalCount() {
            return successCount + failedCount;
        }

        public boolean isAllSuccess() {
            return failedCount == 0 && successCount > 0;
        }

        @Override
        public String toString() {
            return String.format("删除结果: 成功%d个, 失败%d个, 总计%d个",
                    successCount, failedCount, getTotalCount());
        }
    }
}
