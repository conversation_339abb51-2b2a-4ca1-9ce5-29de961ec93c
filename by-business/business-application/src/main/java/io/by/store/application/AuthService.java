package io.by.store.application;

import cn.hutool.core.util.StrUtil;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.JwtUtil;
import io.by.store.infrastructure.entity.Staff;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 认证服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final StaffService staffService;
    private final JwtUtil jwtUtil;
    private final RedissonClient redissonClient;

    /**
     * Redis key前缀
     */
    private static final String ACCESS_TOKEN_PREFIX = "auth:access_token:";
    private static final String REFRESH_TOKEN_PREFIX = "auth:refresh_token:";
    private static final String USER_TOKEN_PREFIX = "auth:user_tokens:";

    /**
     * 用户登录
     */
    public LoginResult login(String username, String password) {
        // 验证用户名和密码
        if (StrUtil.isBlank(username) || StrUtil.isBlank(password)) {
            throw new BusinessException("用户名和密码不能为空");
        }

        // 查询员工信息
        Staff staff = staffService.getStaffByUsername(username);
        if (staff == null) {
            throw new BusinessException("用户名或密码错误");
        }

        // 检查账户状态
        if (!staff.getIsActive()) {
            throw new BusinessException("账户已被禁用");
        }

        // 验证密码
        boolean passwordValid = staffService.validatePassword(username, password);
        if (!passwordValid) {
            throw new BusinessException("用户名或密码错误");
        }

        // 生成令牌
        String accessToken = jwtUtil.generateAccessToken(
                staff.getId(), 
                staff.getUsername(), 
                staff.getStoreId(), 
                staff.getRoleId()
        );
        String refreshToken = jwtUtil.generateRefreshToken(staff.getId(), staff.getUsername());

        // 存储令牌到Redis
        storeTokensInRedis(staff.getId(), accessToken, refreshToken);

        log.info("用户登录成功: {}", username);

        return LoginResult.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .expiresIn(jwtUtil.getAccessTokenExpiration() / 1000) // 转换为秒
                .staffId(staff.getId())
                .username(staff.getUsername())
                .fullName(staff.getFullName())
                .storeId(staff.getStoreId())
                .roleId(staff.getRoleId())
                .build();
    }

    /**
     * 刷新令牌
     */
    public LoginResult refreshToken(String refreshToken) {
        if (StrUtil.isBlank(refreshToken)) {
            throw new BusinessException("刷新令牌不能为空");
        }

        // 验证刷新令牌
        if (!jwtUtil.validateToken(refreshToken)) {
            throw new BusinessException("刷新令牌无效或已过期");
        }

        // 检查令牌类型
        String tokenType = jwtUtil.getTokenTypeFromToken(refreshToken);
        if (!"refresh".equals(tokenType)) {
            throw new BusinessException("令牌类型错误");
        }

        // 检查Redis中是否存在该刷新令牌
        Long staffId = jwtUtil.getStaffIdFromToken(refreshToken);
        String username = jwtUtil.getUsernameFromToken(refreshToken);
        
        String redisKey = REFRESH_TOKEN_PREFIX + staffId;
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        String storedRefreshToken = bucket.get();
        
        if (!refreshToken.equals(storedRefreshToken)) {
            throw new BusinessException("刷新令牌无效");
        }

        // 获取员工信息
        Staff staff = staffService.getStaffById(staffId);
        if (staff == null || !staff.getIsActive()) {
            throw new BusinessException("用户不存在或已被禁用");
        }

        // 生成新的访问令牌
        String newAccessToken = jwtUtil.generateAccessToken(
                staff.getId(), 
                staff.getUsername(), 
                staff.getStoreId(), 
                staff.getRoleId()
        );

        // 更新Redis中的访问令牌
        String accessTokenKey = ACCESS_TOKEN_PREFIX + staffId;
        RBucket<String> accessTokenBucket = redissonClient.getBucket(accessTokenKey);
        accessTokenBucket.set(newAccessToken, jwtUtil.getAccessTokenExpiration(), TimeUnit.MILLISECONDS);

        log.info("令牌刷新成功: {}", username);

        return LoginResult.builder()
                .accessToken(newAccessToken)
                .refreshToken(refreshToken) // 刷新令牌保持不变
                .expiresIn(jwtUtil.getAccessTokenExpiration() / 1000)
                .staffId(staff.getId())
                .username(staff.getUsername())
                .fullName(staff.getFullName())
                .storeId(staff.getStoreId())
                .roleId(staff.getRoleId())
                .build();
    }

    /**
     * 用户登出
     */
    public void logout(Long staffId) {
        if (staffId == null) {
            return;
        }

        // 从Redis中删除令牌
        removeTokensFromRedis(staffId);

        log.info("用户登出成功: staffId={}", staffId);
    }

    /**
     * 验证访问令牌
     */
    public boolean validateAccessToken(String accessToken) {
        if (StrUtil.isBlank(accessToken)) {
            return false;
        }

        // 验证令牌格式和签名
        if (!jwtUtil.validateToken(accessToken)) {
            return false;
        }

        // 检查令牌类型
        String tokenType = jwtUtil.getTokenTypeFromToken(accessToken);
        if (!"access".equals(tokenType)) {
            return false;
        }

        // 检查Redis中是否存在该访问令牌
        Long staffId = jwtUtil.getStaffIdFromToken(accessToken);
        if (staffId == null) {
            return false;
        }

        String redisKey = ACCESS_TOKEN_PREFIX + staffId;
        RBucket<String> bucket = redissonClient.getBucket(redisKey);
        String storedAccessToken = bucket.get();

        return accessToken.equals(storedAccessToken);
    }

    /**
     * 存储令牌到Redis
     */
    private void storeTokensInRedis(Long staffId, String accessToken, String refreshToken) {
        // 存储访问令牌
        String accessTokenKey = ACCESS_TOKEN_PREFIX + staffId;
        RBucket<String> accessTokenBucket = redissonClient.getBucket(accessTokenKey);
        accessTokenBucket.set(accessToken, jwtUtil.getAccessTokenExpiration(), TimeUnit.MILLISECONDS);

        // 存储刷新令牌
        String refreshTokenKey = REFRESH_TOKEN_PREFIX + staffId;
        RBucket<String> refreshTokenBucket = redissonClient.getBucket(refreshTokenKey);
        refreshTokenBucket.set(refreshToken, jwtUtil.getRefreshTokenExpiration(), TimeUnit.MILLISECONDS);
    }

    /**
     * 从Redis中删除令牌
     */
    private void removeTokensFromRedis(Long staffId) {
        String accessTokenKey = ACCESS_TOKEN_PREFIX + staffId;
        String refreshTokenKey = REFRESH_TOKEN_PREFIX + staffId;
        
        redissonClient.getBucket(accessTokenKey).delete();
        redissonClient.getBucket(refreshTokenKey).delete();
    }

    /**
     * 登录结果
     */
    @lombok.Data
    @lombok.Builder
    public static class LoginResult {
        private String accessToken;
        private String refreshToken;
        private Long expiresIn; // 过期时间（秒）
        private Long staffId;
        private String username;
        private String fullName;
        private Long storeId;
        private Long roleId;
    }
}
