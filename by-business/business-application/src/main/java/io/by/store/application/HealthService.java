package io.by.store.application;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;

@Slf4j
@Service
public class HealthService {

    private final DataSource dataSource;

    @Autowired(required = false)
    private RedissonClient redissonClient;

    public HealthService(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * 获取应用健康状态
     */
    public String getHealthStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Application is running");
        
        // 检查数据库连接
        if (checkDatabaseConnection()) {
            status.append(" | Database: OK");
        } else {
            status.append(" | Database: ERROR");
        }
        
        // 检查Redis连接
        if (redissonClient != null) {
            if (checkRedisConnection()) {
                status.append(" | Redis: OK");
            } else {
                status.append(" | Redis: ERROR");
            }
        } else {
            status.append(" | Redis: DISABLED");
        }
        
        return status.toString();
    }

    /**
     * 检查数据库连接
     */
    private boolean checkDatabaseConnection() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (Exception e) {
            log.error("Database connection check failed", e);
            return false;
        }
    }

    /**
     * 检查Redis连接
     */
    private boolean checkRedisConnection() {
        try {
            if (redissonClient != null) {
                redissonClient.getBucket("health:check").set("ok");
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("Redis connection check failed", e);
            return false;
        }
    }
}
