package io.by.store.application.config;

import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 七牛云配置类
 * 负责初始化七牛云相关的配置和Bean
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "qiniu")
@ConditionalOnProperty(name = "qiniu.enabled", havingValue = "true", matchIfMissing = false)
public class QinConfig {

    /**
     * 七牛云AccessKey
     */
    private String accessKey;

    /**
     * 七牛云SecretKey
     */
    private String secretKey;

    /**
     * 存储空间名称
     */
    private String bucket;

    /**
     * 访问域名
     */
    private String domain;

    /**
     * 存储区域
     * z0: 华东
     * z1: 华北
     * z2: 华南
     * na0: 北美
     * as0: 东南亚
     */
    private String region = "z0";

    /**
     * 是否启用七牛云存储
     */
    private boolean enabled = true;

    /**
     * 创建Auth认证对象
     */
    @Bean
    public Auth qiniuAuth() {
        log.info("初始化七牛云Auth认证对象");
        return Auth.create(accessKey, secretKey);
    }

    /**
     * 创建上传管理器
     */
    @Bean
    public UploadManager uploadManager() {
        log.info("初始化七牛云UploadManager，区域: {}", region);

        // 根据配置选择存储区域
        Region qiniuRegion = getQiniuRegion(region);

        com.qiniu.storage.Configuration cfg = new com.qiniu.storage.Configuration(qiniuRegion);
        // 指定分片上传版本
        cfg.resumableUploadAPIVersion = com.qiniu.storage.Configuration.ResumableUploadAPIVersion.V2;

        return new UploadManager(cfg);
    }

    /**
     * 创建存储空间管理器
     */
    @Bean
    public BucketManager bucketManager(Auth auth) {
        log.info("初始化七牛云BucketManager");

        Region qiniuRegion = getQiniuRegion(region);
        com.qiniu.storage.Configuration cfg = new com.qiniu.storage.Configuration(qiniuRegion);

        return new BucketManager(auth, cfg);
    }

    /**
     * 根据配置字符串获取七牛云区域对象
     */
    private Region getQiniuRegion(String regionStr) {
        switch (regionStr.toLowerCase()) {
            case "z0":
                return Region.region0(); // 华东
            case "z1":
                return Region.region1(); // 华北
            case "z2":
                return Region.region2(); // 华南
            case "na0":
                return Region.regionNa0(); // 北美
            case "as0":
                return Region.regionAs0(); // 东南亚
            default:
                log.warn("未知的存储区域配置: {}，使用默认华东区域", regionStr);
                return Region.region0();
        }
    }
}
