package io.by.store.application;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.common.BusinessException;
import io.by.store.infrastructure.entity.Product;
import io.by.store.infrastructure.entity.ProductCategory;
import io.by.store.infrastructure.repository.ProductCategoryRepository;
import io.by.store.infrastructure.repository.ProductRepository;
import io.by.store.infrastructure.vo.ProductWithCategoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 商品服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductService {

    private final ProductRepository productRepository;
    private final ProductCategoryRepository categoryRepository;

    /**
     * 创建商品
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createProduct(Product product) {
        // 验证分类是否存在
        Optional<ProductCategory> category = categoryRepository.findById(product.getCategoryId());
        if (!category.isPresent()) {
            throw new BusinessException("分类不存在，ID: " + product.getCategoryId());
        }

        // 检查商品编码是否重复（如果提供了编码）
        if (StringUtils.hasText(product.getProductCode())) {
            Optional<Product> existing = productRepository.findByProductCodeAndStoreId(
                    product.getProductCode(), product.getStoreId());
            if (existing.isPresent()) {
                throw new BusinessException("商品编码已存在: " + product.getProductCode());
            }
        }

        // 设置默认值
        if (product.getStatus() == null) {
            product.setStatus(Product.Status.PUBLISHED.getCode());
        }
        if (product.getStockQuantity() == null) {
            product.setStockQuantity(0);
        }
        if (product.getAlertQuantity() == null) {
            product.setAlertQuantity(10);
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        product.setCreatedAt(now);
        product.setUpdatedAt(now);

        boolean result = productRepository.save(product);
        if (result) {
            log.info("商品创建成功: {}", product.getName());
        }
        return result;
    }

    /**
     * 更新商品
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProduct(Product product) {
        // 检查商品是否存在
        Optional<Product> existing = productRepository.findById(product.getId());
        if (!existing.isPresent()) {
            throw new BusinessException("商品不存在，ID: " + product.getId());
        }

        // 验证分类是否存在
        Optional<ProductCategory> category = categoryRepository.findById(product.getCategoryId());
        if (!category.isPresent()) {
            throw new BusinessException("分类不存在，ID: " + product.getCategoryId());
        }

        // 检查商品编码是否与其他商品重复
        if (StringUtils.hasText(product.getProductCode())) {
            if (productRepository.existsByProductCodeAndStoreIdAndIdNot(
                    product.getProductCode(), product.getStoreId(), product.getId())) {
                throw new BusinessException("商品编码已存在: " + product.getProductCode());
            }
        }

        // 设置更新时间
        product.setUpdatedAt(LocalDateTime.now());

        boolean result = productRepository.updateById(product);
        if (result) {
            log.info("商品更新成功: {}", product.getName());
        }
        return result;
    }

    /**
     * 删除商品
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProduct(Long id) {
        // 检查商品是否存在
        Optional<Product> product = productRepository.findById(id);
        if (!product.isPresent()) {
            throw new BusinessException("商品不存在，ID: " + id);
        }

        boolean result = productRepository.deleteById(id);
        if (result) {
            log.info("商品删除成功，ID: {}", id);
        }
        return result;
    }

    /**
     * 根据ID查询商品
     */
    public Optional<Product> getProductById(Long id) {
        return productRepository.findById(id);
    }

    /**
     * 根据ID查询商品（联表查询，包含分类名称）
     */
    public Optional<ProductWithCategoryVO> getProductByIdWithCategory(Long id) {
        return productRepository.findByIdWithCategory(id);
    }

    /**
     * 根据门店ID查询商品列表
     */
    public List<Product> getProductsByStoreId(Long storeId) {
        return productRepository.findByStoreId(storeId);
    }

    /**
     * 根据门店ID查询商品列表（联表查询，包含分类名称）
     */
    public List<ProductWithCategoryVO> getProductsByStoreIdWithCategory(Long storeId) {
        return productRepository.findByStoreIdWithCategory(storeId);
    }

    /**
     * 根据分类ID查询商品列表
     */
    public List<Product> getProductsByCategoryId(Long categoryId) {
        return productRepository.findByCategoryId(categoryId);
    }

    /**
     * 根据分类ID查询商品列表（联表查询，包含分类名称）
     */
    public List<ProductWithCategoryVO> getProductsByCategoryIdWithCategory(Long categoryId) {
        return productRepository.findByCategoryIdWithCategory(categoryId);
    }

    /**
     * 根据状态查询商品列表
     */
    public List<Product> getProductsByStatus(String status, Long storeId) {
        return productRepository.findByStatus(status, storeId);
    }

    /**
     * 分页查询商品
     */
    public IPage<Product> queryProducts(Integer current, Integer size, String name,
                                       Long categoryId, String status, String productCode, Long storeId) {
        return productRepository.findPage(current, size, name, categoryId, status, productCode, storeId);
    }

    /**
     * 分页查询商品（联表查询，包含分类名称）
     */
    public IPage<ProductWithCategoryVO> queryProductsWithCategory(Integer current, Integer size, String name,
                                                                 Long categoryId, String status, String productCode, Long storeId) {
        return productRepository.findPageWithCategory(current, size, name, categoryId, status, productCode, storeId);
    }

    /**
     * 根据商品编码查询商品
     */
    public Optional<Product> getProductByCode(String productCode, Long storeId) {
        return productRepository.findByProductCodeAndStoreId(productCode, storeId);
    }

    /**
     * 根据名称模糊查询商品
     */
    public List<Product> searchProductsByName(String name, Long storeId) {
        return productRepository.findByNameLike(name, storeId);
    }

    /**
     * 根据分类ID分页查询商品（联表查询，包含分类名称）
     */
    public IPage<ProductWithCategoryVO> getProductsByCategoryIdWithCategoryPage(Integer current, Integer size,
                                                                               Long categoryId, Long storeId, String status) {
        return productRepository.findPageByCategoryIdWithCategory(current, size, categoryId, storeId, status);
    }

    /**
     * 搜索商品（分页，联表查询，包含分类名称）
     */
    public IPage<ProductWithCategoryVO> searchProductsWithCategoryPage(Integer current, Integer size,
                                                                      Long storeId, String keyword) {
        return productRepository.searchProductsWithCategoryPage(current, size, storeId, keyword);
    }

    // ==================== 库存管理 ====================

    /**
     * 更新库存数量
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStock(Long id, Integer quantity) {
        // 检查商品是否存在
        Optional<Product> product = productRepository.findById(id);
        if (!product.isPresent()) {
            throw new BusinessException("商品不存在，ID: " + id);
        }

        if (quantity < 0) {
            throw new BusinessException("库存数量不能为负数");
        }

        boolean result = productRepository.updateStockQuantity(id, quantity);
        if (result) {
            log.info("商品库存更新成功，ID: {}, 库存: {}", id, quantity);
            
            // 检查是否需要自动标记为售罄
            if (quantity == 0) {
                productRepository.markAsSoldOut(id);
                log.info("商品库存为0，自动标记为售罄，ID: {}", id);
            }
        }
        return result;
    }

    /**
     * 增加库存
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean increaseStock(Long id, Integer quantity) {
        if (quantity <= 0) {
            throw new BusinessException("增加的库存数量必须大于0");
        }

        boolean result = productRepository.increaseStock(id, quantity);
        if (result) {
            log.info("商品库存增加成功，ID: {}, 增加数量: {}", id, quantity);
        }
        return result;
    }

    /**
     * 减少库存
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean decreaseStock(Long id, Integer quantity) {
        if (quantity <= 0) {
            throw new BusinessException("减少的库存数量必须大于0");
        }

        // 检查当前库存是否足够
        Optional<Product> product = productRepository.findById(id);
        if (!product.isPresent()) {
            throw new BusinessException("商品不存在，ID: " + id);
        }

        if (product.get().getStockQuantity() < quantity) {
            throw new BusinessException("库存不足，当前库存: " + product.get().getStockQuantity() + 
                                     ", 需要减少: " + quantity);
        }

        boolean result = productRepository.decreaseStock(id, quantity);
        if (result) {
            log.info("商品库存减少成功，ID: {}, 减少数量: {}", id, quantity);
            
            // 检查减少后的库存
            Optional<Product> updatedProduct = productRepository.findById(id);
            if (updatedProduct.isPresent() && updatedProduct.get().getStockQuantity() == 0) {
                productRepository.markAsSoldOut(id);
                log.info("商品库存为0，自动标记为售罄，ID: {}", id);
            }
        }
        return result;
    }

    /**
     * 查询库存不足的商品
     */
    public List<Product> getLowStockProducts(Long storeId) {
        return productRepository.findLowStockProducts(storeId);
    }

    /**
     * 查询零库存商品
     */
    public List<Product> getZeroStockProducts(Long storeId) {
        return productRepository.findZeroStockProducts(storeId);
    }

    // ==================== 状态管理 ====================

    /**
     * 上架商品
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean publishProduct(Long id) {
        boolean result = productRepository.publishProduct(id);
        if (result) {
            log.info("商品上架成功，ID: {}", id);
        }
        return result;
    }

    /**
     * 下架商品
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean archiveProduct(Long id) {
        boolean result = productRepository.archiveProduct(id);
        if (result) {
            log.info("商品下架成功，ID: {}", id);
        }
        return result;
    }

    /**
     * 标记为售罄
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsSoldOut(Long id) {
        boolean result = productRepository.markAsSoldOut(id);
        if (result) {
            log.info("商品标记为售罄，ID: {}", id);
        }
        return result;
    }

    /**
     * 批量更新商品状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatusBatch(List<Long> ids, String status) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        boolean result = productRepository.updateStatusBatch(ids, status);
        if (result) {
            log.info("批量更新商品状态成功，数量: {}, 状态: {}", ids.size(), status);
        }
        return result;
    }

    // ==================== 价格管理 ====================

    /**
     * 更新商品价格
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePrice(Long id, BigDecimal price) {
        if (price.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("价格不能为负数");
        }

        boolean result = productRepository.updatePrice(id, price);
        if (result) {
            log.info("商品价格更新成功，ID: {}, 价格: {}", id, price);
        }
        return result;
    }

    /**
     * 根据价格范围查询商品
     */
    public List<Product> getProductsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Long storeId) {
        return productRepository.findByPriceRange(minPrice, maxPrice, storeId);
    }

    // ==================== 统计功能 ====================

    /**
     * 统计门店商品总数
     */
    public Long countProductsByStoreId(Long storeId) {
        return productRepository.countByStoreId(storeId);
    }

    /**
     * 统计各状态商品数量
     */
    public Long countProductsByStatus(String status, Long storeId) {
        return productRepository.countByStatus(status, storeId);
    }

    /**
     * 批量删除商品
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProductsBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        boolean result = productRepository.deleteBatchByIds(ids);
        if (result) {
            log.info("批量删除商品成功，数量: {}", ids.size());
        }
        return result;
    }
}
