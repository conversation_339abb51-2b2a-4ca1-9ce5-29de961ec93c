package io.by.store.application;

import io.by.store.infrastructure.entity.Order;
import io.by.store.infrastructure.entity.OrderItem;
import io.by.store.infrastructure.repository.OrderRepository;
import io.by.store.infrastructure.repository.OrderItemRepository;
import io.by.store.infrastructure.repository.MemberRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统计服务类
 * 提供各种业务统计数据
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsService {

    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final MemberRepository memberRepository;

    /**
     * 获取今日业务统计数据
     */
    public DailyStatistics getTodayStatistics() {
        LocalDate today = LocalDate.now();
        LocalDate yesterday = today.minusDays(1);

        // 今日数据
        DailyData todayData = getDailyData(today);
        // 昨日数据
        DailyData yesterdayData = getDailyData(yesterday);

        // 计算同比增长百分比
        DailyStatistics statistics = new DailyStatistics();
        statistics.setTodaySalesAmount(todayData.getSalesAmount());
        statistics.setSalesGrowthPercentage(calculateGrowthPercentage(todayData.getSalesAmount(), yesterdayData.getSalesAmount()));
        
        statistics.setTodayOrderCount(todayData.getOrderCount());
        statistics.setOrderGrowthPercentage(calculateGrowthPercentage(
                BigDecimal.valueOf(todayData.getOrderCount()), 
                BigDecimal.valueOf(yesterdayData.getOrderCount())
        ));
        
        statistics.setTodayNewMemberCount(todayData.getNewMemberCount());
        statistics.setMemberGrowthPercentage(calculateGrowthPercentage(
                BigDecimal.valueOf(todayData.getNewMemberCount()), 
                BigDecimal.valueOf(yesterdayData.getNewMemberCount())
        ));

        log.info("今日统计数据: 销售额={}, 订单数={}, 新增会员={}", 
                todayData.getSalesAmount(), todayData.getOrderCount(), todayData.getNewMemberCount());

        return statistics;
    }

    /**
     * 获取最近5笔订单详情
     */
    public List<RecentOrderInfo> getRecentOrders() {
        // 查询最近5笔订单
        List<Order> recentOrders = orderRepository.findRecentOrders(5);

        if (recentOrders.isEmpty()) {
            log.info("没有找到最近的订单");
            return Collections.emptyList();
        }

        // 获取所有订单ID
        List<Long> orderIds = recentOrders.stream()
                .map(Order::getId)
                .collect(Collectors.toList());

        // 批量查询订单商品项
        List<OrderItem> allOrderItems = orderItemRepository.findByOrderIds(orderIds);

        // 按订单ID分组
        Map<Long, List<OrderItem>> orderItemsMap = allOrderItems.stream()
                .collect(Collectors.groupingBy(OrderItem::getOrderId));

        // 构建响应数据
        return recentOrders.stream()
                .map(order -> {
                    RecentOrderInfo orderInfo = new RecentOrderInfo();
                    orderInfo.setOrderNo(order.getOrderNo());
                    orderInfo.setTotalAmount(order.getPayableAmount());

                    // 获取该订单的商品项
                    List<OrderItem> orderItems = orderItemsMap.getOrDefault(order.getId(), Collections.emptyList());
                    List<OrderItemInfo> itemInfos = orderItems.stream()
                            .map(item -> {
                                OrderItemInfo itemInfo = new OrderItemInfo();
                                itemInfo.setProductName(item.getProductName());
                                itemInfo.setQuantity(item.getQuantity());
                                return itemInfo;
                            })
                            .collect(Collectors.toList());

                    orderInfo.setItems(itemInfos);
                    return orderInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取指定日期的业务数据
     */
    private DailyData getDailyData(LocalDate date) {
        LocalDateTime startTime = date.atStartOfDay();
        LocalDateTime endTime = date.atTime(LocalTime.MAX);

        // 统计销售总额
        BigDecimal salesAmount = orderRepository.sumPayableAmountByDateRange(startTime, endTime);
        if (salesAmount == null) {
            salesAmount = BigDecimal.ZERO;
        }

        // 统计订单数量
        Long orderCount = orderRepository.countOrdersByDateRange(startTime, endTime);
        if (orderCount == null) {
            orderCount = 0L;
        }

        // 统计新增会员数
        Long newMemberCount = memberRepository.countMembersByDateRange(startTime, endTime);
        if (newMemberCount == null) {
            newMemberCount = 0L;
        }

        DailyData data = new DailyData();
        data.setSalesAmount(salesAmount);
        data.setOrderCount(orderCount);
        data.setNewMemberCount(newMemberCount);

        return data;
    }

    /**
     * 计算增长百分比
     * @param current 当前值
     * @param previous 前一期值
     * @return 增长百分比
     */
    private BigDecimal calculateGrowthPercentage(BigDecimal current, BigDecimal previous) {
        if (previous == null || previous.compareTo(BigDecimal.ZERO) == 0) {
            // 如果昨日数据为0或null，今日有数据则为100%增长，否则为0%
            return current != null && current.compareTo(BigDecimal.ZERO) > 0 ? 
                    new BigDecimal("100.00") : BigDecimal.ZERO;
        }
        
        if (current == null) {
            current = BigDecimal.ZERO;
        }

        // 计算增长率: (今日 - 昨日) / 昨日 * 100
        BigDecimal growth = current.subtract(previous)
                .divide(previous, 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);

        return growth;
    }

    /**
     * 日统计数据
     */
    public static class DailyStatistics {
        /**
         * 今日订单销售总额
         */
        private BigDecimal todaySalesAmount;

        /**
         * 销售额相比昨日增长百分比
         */
        private BigDecimal salesGrowthPercentage;

        /**
         * 今日订单数量
         */
        private Long todayOrderCount;

        /**
         * 订单数量相比昨日增长百分比
         */
        private BigDecimal orderGrowthPercentage;

        /**
         * 今日新增会员数
         */
        private Long todayNewMemberCount;

        /**
         * 新增会员数相比昨日增长百分比
         */
        private BigDecimal memberGrowthPercentage;

        // Getters and Setters
        public BigDecimal getTodaySalesAmount() {
            return todaySalesAmount;
        }

        public void setTodaySalesAmount(BigDecimal todaySalesAmount) {
            this.todaySalesAmount = todaySalesAmount;
        }

        public BigDecimal getSalesGrowthPercentage() {
            return salesGrowthPercentage;
        }

        public void setSalesGrowthPercentage(BigDecimal salesGrowthPercentage) {
            this.salesGrowthPercentage = salesGrowthPercentage;
        }

        public Long getTodayOrderCount() {
            return todayOrderCount;
        }

        public void setTodayOrderCount(Long todayOrderCount) {
            this.todayOrderCount = todayOrderCount;
        }

        public BigDecimal getOrderGrowthPercentage() {
            return orderGrowthPercentage;
        }

        public void setOrderGrowthPercentage(BigDecimal orderGrowthPercentage) {
            this.orderGrowthPercentage = orderGrowthPercentage;
        }

        public Long getTodayNewMemberCount() {
            return todayNewMemberCount;
        }

        public void setTodayNewMemberCount(Long todayNewMemberCount) {
            this.todayNewMemberCount = todayNewMemberCount;
        }

        public BigDecimal getMemberGrowthPercentage() {
            return memberGrowthPercentage;
        }

        public void setMemberGrowthPercentage(BigDecimal memberGrowthPercentage) {
            this.memberGrowthPercentage = memberGrowthPercentage;
        }
    }

    /**
     * 最近订单信息
     */
    public static class RecentOrderInfo {
        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 订单总金额
         */
        private BigDecimal totalAmount;

        /**
         * 订单商品项列表
         */
        private List<OrderItemInfo> items;

        // Getters and Setters
        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public void setTotalAmount(BigDecimal totalAmount) {
            this.totalAmount = totalAmount;
        }

        public List<OrderItemInfo> getItems() {
            return items;
        }

        public void setItems(List<OrderItemInfo> items) {
            this.items = items;
        }
    }

    /**
     * 订单商品项信息
     */
    public static class OrderItemInfo {
        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品数量
         */
        private Integer quantity;

        // Getters and Setters
        public String getProductName() {
            return productName;
        }

        public void setProductName(String productName) {
            this.productName = productName;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }

    /**
     * 内部使用的日数据类
     */
    private static class DailyData {
        private BigDecimal salesAmount;
        private Long orderCount;
        private Long newMemberCount;

        public BigDecimal getSalesAmount() {
            return salesAmount;
        }

        public void setSalesAmount(BigDecimal salesAmount) {
            this.salesAmount = salesAmount;
        }

        public Long getOrderCount() {
            return orderCount;
        }

        public void setOrderCount(Long orderCount) {
            this.orderCount = orderCount;
        }

        public Long getNewMemberCount() {
            return newMemberCount;
        }

        public void setNewMemberCount(Long newMemberCount) {
            this.newMemberCount = newMemberCount;
        }
    }
}
