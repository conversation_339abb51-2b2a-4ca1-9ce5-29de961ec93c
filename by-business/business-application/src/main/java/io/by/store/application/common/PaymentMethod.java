package io.by.store.application.common;

import lombok.Getter;

/**
 * 支付方式枚举
 */
@Getter
public enum PaymentMethod {

    /**
     * 现金支付
     */
    CASH("cash", "现金支付", "线下现金支付"),

    /**
     * 余额支付
     */
    BALANCE("balance", "余额支付", "会员账户余额支付"),

    /**
     * 微信支付
     */
    WECHAT_PAY("wechat_pay", "微信支付", "微信扫码或小程序支付"),

    /**
     * 线下扫码
     */
    OFFLINE_SCAN("offline_scan", "会员线下扫码", "会员线下扫码支付（支付宝、微信等）");

    /**
     * 支付方式代码
     */
    private final String code;

    /**
     * 支付方式名称
     */
    private final String name;

    /**
     * 支付方式描述
     */
    private final String description;

    PaymentMethod(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static PaymentMethod fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (PaymentMethod method : values()) {
            if (method.getCode().equals(code)) {
                return method;
            }
        }

        throw new IllegalArgumentException("未知的支付方式: " + code);
    }

    /**
     * 检查是否是线上支付方式
     */
    public boolean isOnlinePayment() {
        return this == WECHAT_PAY;
    }

    /**
     * 检查是否是线下支付方式
     */
    public boolean isOfflinePayment() {
        return this == CASH || this == OFFLINE_SCAN;
    }

    /**
     * 检查是否是余额支付
     */
    public boolean isBalancePayment() {
        return this == BALANCE;
    }

    /**
     * 检查是否需要第三方交易流水号
     */
    public boolean requiresTransactionId() {
        return this == WECHAT_PAY || this == OFFLINE_SCAN;
    }

    /**
     * 检查是否需要员工操作
     */
    public boolean requiresStaffOperation() {
        return this == CASH || this == OFFLINE_SCAN;
    }

    /**
     * 获取支付方式的显示名称
     */
    public String getDisplayName() {
        return this.name;
    }
}
