package io.by.store.application;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.application.common.UserContext;
import io.by.store.infrastructure.entity.StoreMemo;
import io.by.store.infrastructure.repository.StoreMemoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 门店备忘录服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StoreMemoService {

    private final StoreMemoRepository storeMemoRepository;

    /**
     * 创建备忘录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createMemo(StoreMemo memo) {
        // 验证备忘录内容
        validateMemoContent(memo.getContent());
        
        // 设置默认值
        if (!StringUtils.hasText(memo.getStatus())) {
            memo.setStatus(StoreMemo.Status.TODO.getCode());
        }
        
        // 验证状态
        validateMemoStatus(memo.getStatus());
        
        // 设置门店ID（从上下文获取）
        if (memo.getStoreId() == null) {
            memo.setStoreId(UserContext.getCurrentStoreId());
        }
        
        // 设置创建人ID（从上下文获取）
        if (memo.getCreatorId() == null) {
            memo.setCreatorId(UserContext.getCurrentStaffId());
        }
        
        // 如果是待办状态，清空完成时间
        if (StoreMemo.Status.TODO.getCode().equals(memo.getStatus())) {
            memo.setCompletedAt(null);
        }
        
        boolean result = storeMemoRepository.save(memo);
        if (result) {
            log.info("备忘录创建成功: id={}, content={}", memo.getId(), memo.getContent());
        }
        return result;
    }

    /**
     * 更新备忘录状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMemoStatus(Long id, String status) {
        // 验证状态
        validateMemoStatus(status);
        
        // 检查备忘录是否存在
        Optional<StoreMemo> memoOpt = storeMemoRepository.findById(id);
        if (!memoOpt.isPresent()) {
            throw new ResourceNotFoundException("备忘录不存在: " + id);
        }
        
        StoreMemo memo = memoOpt.get();
        
        // 检查权限（只能修改自己创建的备忘录或同门店的备忘录）
        validateMemoPermission(memo);
        
        // 设置完成时间
        LocalDateTime completedAt = null;
        if (StoreMemo.Status.DONE.getCode().equals(status)) {
            completedAt = LocalDateTime.now();
        }
        
        boolean result = storeMemoRepository.updateStatus(id, status, completedAt);
        if (result) {
            log.info("备忘录状态更新成功: id={}, status={}", id, status);
        }
        return result;
    }

    /**
     * 批量更新备忘录状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMemoStatusBatch(List<Long> ids, String status) {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException("备忘录ID列表不能为空");
        }
        
        // 验证状态
        validateMemoStatus(status);
        
        // 检查所有备忘录的权限
        for (Long id : ids) {
            Optional<StoreMemo> memoOpt = storeMemoRepository.findById(id);
            if (!memoOpt.isPresent()) {
                throw new ResourceNotFoundException("备忘录不存在: " + id);
            }
            validateMemoPermission(memoOpt.get());
        }
        
        // 设置完成时间
        LocalDateTime completedAt = null;
        if (StoreMemo.Status.DONE.getCode().equals(status)) {
            completedAt = LocalDateTime.now();
        }
        
        boolean result = storeMemoRepository.updateStatusBatch(ids, status, completedAt);
        if (result) {
            log.info("批量更新备忘录状态成功: ids={}, status={}", ids, status);
        }
        return result;
    }

    /**
     * 根据ID查询备忘录
     */
    public StoreMemo getMemoById(Long id) {
        Optional<StoreMemo> memoOpt = storeMemoRepository.findById(id);
        if (!memoOpt.isPresent()) {
            throw new ResourceNotFoundException("备忘录不存在: " + id);
        }
        
        StoreMemo memo = memoOpt.get();
        validateMemoPermission(memo);
        
        return memo;
    }

    /**
     * 分页查询备忘录
     */
    public IPage<StoreMemo> getMemosPage(Integer current, Integer size, Long storeId, 
                                        String status, Long creatorId, String content) {
        Page<StoreMemo> page = new Page<>(current, size);
        
        // 如果没有指定门店ID，使用当前用户的门店ID
        if (storeId == null) {
            storeId = UserContext.getCurrentStoreId();
        }
        
        return storeMemoRepository.findPage(page, storeId, status, creatorId, content);
    }

    /**
     * 根据门店ID查询备忘录列表
     */
    public List<StoreMemo> getMemosByStoreId(Long storeId) {
        if (storeId == null) {
            storeId = UserContext.getCurrentStoreId();
        }
        return storeMemoRepository.findByStoreId(storeId);
    }

    /**
     * 根据状态查询备忘录列表
     */
    public List<StoreMemo> getMemosByStatus(String status) {
        validateMemoStatus(status);
        return storeMemoRepository.findByStatus(status);
    }

    /**
     * 根据创建人ID查询备忘录列表
     */
    public List<StoreMemo> getMemosByCreatorId(Long creatorId) {
        return storeMemoRepository.findByCreatorId(creatorId);
    }

    /**
     * 删除备忘录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMemo(Long id) {
        // 检查备忘录是否存在
        Optional<StoreMemo> memoOpt = storeMemoRepository.findById(id);
        if (!memoOpt.isPresent()) {
            throw new ResourceNotFoundException("备忘录不存在: " + id);
        }
        
        StoreMemo memo = memoOpt.get();
        validateMemoPermission(memo);
        
        boolean result = storeMemoRepository.deleteById(id);
        if (result) {
            log.info("备忘录删除成功: id={}", id);
        }
        return result;
    }

    /**
     * 统计备忘录数量
     */
    public MemoStatistics getMemoStatistics(Long storeId) {
        if (storeId == null) {
            storeId = UserContext.getCurrentStoreId();
        }
        
        Long totalCount = storeMemoRepository.countByStoreId(storeId);
        Long todoCount = storeMemoRepository.countByStoreIdAndStatus(storeId, StoreMemo.Status.TODO.getCode());
        Long doneCount = storeMemoRepository.countByStoreIdAndStatus(storeId, StoreMemo.Status.DONE.getCode());
        
        MemoStatistics statistics = new MemoStatistics();
        statistics.setTotalCount(totalCount.intValue());
        statistics.setTodoCount(todoCount.intValue());
        statistics.setDoneCount(doneCount.intValue());
        
        return statistics;
    }

    // ==================== 私有方法 ====================

    /**
     * 验证备忘录内容
     */
    private void validateMemoContent(String content) {
        if (!StringUtils.hasText(content)) {
            throw new BusinessException("备忘录内容不能为空");
        }
        if (content.length() > 1000) {
            throw new BusinessException("备忘录内容不能超过1000个字符");
        }
    }

    /**
     * 验证备忘录状态
     */
    private void validateMemoStatus(String status) {
        if (!StringUtils.hasText(status)) {
            throw new BusinessException("备忘录状态不能为空");
        }
        try {
            StoreMemo.Status.fromCode(status);
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的备忘录状态: " + status);
        }
    }

    /**
     * 验证备忘录权限
     */
    private void validateMemoPermission(StoreMemo memo) {
        Long currentStoreId = UserContext.getCurrentStoreId();
        if (currentStoreId != null && !currentStoreId.equals(memo.getStoreId())) {
            throw new BusinessException("无权限访问该备忘录");
        }
    }

    /**
     * 备忘录统计信息
     */
    public static class MemoStatistics {
        private Integer totalCount;
        private Integer todoCount;
        private Integer doneCount;

        // Getters and Setters
        public Integer getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Integer totalCount) {
            this.totalCount = totalCount;
        }

        public Integer getTodoCount() {
            return todoCount;
        }

        public void setTodoCount(Integer todoCount) {
            this.todoCount = todoCount;
        }

        public Integer getDoneCount() {
            return doneCount;
        }

        public void setDoneCount(Integer doneCount) {
            this.doneCount = doneCount;
        }
    }
}
