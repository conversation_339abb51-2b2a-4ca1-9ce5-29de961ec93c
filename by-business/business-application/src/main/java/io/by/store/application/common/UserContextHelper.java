package io.by.store.application.common;

import lombok.extern.slf4j.Slf4j;

/**
 * 用户上下文辅助工具类
 * 提供一些便捷方法用于用户上下文操作
 */
@Slf4j
public class UserContextHelper {

    /**
     * 创建用户信息对象
     */
    public static UserContext.UserInfo createUserInfo(Long staffId, String username, Long storeId, Long roleId, String token) {
        UserContext.UserInfo userInfo = new UserContext.UserInfo();
        userInfo.setStaffId(staffId);
        userInfo.setUsername(username);
        userInfo.setStoreId(storeId);
        userInfo.setRoleId(roleId);
        userInfo.setToken(token);
        return userInfo;
    }

    /**
     * 设置测试用户上下文（仅用于测试环境）
     */
    public static void setTestUserContext(Long staffId, String username, Long storeId, Long roleId) {
        UserContext.UserInfo userInfo = createUserInfo(staffId, username, storeId, roleId, "test-token");
        UserContext.setCurrentUser(userInfo);
        log.debug("设置测试用户上下文: staffId={}, username={}, storeId={}", staffId, username, storeId);
    }

    /**
     * 执行带用户上下文的操作
     * 执行完成后自动清理上下文
     */
    public static <T> T executeWithUserContext(UserContext.UserInfo userInfo, java.util.function.Supplier<T> supplier) {
        UserContext.UserInfo originalUserInfo = UserContext.getCurrentUser();
        try {
            UserContext.setCurrentUser(userInfo);
            return supplier.get();
        } finally {
            UserContext.setCurrentUser(originalUserInfo);
        }
    }

    /**
     * 执行带用户上下文的操作（无返回值）
     * 执行完成后自动清理上下文
     */
    public static void executeWithUserContext(UserContext.UserInfo userInfo, Runnable runnable) {
        UserContext.UserInfo originalUserInfo = UserContext.getCurrentUser();
        try {
            UserContext.setCurrentUser(userInfo);
            runnable.run();
        } finally {
            UserContext.setCurrentUser(originalUserInfo);
        }
    }

    /**
     * 检查当前是否有有效的用户上下文
     */
    public static boolean hasValidUserContext() {
        UserContext.UserInfo userInfo = UserContext.getCurrentUser();
        return userInfo != null && userInfo.getStaffId() != null;
    }

    /**
     * 获取当前用户的简要信息字符串
     */
    public static String getCurrentUserInfo() {
        UserContext.UserInfo userInfo = UserContext.getCurrentUser();
        if (userInfo == null) {
            return "无用户上下文";
        }
        return String.format("用户[ID=%d, 用户名=%s, 门店ID=%d]", 
                userInfo.getStaffId(), userInfo.getUsername(), userInfo.getStoreId());
    }

    /**
     * 验证当前用户是否属于指定门店
     */
    public static boolean isCurrentUserBelongToStore(Long storeId) {
        if (storeId == null) {
            return false;
        }
        Long currentStoreId = UserContext.getCurrentStoreId();
        return storeId.equals(currentStoreId);
    }

    /**
     * 验证当前用户是否具有指定角色
     */
    public static boolean isCurrentUserHasRole(Long roleId) {
        if (roleId == null) {
            return false;
        }
        Long currentRoleId = UserContext.getCurrentRoleId();
        return roleId.equals(currentRoleId);
    }
}
