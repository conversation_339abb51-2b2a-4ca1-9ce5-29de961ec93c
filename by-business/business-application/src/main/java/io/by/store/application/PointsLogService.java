package io.by.store.application;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.PointsChangeType;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.infrastructure.entity.Member;
import io.by.store.infrastructure.entity.PointsLog;
import io.by.store.infrastructure.mapper.PointsLogMapper;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 积分变动记录服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PointsLogService {

    private final PointsLogMapper pointsLogMapper;
    private final MemberService memberService;

    /**
     * 创建积分变动记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createPointsLog(PointsLog pointsLog) {
        // 验证会员是否存在
        Member member = memberService.getMemberByMemberId(pointsLog.getMemberId());
        if (member == null) {
            throw new ResourceNotFoundException("会员不存在: " + pointsLog.getMemberId());
        }

        // 验证积分变动类型
        try {
            PointsChangeType.fromCode(pointsLog.getChangeType());
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的积分变动类型: " + pointsLog.getChangeType());
        }

        // 验证积分变动数量
        if (pointsLog.getPointsChange() == null || pointsLog.getPointsChange() == 0) {
            throw new BusinessException("积分变动数量不能为0");
        }

        // 如果是扣减积分，检查积分是否足够
        if (pointsLog.getPointsChange() < 0) {
            Long absChange = Math.abs(pointsLog.getPointsChange());
            if (member.getPoints() < absChange) {
                throw new BusinessException("积分不足，当前积分: " + member.getPoints());
            }
        }

        // 设置变动前积分
        pointsLog.setPointsBefore(member.getPoints());
        
        // 计算变动后积分
        Long pointsAfter = member.getPoints() + pointsLog.getPointsChange();
        pointsLog.setPointsAfter(pointsAfter);

        // 设置创建时间
        pointsLog.setCreatedAt(LocalDateTime.now());

        // 插入积分记录
        int result = pointsLogMapper.insert(pointsLog);
        if (result > 0) {
            // 更新会员积分
            boolean pointsUpdated = memberService.updateMemberPoints(
                    pointsLog.getMemberId(), 
                    pointsLog.getPointsChange()
            );
            
            if (pointsUpdated) {
                log.info("积分变动记录创建成功: memberId={}, changeType={}, pointsChange={}", 
                        pointsLog.getMemberId(), pointsLog.getChangeType(), 
                        pointsLog.getPointsChange());
                return true;
            } else {
                throw new BusinessException("积分更新失败");
            }
        }
        return false;
    }

    /**
     * 根据ID查询积分记录
     */
    public PointsLog getPointsLogById(Long id) {
        return pointsLogMapper.selectById(id);
    }

    /**
     * 根据会员ID查询积分记录
     */
    public List<PointsLog> getPointsLogsByMemberId(Long memberId) {
        return pointsLogMapper.findByMemberId(memberId);
    }

    /**
     * 根据变动类型查询积分记录
     */
    public List<PointsLog> getPointsLogsByChangeType(String changeType) {
        return pointsLogMapper.findByChangeType(changeType);
    }

    /**
     * 根据订单ID查询积分记录
     */
    public List<PointsLog> getPointsLogsByOrderId(Long orderId) {
        return pointsLogMapper.findByOrderId(orderId);
    }

    /**
     * 分页查询积分记录
     */
    public IPage<PointsLog> getPointsLogsPage(Integer current, Integer size, 
                                             Long memberId, String changeType, Long orderId,
                                             LocalDateTime startTime, LocalDateTime endTime) {
        Page<PointsLog> page = new Page<>(current, size);
        
        LambdaQueryWrapper<PointsLog> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据会员ID查询
        if (memberId != null) {
            queryWrapper.eq(PointsLog::getMemberId, memberId);
        }
        
        // 根据变动类型查询
        if (StringUtils.hasText(changeType)) {
            queryWrapper.eq(PointsLog::getChangeType, changeType);
        }
        
        // 根据订单ID查询
        if (orderId != null) {
            queryWrapper.eq(PointsLog::getOrderId, orderId);
        }
        
        // 根据时间范围查询
        if (startTime != null && endTime != null) {
            queryWrapper.between(PointsLog::getCreatedAt, startTime, endTime);
        } else if (startTime != null) {
            queryWrapper.ge(PointsLog::getCreatedAt, startTime);
        } else if (endTime != null) {
            queryWrapper.le(PointsLog::getCreatedAt, endTime);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(PointsLog::getCreatedAt);
        
        return pointsLogMapper.selectPage(page, queryWrapper);
    }

    /**
     * 获取积分统计信息
     */
    public PointsStatistics getPointsStatistics() {
        Long todayChange = pointsLogMapper.sumTodayPointsChange();
        Long monthChange = pointsLogMapper.sumMonthPointsChange();
        Integer totalCount = pointsLogMapper.countPointsLogs();
        List<PointsLog> recentLogs = pointsLogMapper.findRecentLogs(10);
        
        PointsStatistics statistics = new PointsStatistics();
        statistics.setTodayChange(todayChange != null ? todayChange : 0L);
        statistics.setMonthChange(monthChange != null ? monthChange : 0L);
        statistics.setTotalCount(totalCount);
        statistics.setRecentLogsCount(recentLogs.size());
        
        return statistics;
    }

    /**
     * 获取会员积分统计
     */
    public MemberPointsStatistics getMemberPointsStatistics(Long memberId) {
        Long totalEarned = pointsLogMapper.sumEarnedPointsByMemberId(memberId);
        Long totalSpent = pointsLogMapper.sumSpentPointsByMemberId(memberId);
        Integer totalCount = pointsLogMapper.countByMemberId(memberId);
        Long maxEarned = pointsLogMapper.findMaxEarnedPointsByMemberId(memberId);
        Long maxSpent = pointsLogMapper.findMaxSpentPointsByMemberId(memberId);
        
        MemberPointsStatistics statistics = new MemberPointsStatistics();
        statistics.setMemberId(memberId);
        statistics.setTotalEarned(totalEarned != null ? totalEarned : 0L);
        statistics.setTotalSpent(totalSpent != null ? totalSpent : 0L);
        statistics.setTotalCount(totalCount);
        statistics.setMaxEarned(maxEarned != null ? maxEarned : 0L);
        statistics.setMaxSpent(maxSpent != null ? maxSpent : 0L);
        statistics.setNetPoints(statistics.getTotalEarned() - statistics.getTotalSpent());
        
        return statistics;
    }

    /**
     * 充值积分奖励
     * 根据充值金额计算并发放积分奖励
     *
     * @param memberId 会员ID
     * @param rechargeAmount 充值金额
     * @param rechargeOrderNumber 充值订单号
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean processRechargePointsReward(Long memberId, BigDecimal rechargeAmount, String rechargeOrderNumber) {
        if (rechargeAmount == null || rechargeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("充值金额无效，跳过积分奖励: memberId={}, amount={}", memberId, rechargeAmount);
            return false;
        }

        // 计算积分奖励（1元=1积分，向下取整）
        Long pointsReward = calculateRechargePoints(rechargeAmount);
        if (pointsReward <= 0) {
            log.info("充值金额不足1元，无积分奖励: memberId={}, amount={}", memberId, rechargeAmount);
            return true; // 不算失败，只是没有奖励
        }

        // 创建积分变动记录
        PointsLog pointsLog = new PointsLog();
        pointsLog.setMemberId(memberId);
        pointsLog.setChangeType(PointsChangeType.EARN_FROM_RECHARGE.getCode());
        pointsLog.setPointsChange(pointsReward);
        pointsLog.setDescription(String.format("充值%.2f元获得积分奖励", rechargeAmount));

        // 如果有充值订单号，可以考虑添加到描述中
        if (StringUtils.hasText(rechargeOrderNumber)) {
            pointsLog.setDescription(pointsLog.getDescription() + "（订单号：" + rechargeOrderNumber + "）");
        }

        boolean success = createPointsLog(pointsLog);
        if (success) {
            log.info("充值积分奖励发放成功: memberId={}, rechargeAmount={}, pointsReward={}",
                    memberId, rechargeAmount, pointsReward);
        } else {
            log.error("充值积分奖励发放失败: memberId={}, rechargeAmount={}, pointsReward={}",
                    memberId, rechargeAmount, pointsReward);
        }

        return success;
    }

    /**
     * 计算充值积分奖励
     * 当前规则：1元 = 1积分（向下取整）
     *
     * @param rechargeAmount 充值金额
     * @return 积分奖励数量
     */
    private Long calculateRechargePoints(BigDecimal rechargeAmount) {
        if (rechargeAmount == null || rechargeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return 0L;
        }

        // 1元 = 1积分，向下取整
        return rechargeAmount.longValue();
    }

    /**
     * 积分统计信息
     */
    @lombok.Data
    public static class PointsStatistics {
        private Long todayChange;
        private Long monthChange;
        private Integer totalCount;
        private Integer recentLogsCount;
    }

    /**
     * 会员积分统计信息
     */
    @lombok.Data
    public static class MemberPointsStatistics {
        private Long memberId;
        private Long totalEarned;
        private Long totalSpent;
        private Long netPoints;
        private Integer totalCount;
        private Long maxEarned;
        private Long maxSpent;
    }
}
