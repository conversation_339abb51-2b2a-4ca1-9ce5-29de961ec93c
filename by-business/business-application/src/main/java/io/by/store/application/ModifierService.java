package io.by.store.application;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.common.BusinessException;
import io.by.store.infrastructure.entity.Modifier;
import io.by.store.infrastructure.repository.ModifierRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 加料服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModifierService {

    private final ModifierRepository modifierRepository;

    /**
     * 创建加料
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createModifier(Modifier modifier) {
        // 检查加料名称是否重复
        Optional<Modifier> existing = modifierRepository.findByNameAndStoreId(
                modifier.getName(), modifier.getStoreId());
        if (existing.isPresent()) {
            throw new BusinessException("加料名称已存在: " + modifier.getName());
        }

        // 设置默认值
        if (modifier.getPriceChange() == null) {
            modifier.setPriceChange(BigDecimal.ZERO);
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        modifier.setCreatedAt(now);
        modifier.setUpdatedAt(now);

        boolean result = modifierRepository.save(modifier);
        if (result) {
            log.info("加料创建成功: {}", modifier.getName());
        }
        return result;
    }

    /**
     * 更新加料
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateModifier(Modifier modifier) {
        // 检查加料是否存在
        Optional<Modifier> existing = modifierRepository.findById(modifier.getId());
        if (!existing.isPresent()) {
            throw new BusinessException("加料不存在，ID: " + modifier.getId());
        }

        // 检查名称是否与其他加料重复
        if (modifierRepository.existsByNameAndStoreIdAndIdNot(
                modifier.getName(), modifier.getStoreId(), modifier.getId())) {
            throw new BusinessException("加料名称已存在: " + modifier.getName());
        }

        // 设置更新时间
        modifier.setUpdatedAt(LocalDateTime.now());

        boolean result = modifierRepository.updateById(modifier);
        if (result) {
            log.info("加料更新成功: {}", modifier.getName());
        }
        return result;
    }

    /**
     * 删除加料
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteModifier(Long id) {
        // 检查加料是否存在
        Optional<Modifier> modifier = modifierRepository.findById(id);
        if (!modifier.isPresent()) {
            throw new BusinessException("加料不存在，ID: " + id);
        }

        boolean result = modifierRepository.deleteById(id);
        if (result) {
            log.info("加料删除成功，ID: {}", id);
        }
        return result;
    }

    /**
     * 根据ID查询加料
     */
    public Optional<Modifier> getModifierById(Long id) {
        return modifierRepository.findById(id);
    }

    /**
     * 根据门店ID查询所有加料
     */
    public List<Modifier> getModifiersByStoreId(Long storeId) {
        return modifierRepository.findByStoreId(storeId);
    }

    /**
     * 分页查询加料
     */
    public IPage<Modifier> queryModifiers(Integer current, Integer size, String name, Long storeId) {
        return modifierRepository.findPage(current, size, name, storeId);
    }

    /**
     * 根据名称模糊查询加料
     */
    public List<Modifier> searchModifiersByName(String name, Long storeId) {
        return modifierRepository.findByNameLike(name, storeId);
    }

    // ==================== 价格管理 ====================

    /**
     * 更新加料价格
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePrice(Long id, BigDecimal priceChange) {
        // 检查加料是否存在
        Optional<Modifier> modifier = modifierRepository.findById(id);
        if (!modifier.isPresent()) {
            throw new BusinessException("加料不存在，ID: " + id);
        }

        if (priceChange.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("价格变动不能为负数");
        }

        boolean result = modifierRepository.updatePriceChange(id, priceChange);
        if (result) {
            log.info("加料价格更新成功，ID: {}, 价格变动: {}", id, priceChange);
        }
        return result;
    }

    /**
     * 根据价格范围查询加料
     */
    public List<Modifier> getModifiersByPriceRange(BigDecimal minPrice, BigDecimal maxPrice, Long storeId) {
        return modifierRepository.findByPriceRange(minPrice, maxPrice, storeId);
    }

    /**
     * 查询免费加料
     */
    public List<Modifier> getFreeModifiers(Long storeId) {
        return modifierRepository.findFreeModifiers(storeId);
    }

    /**
     * 查询收费加料
     */
    public List<Modifier> getPaidModifiers(Long storeId) {
        return modifierRepository.findPaidModifiers(storeId);
    }

    /**
     * 批量更新加料价格
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePriceBatch(List<Long> ids, BigDecimal priceChange) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        if (priceChange.compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("价格变动不能为负数");
        }

        boolean result = modifierRepository.updatePriceChangeBatch(ids, priceChange);
        if (result) {
            log.info("批量更新加料价格成功，数量: {}, 价格变动: {}", ids.size(), priceChange);
        }
        return result;
    }

    // ==================== 统计功能 ====================

    /**
     * 统计门店加料总数
     */
    public Long countModifiersByStoreId(Long storeId) {
        return modifierRepository.countByStoreId(storeId);
    }

    /**
     * 统计免费加料数量
     */
    public Long countFreeModifiers(Long storeId) {
        return modifierRepository.countFreeModifiers(storeId);
    }

    /**
     * 统计收费加料数量
     */
    public Long countPaidModifiers(Long storeId) {
        return modifierRepository.countPaidModifiers(storeId);
    }

    /**
     * 批量删除加料
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteModifiersBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        boolean result = modifierRepository.deleteBatchByIds(ids);
        if (result) {
            log.info("批量删除加料成功，数量: {}", ids.size());
        }
        return result;
    }

    /**
     * 加料统计信息
     */
    public ModifierStatistics getModifierStatistics(Long storeId) {
        ModifierStatistics statistics = new ModifierStatistics();
        statistics.setTotalCount(countModifiersByStoreId(storeId));
        statistics.setFreeCount(countFreeModifiers(storeId));
        statistics.setPaidCount(countPaidModifiers(storeId));
        return statistics;
    }

    /**
     * 加料统计信息类
     */
    @lombok.Data
    public static class ModifierStatistics {
        private Long totalCount;
        private Long freeCount;
        private Long paidCount;
    }
}
