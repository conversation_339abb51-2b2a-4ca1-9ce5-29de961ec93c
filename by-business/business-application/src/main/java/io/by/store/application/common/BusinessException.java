package io.by.store.application.common;

/**
 * 业务异常类
 */
public class BusinessException extends BaseException {
    
    public BusinessException(String message) {
        super(500, message);
    }
    
    public BusinessException(Integer code, String message) {
        super(code, message);
    }
    
    public BusinessException(String message, Throwable cause) {
        super(500, message, cause);
    }
    
    public BusinessException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }
}
