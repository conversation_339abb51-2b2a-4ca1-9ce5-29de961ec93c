package io.by.store.application.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 */
@Slf4j
@Component
public class JwtUtil {

    /**
     * JWT密钥
     */
    @Value("${jwt.secret:by-store-jwt-secret-key-2024}")
    private String secret;

    /**
     * JWT过期时间（小时）
     */
    @Value("${jwt.expiration:24}")
    private int expiration;

    /**
     * Refresh Token过期时间（天）
     */
    @Value("${jwt.refresh-expiration:7}")
    private int refreshExpiration;

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(Long staffId, String username, Long storeId, Long roleId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("staffId", staffId);
        claims.put("username", username);
        claims.put("storeId", storeId);
        claims.put("roleId", roleId);
        claims.put("type", "access");
        
        return generateToken(claims, expiration * 60 * 60 * 1000L); // 转换为毫秒
    }

    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(Long staffId, String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("staffId", staffId);
        claims.put("username", username);
        claims.put("type", "refresh");

        return generateToken(claims, refreshExpiration * 24 * 60 * 60 * 1000L); // 转换为毫秒
    }

    /**
     * 生成小程序访问令牌
     */
    public String generateMiniAccessToken(String openId, Long memberId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("openId", openId);
        claims.put("memberId", memberId);
        claims.put("type", "mini_access");

        return generateToken(claims, expiration * 60 * 60 * 1000L); // 转换为毫秒
    }

    /**
     * 生成令牌
     */
    private String generateToken(Map<String, Object> claims, long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从令牌中获取用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get("username", String.class) : null;
    }

    /**
     * 从令牌中获取员工ID
     */
    public Long getStaffIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get("staffId", Long.class) : null;
    }

    /**
     * 从令牌中获取门店ID
     */
    public Long getStoreIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get("storeId", Long.class) : null;
    }

    /**
     * 从令牌中获取角色ID
     */
    public Long getRoleIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get("roleId", Long.class) : null;
    }

    /**
     * 从令牌中获取令牌类型
     */
    public String getTokenTypeFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get("type", String.class) : null;
    }

    /**
     * 从令牌中获取OpenID
     */
    public String getOpenIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get("openId", String.class) : null;
    }

    /**
     * 从令牌中获取会员ID
     */
    public Long getMemberIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.get("memberId", Long.class) : null;
    }

    /**
     * 验证令牌
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return claims != null && !isTokenExpired(claims);
        } catch (Exception e) {
            log.warn("Token验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查令牌是否过期
     */
    private boolean isTokenExpired(Claims claims) {
        Date expiration = claims.getExpiration();
        return expiration.before(new Date());
    }

    /**
     * 从令牌中获取声明
     */
    private Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            log.warn("Token已过期: {}", e.getMessage());
            return null;
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的Token: {}", e.getMessage());
            return null;
        } catch (MalformedJwtException e) {
            log.warn("Token格式错误: {}", e.getMessage());
            return null;
        } catch (SignatureException e) {
            log.warn("Token签名验证失败: {}", e.getMessage());
            return null;
        } catch (IllegalArgumentException e) {
            log.warn("Token参数错误: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 获取令牌过期时间（毫秒）
     */
    public long getAccessTokenExpiration() {
        return expiration * 60 * 60 * 1000L;
    }

    /**
     * 获取刷新令牌过期时间（毫秒）
     */
    public long getRefreshTokenExpiration() {
        return refreshExpiration * 24 * 60 * 60 * 1000L;
    }
}
