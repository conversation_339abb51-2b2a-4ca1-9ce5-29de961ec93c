package io.by.store.application;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.LevelTag;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.application.common.SnowflakeIdGenerator;
import io.by.store.infrastructure.entity.Member;
import io.by.store.infrastructure.entity.MembershipLevel;
import io.by.store.infrastructure.mapper.MembershipLevelMapper;
import io.by.store.infrastructure.repository.MemberRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 会员服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberService {

    private final MemberRepository memberRepository;
    private final SnowflakeIdGenerator snowflakeIdGenerator;
    private final MembershipLevelMapper membershipLevelMapper;

    @PostConstruct
    public void init() {
        snowflakeIdGenerator.init();
    }

    /**
     * 创建会员（支持微信预注册和多端注册融合）
     */
    @Transactional(rollbackFor = Exception.class)
    public Member createMemberAndReturn(Member member) {
        // 生成会员业务ID
        Long memberId = Long.valueOf(snowflakeIdGenerator.generateMemberId());
        member.setMemberId(memberId);

        // 处理多端注册融合场景
        if (StringUtils.hasText(member.getWxOpenid()) && StringUtils.hasText(member.getPhoneNumber())) {
            Member result = handleMultiPlatformRegistrationAndReturn(member);
            return result;
        }

        // 处理微信预注册场景（只有openid，没有手机号）
        if (StringUtils.hasText(member.getWxOpenid()) && !StringUtils.hasText(member.getPhoneNumber())) {
            boolean success = handleWechatPreRegistration(member);
            return success ? member : null;
        }

        // 处理普通注册场景
        boolean success = handleNormalRegistration(member);
        return success ? member : null;
    }

    /**
     * 创建会员（支持微信预注册和多端注册融合）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createMember(Member member) {
        Member result = createMemberAndReturn(member);
        return result != null;
    }

    /**
     * 处理微信预注册场景
     */
    private boolean handleWechatPreRegistration(Member member) {
        // 检查微信OpenID是否已存在（包含已删除的）
        Optional<Member> existingByOpenid = memberRepository.findByWxOpenidIncludeDeleted(member.getWxOpenid());
        if (existingByOpenid.isPresent()) {
            throw new BusinessException("微信OpenID已存在: " + member.getWxOpenid());
        }

        // 设置预注册用户的默认值
        member.setPhoneNumber(null);
        member.setNickname("微信预注册用户");

        return createMemberWithDefaults(member);
    }

    /**
     * 处理多端注册融合场景（返回Member对象）
     */
    private Member handleMultiPlatformRegistrationAndReturn(Member member) {
        // 通过手机号查询是否已存在会员
        Optional<Member> existingByPhone = memberRepository.findByPhoneNumberIncludeDeleted(member.getPhoneNumber());

        if (existingByPhone.isPresent()) {
            // 手机号已存在，更新该会员的openid
            Member existingMember = existingByPhone.get();
            existingMember.setWxOpenid(member.getWxOpenid());
            existingMember.setWxUnionid(member.getWxUnionid());
            existingMember.setUpdatedAt(LocalDateTime.now());

            // 如果传入了昵称和头像，也更新
            if (StringUtils.hasText(member.getNickname())) {
                existingMember.setNickname(member.getNickname());
            }
            if (StringUtils.hasText(member.getAvatarUrl())) {
                existingMember.setAvatarUrl(member.getAvatarUrl());
            }

            boolean updateResult = memberRepository.updateById(existingMember);

            if (updateResult) {
                // 逻辑删除预注册用户（有openid无手机号的用户）
                deletePreRegistrationUser(member.getWxOpenid());
                log.info("多端注册融合成功: memberId={}, 手机号={}", existingMember.getMemberId(), member.getPhoneNumber());
                return existingMember;
            }

            return null;
        } else {
            // 手机号不存在，检查openid是否已存在
            Optional<Member> existingByOpenid = memberRepository.findByWxOpenidIncludeDeleted(member.getWxOpenid());
            if (existingByOpenid.isPresent()) {
                throw new BusinessException("微信OpenID已存在: " + member.getWxOpenid());
            }

            // 创建新会员
            boolean success = createMemberWithDefaults(member);
            return success ? member : null;
        }
    }

    /**
     * 处理多端注册融合场景
     */
    private boolean handleMultiPlatformRegistration(Member member) {
        Member result = handleMultiPlatformRegistrationAndReturn(member);
        return result != null;
    }

    /**
     * 处理普通注册场景
     */
    private boolean handleNormalRegistration(Member member) {
        // 检查手机号是否重复
        if (StringUtils.hasText(member.getPhoneNumber())) {
            Optional<Member> existingByPhone = memberRepository.findByPhoneNumber(member.getPhoneNumber());
            if (existingByPhone.isPresent()) {
                throw new BusinessException("手机号已存在: " + member.getPhoneNumber());
            }
        }

        // 检查微信OpenID是否重复
        if (StringUtils.hasText(member.getWxOpenid())) {
            Optional<Member> existingByOpenid = memberRepository.findByWxOpenid(member.getWxOpenid());
            if (existingByOpenid.isPresent()) {
                throw new BusinessException("微信OpenID已存在: " + member.getWxOpenid());
            }
        } else {
            member.setWxOpenid(null);
        }

        // 检查微信UnionID是否重复
        if (StringUtils.hasText(member.getWxUnionid())) {
            Optional<Member> existingByUnionid = memberRepository.findByWxUnionid(member.getWxUnionid());
            if (existingByUnionid.isPresent()) {
                throw new BusinessException("微信UnionID已存在: " + member.getWxUnionid());
            }
        } else {
            member.setWxUnionid(null);
        }

        return createMemberWithDefaults(member);
    }

    /**
     * 创建会员并设置默认值
     */
    private boolean createMemberWithDefaults(Member member) {
        // 设置默认值
        if (ObjectUtil.isNull(member.getBalance())) {
            member.setBalance(BigDecimal.ZERO);
        }
        if (ObjectUtil.isNull(member.getPoints())) {
            member.setPoints(0L);
        }
        if (ObjectUtil.isNull(member.getIsDelete())) {
            member.setIsDelete(0);
        }
        if (ObjectUtil.isNull(member.getMembershipLevelId())) {
            MembershipLevel existingByTag = membershipLevelMapper.findByLevelTag(LevelTag.NORMAL.getTag());
            member.setMembershipLevelId(existingByTag.getId());
            member.setMembershipLevelName(existingByTag.getLevelName());
        } else {
            // 如果指定了会员等级ID，需要同步设置等级名称
            syncMembershipLevelName(member);
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        member.setCreatedAt(now);
        member.setUpdatedAt(now);

        boolean result = memberRepository.save(member);
        if (result) {
            log.info("会员创建成功: memberId={}, nickname={}", member.getMemberId(), member.getNickname());
            return true;
        }
        return false;
    }

    /**
     * 删除预注册用户（有openid无手机号的用户）
     */
    private void deletePreRegistrationUser(String wxOpenid) {
        try {
            // 查找有相同openid但没有手机号的预注册用户
            Optional<Member> preRegUser = memberRepository.findByWxOpenidIncludeDeleted(wxOpenid);
            if (preRegUser.isPresent()) {
                Member user = preRegUser.get();
                if (!StringUtils.hasText(user.getPhoneNumber()) && user.getIsDelete() == 0) {
                    memberRepository.logicalDeleteById(user.getId());
                    log.info("逻辑删除预注册用户成功: memberId={}", user.getMemberId());
                }
            }
        } catch (Exception e) {
            log.warn("删除预注册用户失败: {}", e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 更新会员信息
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMember(Member member) {
        // 检查会员是否存在
        Optional<Member> existingOpt = memberRepository.findByMemberId(member.getMemberId());
        if (!existingOpt.isPresent()) {
            throw new ResourceNotFoundException("会员不存在: " + member.getId());
        }
        Member existing = existingOpt.get();

        // 如果修改了手机号，检查新手机号是否重复
        if (StringUtils.hasText(member.getPhoneNumber()) &&
                !member.getPhoneNumber().equals(existing.getPhoneNumber())) {
            Optional<Member> phoneExists = memberRepository.findByPhoneNumber(member.getPhoneNumber());
            if (phoneExists.isPresent() && !phoneExists.get().getId().equals(member.getId())) {
                throw new BusinessException("手机号已存在: " + member.getPhoneNumber());
            }
        }

        // 如果修改了微信OpenID，检查是否重复
        if (StringUtils.hasText(member.getWxOpenid()) &&
                !member.getWxOpenid().equals(existing.getWxOpenid())) {
            Optional<Member> openidExists = memberRepository.findByWxOpenid(member.getWxOpenid());
            if (openidExists.isPresent() && !openidExists.get().getId().equals(member.getId())) {
                throw new BusinessException("微信OpenID已存在: " + member.getWxOpenid());
            }
        }

        // 保留原有的会员业务ID、余额、积分、创建时间
        member.setMemberId(existing.getMemberId());
        member.setBalance(existing.getBalance());
        member.setPoints(existing.getPoints());
        member.setCreatedAt(existing.getCreatedAt());
        member.setId(existing.getId());

        // 如果更新了会员等级ID，需要同步更新等级名称
        if (member.getMembershipLevelId() != null &&
                !member.getMembershipLevelId().equals(existing.getMembershipLevelId())) {
            syncMembershipLevelName(member);
        } else if (member.getMembershipLevelId() == null) {
            // 如果没有指定等级ID，保留原有的等级信息
            member.setMembershipLevelId(existing.getMembershipLevelId());
            member.setMembershipLevelName(existing.getMembershipLevelName());
        }

        boolean result = memberRepository.updateById(member);
        if (result) {
            log.info("会员信息更新成功: memberId={}", member.getMemberId());
            return true;
        }
        return false;
    }

    /**
     * 删除会员（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMember(Long id) {
        Optional<Member> memberOpt = memberRepository.findById(id);
        if (!memberOpt.isPresent()) {
            throw new ResourceNotFoundException("会员不存在: " + id);
        }
        Member member = memberOpt.get();

        boolean result = memberRepository.logicalDeleteById(id);
        if (result) {
            log.info("会员逻辑删除成功: memberId={}", member.getMemberId());
            return true;
        }
        return false;
    }

    /**
     * 根据ID查询会员
     */
    public Member getMemberById(Long id) {
        return memberRepository.findById(id).orElse(null);
    }

    /**
     * 根据会员业务ID查询会员
     */
    public Member getMemberByMemberId(Long memberId) {
        return memberRepository.findByMemberId(memberId).orElse(null);
    }

    /**
     * 根据手机号查询会员
     */
    public Member getMemberByPhoneNumber(String phoneNumber) {
        return memberRepository.findByPhoneNumber(phoneNumber).orElse(null);
    }

    /**
     * 根据微信OpenID查询会员
     */
    public Member getMemberByWxOpenid(String wxOpenid) {
        return memberRepository.findByWxOpenid(wxOpenid).orElse(null);
    }

    /**
     * 分页查询会员
     */
    public IPage<Member> getMembersPage(Integer current, Integer size, String nickname,
                                        String phoneNumber, Long membershipLevelId, String membershipLevelName) {
        Page<Member> page = new Page<>(current, size);
        return memberRepository.findPage(page, nickname, phoneNumber, membershipLevelId, membershipLevelName);
    }

    /**
     * 同步会员等级名称
     * 根据会员等级ID设置对应的等级名称
     */
    private void syncMembershipLevelName(Member member) {
        if (member.getMembershipLevelId() != null) {
            MembershipLevel level = membershipLevelMapper.selectById(member.getMembershipLevelId());
            if (level != null) {
                member.setMembershipLevelName(level.getLevelName());
            } else {
                log.warn("会员等级ID不存在: {}", member.getMembershipLevelId());
                member.setMembershipLevelName(null);
            }
        } else {
            member.setMembershipLevelName(null);
        }
    }

    /**
     * 根据积分自动升级会员等级
     *
     * @param memberId 会员ID
     * @return 是否升级成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean upgradeByPoints(Long memberId) {
        Optional<Member> memberOpt = memberRepository.findById(memberId);
        if (!memberOpt.isPresent()) {
            throw new ResourceNotFoundException("会员不存在: " + memberId);
        }

        Member member = memberOpt.get();
        Long currentPoints = member.getPoints() != null ? member.getPoints() : 0L;

        // 根据积分获取应该的等级
        MembershipLevel targetLevel = membershipLevelMapper.findLevelByPoints(currentPoints);
        if (targetLevel == null) {
            log.warn("未找到积分{}对应的会员等级", currentPoints);
            return false;
        }

        // 如果等级没有变化，不需要升级
        if (targetLevel.getId().equals(member.getMembershipLevelId())) {
            return true;
        }

        // 更新会员等级
        member.setMembershipLevelId(targetLevel.getId());
        member.setMembershipLevelName(targetLevel.getLevelName());

        boolean result = memberRepository.updateById(member);
        if (result) {
            log.info("会员等级升级成功: memberId={}, 新等级={}",
                    member.getMemberId(), targetLevel.getLevelName());
        }

        return result;
    }

    /**
     * 更新会员余额
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMemberBalance(Long memberId, BigDecimal amount) {
        Optional<Member> memberOpt = memberRepository.findByMemberId(memberId);
        if (!memberOpt.isPresent()) {
            throw new ResourceNotFoundException("会员不存在: " + memberId);
        }
        Member member = memberOpt.get();

        // 检查余额是否足够（如果是扣减操作）
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            BigDecimal newBalance = member.getBalance().add(amount);
            if (newBalance.compareTo(BigDecimal.ZERO) < 0) {
                throw new BusinessException("余额不足，当前余额: " + member.getBalance());
            }
        }

        boolean result = memberRepository.updateBalance(memberId, amount);
        if (result) {
            log.info("会员余额更新成功: memberId={}, amount={}", member.getMemberId(), amount);
            return true;
        }
        return false;
    }

    /**
     * 更新会员积分
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMemberPoints(Long memberId, Long points) {
        Optional<Member> memberOpt = memberRepository.findByMemberId(memberId);
        if (!memberOpt.isPresent()) {
            throw new ResourceNotFoundException("会员不存在: " + memberId);
        }
        Member member = memberOpt.get();

        // 检查积分是否足够（如果是扣减操作）
        if (points < 0) {
            Long newPoints = member.getPoints() + points;
            if (newPoints < 0) {
                throw new BusinessException("积分不足，当前积分: " + member.getPoints());
            }
        }

        boolean result = memberRepository.updatePoints(memberId, points);
        if (result) {
            log.info("会员积分更新成功: memberId={}, points={}", member.getMemberId(), points);
            return true;
        }
        return false;
    }

    /**
     * 获取会员统计信息
     */
    public MemberStatistics getMemberStatistics() {
        Long totalMembers = memberRepository.countMembers();
        List<Member> recentMembers = memberRepository.findRecentMembers(10);

        MemberStatistics statistics = new MemberStatistics();
        statistics.setTotalMembers(totalMembers.intValue());
        statistics.setRecentMembersCount(recentMembers.size());

        return statistics;
    }

    /**
     * 会员统计信息
     */
    @lombok.Data
    public static class MemberStatistics {
        private Integer totalMembers;
        private Integer recentMembersCount;
    }
}
