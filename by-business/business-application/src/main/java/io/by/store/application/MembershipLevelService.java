package io.by.store.application;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.LevelTag;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.infrastructure.entity.MembershipLevel;
import io.by.store.infrastructure.mapper.MembershipLevelMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员等级服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MembershipLevelService {

    private final MembershipLevelMapper membershipLevelMapper;

//    /**
//     * 初始化默认会员等级数据
//     */
//    @PostConstruct
//    @Transactional(rollbackFor = Exception.class)
//    public void initDefaultLevels() {
//        // 检查是否已有数据
//        Integer count = membershipLevelMapper.countActiveLevels();
//        if (count > 0) {
//            log.info("会员等级数据已存在，跳过初始化");
//            return;
//        }
//
//        log.info("开始初始化默认会员等级数据");
//
//        // 创建默认等级数据
//        for (LevelTag levelTag : LevelTag.values()) {
//            MembershipLevel level = new MembershipLevel();
//            level.setLevelName(levelTag.getLevelName());
//            level.setLevelTag(levelTag.getTag());
//            level.setUpgradePointsThreshold(levelTag.getUpgradePointsThreshold());
//            level.setDescription(levelTag.getDescription());
//            level.setIsActive(true);
//            level.setCreatedAt(LocalDateTime.now());
//            level.setUpdatedAt(LocalDateTime.now());
//
//            membershipLevelMapper.insert(level);
//            log.info("创建默认等级: {}", levelTag.getLevelName());
//        }
//
//        log.info("默认会员等级数据初始化完成");
//    }

    /**
     * 创建会员等级
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createMembershipLevel(MembershipLevel level) {
        // 检查等级标签是否重复
        MembershipLevel existingByTag = membershipLevelMapper.findByLevelTag(level.getLevelTag());
        if (existingByTag != null) {
            throw new BusinessException("等级标签已存在: " + level.getLevelTag());
        }

        // 验证等级标签是否有效
        try {
            LevelTag.fromTag(level.getLevelTag());
        } catch (IllegalArgumentException e) {
            log.warn("使用自定义等级标签: {}", level.getLevelTag());
        }

        // 设置默认值
        if (level.getIsActive() == null) {
            level.setIsActive(true);
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        level.setCreatedAt(now);
        level.setUpdatedAt(now);

        int result = membershipLevelMapper.insert(level);
        if (result > 0) {
            log.info("会员等级创建成功: {}", level.getLevelName());
            return true;
        }
        return false;
    }

    /**
     * 更新会员等级
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMembershipLevel(MembershipLevel level) {
        // 检查等级是否存在
        MembershipLevel existing = membershipLevelMapper.selectById(level.getId());
        if (existing == null) {
            throw new ResourceNotFoundException("会员等级不存在: " + level.getId());
        }

        // 如果修改了等级标签，检查新标签是否重复
        if (!existing.getLevelTag().equals(level.getLevelTag())) {
            MembershipLevel tagExists = membershipLevelMapper.findByLevelTag(level.getLevelTag());
            if (tagExists != null && !tagExists.getId().equals(level.getId())) {
                throw new BusinessException("等级标签已存在: " + level.getLevelTag());
            }
        }

        // 保留创建时间
        level.setCreatedAt(existing.getCreatedAt());
        
        // 设置更新时间
        level.setUpdatedAt(LocalDateTime.now());

        int result = membershipLevelMapper.updateById(level);
        if (result > 0) {
            log.info("会员等级更新成功: {}", level.getLevelName());
            return true;
        }
        return false;
    }

    /**
     * 删除会员等级
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMembershipLevel(Long id) {
        MembershipLevel level = membershipLevelMapper.selectById(id);
        if (level == null) {
            throw new ResourceNotFoundException("会员等级不存在: " + id);
        }

        int result = membershipLevelMapper.deleteById(id);
        if (result > 0) {
            log.info("会员等级删除成功: {}", level.getLevelName());
            return true;
        }
        return false;
    }

    /**
     * 根据ID查询会员等级
     */
    public MembershipLevel getMembershipLevelById(Long id) {
        return membershipLevelMapper.selectById(id);
    }

    /**
     * 根据等级标签查询会员等级
     */
    public MembershipLevel getMembershipLevelByTag(String levelTag) {
        return membershipLevelMapper.findByLevelTag(levelTag);
    }

    /**
     * 查询所有启用的会员等级
     */
    public List<MembershipLevel> getAllActiveLevels() {
        return membershipLevelMapper.findAllActiveLevels();
    }

    /**
     * 分页查询会员等级
     */
    public IPage<MembershipLevel> getMembershipLevelsPage(Integer current, Integer size, 
                                                         String levelName, Boolean isActive) {
        Page<MembershipLevel> page = new Page<>(current, size);
        
        LambdaQueryWrapper<MembershipLevel> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据等级名称模糊查询
        if (StringUtils.hasText(levelName)) {
            queryWrapper.like(MembershipLevel::getLevelName, levelName);
        }
        
        // 根据状态查询
        if (isActive != null) {
            queryWrapper.eq(MembershipLevel::getIsActive, isActive);
        }
        
        // 按积分阈值升序排列
        queryWrapper.orderByAsc(MembershipLevel::getUpgradePointsThreshold);
        
        return membershipLevelMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据积分获取对应的会员等级
     */
    public MembershipLevel getLevelByPoints(Long points) {
        if (points == null || points < 0) {
            points = 0L;
        }
        return membershipLevelMapper.findLevelByPoints(points);
    }

    /**
     * 获取下一个等级
     */
    public MembershipLevel getNextLevel(Long currentThreshold) {
        return membershipLevelMapper.findNextLevel(currentThreshold);
    }

    /**
     * 启用/禁用会员等级
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleMembershipLevelStatus(Long id) {
        MembershipLevel level = membershipLevelMapper.selectById(id);
        if (level == null) {
            throw new ResourceNotFoundException("会员等级不存在: " + id);
        }

        level.setIsActive(!level.getIsActive());
        level.setUpdatedAt(LocalDateTime.now());

        int result = membershipLevelMapper.updateById(level);
        if (result > 0) {
            log.info("会员等级状态更新成功: {} -> {}", level.getLevelName(), 
                    level.getIsActive() ? "启用" : "禁用");
            return true;
        }
        return false;
    }

    /**
     * 获取会员等级统计信息
     */
    public LevelStatistics getLevelStatistics() {
        Integer totalLevels = membershipLevelMapper.countActiveLevels();
        MembershipLevel maxLevel = membershipLevelMapper.findMaxLevel();
        MembershipLevel minLevel = membershipLevelMapper.findMinLevel();
        
        LevelStatistics statistics = new LevelStatistics();
        statistics.setTotalActiveLevels(totalLevels);
        statistics.setMaxLevel(maxLevel);
        statistics.setMinLevel(minLevel);
        
        return statistics;
    }

    /**
     * 计算升级到下一等级所需积分
     */
    public Long getPointsToNextLevel(Long currentPoints) {
        MembershipLevel currentLevel = getLevelByPoints(currentPoints);
        if (currentLevel == null) {
            return 0L;
        }
        
        MembershipLevel nextLevel = getNextLevel(currentLevel.getUpgradePointsThreshold());
        if (nextLevel == null) {
            return 0L; // 已经是最高等级
        }
        
        return Math.max(0L, nextLevel.getUpgradePointsThreshold() - (currentPoints != null ? currentPoints : 0L));
    }

    /**
     * 会员等级统计信息
     */
    @lombok.Data
    public static class LevelStatistics {
        private Integer totalActiveLevels;
        private MembershipLevel maxLevel;
        private MembershipLevel minLevel;
    }
}
