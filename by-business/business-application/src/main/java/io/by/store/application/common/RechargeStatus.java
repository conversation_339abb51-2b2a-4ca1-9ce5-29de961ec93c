package io.by.store.application.common;

import lombok.Getter;

/**
 * 充值订单状态枚举
 */
@Getter
public enum RechargeStatus {
    
    /**
     * 待处理
     */
    PENDING("pending", "待处理", "充值订单已创建，等待支付确认"),
    
    /**
     * 成功
     */
    SUCCESSFUL("successful", "成功", "充值成功，余额已到账"),
    
    /**
     * 失败
     */
    FAILED("failed", "失败", "充值失败，请重新尝试"),
    
    /**
     * 退款
     */
    REFUNDED("refunded", "退款", "充值已退款，余额已扣减");

    /**
     * 状态代码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;

    RechargeStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static RechargeStatus fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (RechargeStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("未知的充值状态: " + code);
    }

    /**
     * 检查是否是最终状态（不可再变更）
     */
    public boolean isFinalStatus() {
        return this == SUCCESSFUL || this == FAILED || this == REFUNDED;
    }

    /**
     * 检查是否是成功状态
     */
    public boolean isSuccessful() {
        return this == SUCCESSFUL;
    }

    /**
     * 检查是否是失败状态
     */
    public boolean isFailed() {
        return this == FAILED;
    }

    /**
     * 检查是否是待处理状态
     */
    public boolean isPending() {
        return this == PENDING;
    }

    /**
     * 检查是否是退款状态
     */
    public boolean isRefunded() {
        return this == REFUNDED;
    }

    /**
     * 检查是否可以退款
     */
    public boolean canRefund() {
        return this == SUCCESSFUL;
    }

    /**
     * 检查是否可以重新支付
     */
    public boolean canRetry() {
        return this == FAILED;
    }

    /**
     * 获取状态的显示名称
     */
    public String getDisplayName() {
        return this.name;
    }

    /**
     * 获取下一个可能的状态
     */
    public RechargeStatus[] getNextPossibleStatuses() {
        switch (this) {
            case PENDING:
                return new RechargeStatus[]{SUCCESSFUL, FAILED};
            case SUCCESSFUL:
                return new RechargeStatus[]{REFUNDED};
            case FAILED:
            case REFUNDED:
            default:
                return new RechargeStatus[0]; // 最终状态，无后续状态
        }
    }
}
