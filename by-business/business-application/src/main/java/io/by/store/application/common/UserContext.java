package io.by.store.application.common;

import com.alibaba.ttl.TransmittableThreadLocal;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户上下文工具类
 * 使用TransmittableThreadLocal存储用户信息，支持异步场景
 */
@Slf4j
public class UserContext {

    /**
     * 使用TransmittableThreadLocal存储用户信息
     * 支持在异步线程中传递用户上下文
     */
    private static final TransmittableThreadLocal<UserInfo> USER_CONTEXT = new TransmittableThreadLocal<>();

    /**
     * 设置当前用户信息
     */
    public static void setCurrentUser(UserInfo userInfo) {
        if (userInfo == null) {
            USER_CONTEXT.remove();
        } else {
            USER_CONTEXT.set(userInfo);
        }
        log.debug("设置用户上下文: {}", userInfo != null ? userInfo.getUsername() : "null");
    }

    /**
     * 获取当前用户信息
     */
    public static UserInfo getCurrentUser() {
        return USER_CONTEXT.get();
    }

    /**
     * 清除当前用户信息
     */
    public static void clear() {
        USER_CONTEXT.remove();
        log.debug("清除用户上下文");
    }

    /**
     * 获取当前用户ID
     */
    public static Long getCurrentStaffId() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getStaffId() : null;
    }

    /**
     * 获取当前用户名
     */
    public static String getCurrentUsername() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getUsername() : null;
    }

    /**
     * 获取当前门店ID
     */
    public static Long getCurrentStoreId() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getStoreId() : null;
    }

    /**
     * 获取当前角色ID
     */
    public static Long getCurrentRoleId() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getRoleId() : null;
    }

    /**
     * 获取当前访问令牌
     */
    public static String getCurrentToken() {
        UserInfo userInfo = getCurrentUser();
        return userInfo != null ? userInfo.getToken() : null;
    }

    /**
     * 检查是否有用户上下文
     */
    public static boolean hasUserContext() {
        return getCurrentUser() != null;
    }

    /**
     * 用户信息
     */
    @Data
    public static class UserInfo {
        private Long staffId;
        private String username;
        private Long storeId;
        private Long roleId;
        private String token;
    }
}
