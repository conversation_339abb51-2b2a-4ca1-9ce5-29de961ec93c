package io.by.store.application;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.common.BusinessException;
import io.by.store.infrastructure.entity.ProductCategory;
import io.by.store.infrastructure.repository.ProductCategoryRepository;
import io.by.store.infrastructure.repository.ProductRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 商品分类服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductCategoryService {

    private final ProductCategoryRepository categoryRepository;
    private final ProductRepository productRepository;

    /**
     * 创建分类
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createCategory(ProductCategory category) {
        // 检查分类名称是否重复
        Optional<ProductCategory> existing = categoryRepository.findByNameAndStoreId(
                category.getName(), category.getStoreId());
        if (existing.isPresent()) {
            throw new BusinessException("分类名称已存在: " + category.getName());
        }

        // 设置默认排序值
        if (category.getSortOrder() == null) {
            Integer maxSortOrder = categoryRepository.findMaxSortOrder(
                    category.getStoreId(), category.getParentId());
            category.setSortOrder(maxSortOrder + 1);
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        category.setCreatedAt(now);
        category.setUpdatedAt(now);

        boolean result = categoryRepository.save(category);
        if (result) {
            log.info("分类创建成功: {}", category.getName());
        }
        return result;
    }

    /**
     * 更新分类
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(ProductCategory category) {
        // 检查分类是否存在
        Optional<ProductCategory> existing = categoryRepository.findById(category.getId());
        if (!existing.isPresent()) {
            throw new BusinessException("分类不存在，ID: " + category.getId());
        }

        // 检查名称是否与其他分类重复
        if (categoryRepository.existsByNameAndStoreIdAndIdNot(
                category.getName(), category.getStoreId(), category.getId())) {
            throw new BusinessException("分类名称已存在: " + category.getName());
        }

        // 设置更新时间
        category.setUpdatedAt(LocalDateTime.now());

        boolean result = categoryRepository.updateById(category);
        if (result) {
            log.info("分类更新成功: {}", category.getName());
        }
        return result;
    }

    /**
     * 删除分类
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Long id) {
        // 检查分类是否存在
        Optional<ProductCategory> category = categoryRepository.findById(id);
        if (!category.isPresent()) {
            throw new BusinessException("分类不存在，ID: " + id);
        }

        // 检查是否有子分类
        Long childCount = categoryRepository.countByParentId(id);
        if (childCount > 0) {
            throw new BusinessException("该分类下还有子分类，无法删除");
        }

        // 检查是否有商品
        Long productCount = productRepository.countByCategoryId(id);
        if (productCount > 0) {
            throw new BusinessException("该分类下还有商品，无法删除");
        }

        boolean result = categoryRepository.deleteById(id);
        if (result) {
            log.info("分类删除成功，ID: {}", id);
        }
        return result;
    }

    /**
     * 根据ID查询分类
     */
    public Optional<ProductCategory> getCategoryById(Long id) {
        return categoryRepository.findById(id);
    }

    /**
     * 根据门店ID查询所有分类
     */
    public List<ProductCategory> getCategoriesByStoreId(Long storeId) {
        return categoryRepository.findByStoreId(storeId);
    }

    /**
     * 查询顶级分类
     */
    public List<ProductCategory> getTopLevelCategories(Long storeId) {
        return categoryRepository.findTopLevelCategories(storeId);
    }

    /**
     * 根据父分类ID查询子分类
     */
    public List<ProductCategory> getChildCategories(Long storeId, Long parentId) {
        return categoryRepository.findByStoreIdAndParentId(storeId, parentId);
    }

    /**
     * 根据门店ID和父分类ID查询分类列表
     */
    public List<ProductCategory> getCategoriesByStoreIdAndParentId(Long storeId, Long parentId) {
        return categoryRepository.findByStoreIdAndParentId(storeId, parentId);
    }

    /**
     * 分页查询分类
     */
    public IPage<ProductCategory> queryCategories(Integer current, Integer size, 
                                                 String name, Long parentId, Long storeId) {
        return categoryRepository.findPage(current, size, name, parentId, storeId);
    }

    /**
     * 更新分类排序
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortOrder(Long id, Integer sortOrder) {
        // 检查分类是否存在
        Optional<ProductCategory> category = categoryRepository.findById(id);
        if (!category.isPresent()) {
            throw new BusinessException("分类不存在，ID: " + id);
        }

        boolean result = categoryRepository.updateSortOrder(id, sortOrder);
        if (result) {
            log.info("分类排序更新成功，ID: {}, 排序: {}", id, sortOrder);
        }
        return result;
    }

    /**
     * 根据名称模糊查询分类
     */
    public List<ProductCategory> searchCategoriesByName(String name, Long storeId) {
        return categoryRepository.findByNameLike(name, storeId);
    }

    /**
     * 统计门店分类总数
     */
    public Long countCategoriesByStoreId(Long storeId) {
        return categoryRepository.countByStoreId(storeId);
    }

    /**
     * 批量删除分类
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategoriesBatch(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 检查每个分类是否可以删除
        for (Long id : ids) {
            // 检查是否有子分类
            Long childCount = categoryRepository.countByParentId(id);
            if (childCount > 0) {
                throw new BusinessException("分类ID " + id + " 下还有子分类，无法删除");
            }

            // 检查是否有商品
            Long productCount = productRepository.countByCategoryId(id);
            if (productCount > 0) {
                throw new BusinessException("分类ID " + id + " 下还有商品，无法删除");
            }
        }

        boolean result = categoryRepository.deleteBatchByIds(ids);
        if (result) {
            log.info("批量删除分类成功，数量: {}", ids.size());
        }
        return result;
    }
}
