package io.by.store.application;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.infrastructure.entity.Role;
import io.by.store.infrastructure.mapper.RoleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleService {

    private final RoleMapper roleMapper;

    /**
     * 创建角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(Role role) {
        // 检查角色名称是否重复
        Role existing = roleMapper.findByName(role.getName());
        if (existing != null) {
            throw new BusinessException("角色名称已存在: " + role.getName());
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        role.setCreatedAt(now);
        role.setUpdatedAt(now);

        int result = roleMapper.insert(role);
        if (result > 0) {
            log.info("角色创建成功: {}", role.getName());
            return true;
        }
        return false;
    }

    /**
     * 更新角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(Role role) {
        // 检查角色是否存在
        Role existing = roleMapper.selectById(role.getId());
        if (existing == null) {
            throw new ResourceNotFoundException("角色不存在: " + role.getId());
        }

        // 如果修改了角色名称，检查新名称是否重复
        if (!existing.getName().equals(role.getName())) {
            Role nameExists = roleMapper.findByName(role.getName());
            if (nameExists != null && !nameExists.getId().equals(role.getId())) {
                throw new BusinessException("角色名称已存在: " + role.getName());
            }
        }

        // 设置更新时间
        role.setUpdatedAt(LocalDateTime.now());

        int result = roleMapper.updateById(role);
        if (result > 0) {
            log.info("角色更新成功: {}", role.getName());
            return true;
        }
        return false;
    }

    /**
     * 删除角色
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long id) {
        Role role = roleMapper.selectById(id);
        if (role == null) {
            throw new ResourceNotFoundException("角色不存在: " + id);
        }

        int result = roleMapper.deleteById(id);
        if (result > 0) {
            log.info("角色删除成功: {}", role.getName());
            return true;
        }
        return false;
    }

    /**
     * 根据ID查询角色
     */
    public Role getRoleById(Long id) {
        return roleMapper.selectById(id);
    }

    /**
     * 根据名称查询角色
     */
    public Role getRoleByName(String name) {
        return roleMapper.findByName(name);
    }

    /**
     * 查询所有角色
     */
    public List<Role> getAllRoles() {
        return roleMapper.findAllRoles();
    }

    /**
     * 分页查询角色
     */
    public IPage<Role> getRolesPage(Integer current, Integer size, String name, String description) {
        Page<Role> page = new Page<>(current, size);
        
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据角色名称模糊查询
        if (StringUtils.hasText(name)) {
            queryWrapper.like(Role::getName, name);
        }
        
        // 根据描述模糊查询
        if (StringUtils.hasText(description)) {
            queryWrapper.like(Role::getDescription, description);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Role::getCreatedAt);
        
        return roleMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据权限查询角色列表
     */
    public List<Role> getRolesByPermission(String permission) {
        return roleMapper.findByPermission(permission);
    }
}
