package io.by.store.application;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.infrastructure.entity.Store;
import io.by.store.infrastructure.repository.StoreRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 门店服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StoreService {

    private final StoreRepository storeRepository;

    /**
     * 创建门店
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createStore(Store store) {
        // 验证门店名称唯一性
        if (storeRepository.existsByName(store.getName())) {
            throw new BusinessException("门店名称已存在: " + store.getName());
        }

        // 验证电话号码唯一性
        if (StringUtils.hasText(store.getPhoneNumber()) &&
            storeRepository.existsByPhoneNumber(store.getPhoneNumber())) {
            throw new BusinessException("电话号码已存在: " + store.getPhoneNumber());
        }

        // 设置默认值
        if (store.getIsActive() == null) {
            store.setIsActive(true);
        }

        boolean result = storeRepository.save(store);
        if (result) {
            log.info("门店创建成功: {}", store.getName());
            return true;
        }
        throw new BusinessException("门店创建失败");
    }

    /**
     * 更新门店信息
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStore(Store store) {
        // 验证门店是否存在
        Optional<Store> existingStore = storeRepository.findById(store.getId());
        if (!existingStore.isPresent()) {
            throw new ResourceNotFoundException("门店不存在: " + store.getId());
        }

        // 验证门店名称唯一性（排除自己）
        if (storeRepository.existsByNameAndIdNot(store.getName(), store.getId())) {
            throw new BusinessException("门店名称已存在: " + store.getName());
        }

        // 验证电话号码唯一性（排除自己）
        if (StringUtils.hasText(store.getPhoneNumber()) &&
            storeRepository.existsByPhoneNumberAndIdNot(store.getPhoneNumber(), store.getId())) {
            throw new BusinessException("电话号码已存在: " + store.getPhoneNumber());
        }

        boolean result = storeRepository.updateById(store);
        if (result) {
            log.info("门店更新成功: {}", store.getId());
            return true;
        }
        throw new BusinessException("门店更新失败");
    }

    /**
     * 根据ID删除门店（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStore(Long id) {
        Optional<Store> storeOpt = storeRepository.findById(id);
        if (!storeOpt.isPresent()) {
            log.warn("门店不存在: {}", id);
            throw new ResourceNotFoundException("门店不存在");
        }

        boolean result = storeRepository.deleteById(id);
        if (result) {
            log.info("门店删除成功: {}", id);
            return true;
        }
        throw new BusinessException("门店删除失败");
    }

    /**
     * 根据ID查询门店
     */
    public Store getStoreById(Long id) {
        Optional<Store> storeOpt = storeRepository.findById(id);
        if (!storeOpt.isPresent()) {
            throw new ResourceNotFoundException("门店不存在: " + id);
        }
        return storeOpt.get();
    }

    /**
     * 查询激活的门店列表
     */
    public List<Store> getActiveStores() {
        return storeRepository.findActiveStores();
    }

    /**
     * 分页查询门店
     */
    public IPage<Store> getStoresPage(int current, int size, String name, Boolean isActive) {
        return storeRepository.findPage(current, size, name, isActive);
    }

    /**
     * 根据名称模糊查询门店
     */
    public List<Store> searchStoresByName(String name) {
        return storeRepository.findByNameLike(name);
    }

    /**
     * 根据电话号码查询门店
     */
    public Store getStoreByPhoneNumber(String phoneNumber) {
        Optional<Store> storeOpt = storeRepository.findByPhoneNumber(phoneNumber);
        return storeOpt.orElse(null);
    }

    /**
     * 激活门店
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean activateStore(Long id) {
        boolean result = storeRepository.activateStore(id);
        if (result) {
            log.info("门店激活成功: {}", id);
        }
        return result;
    }

    /**
     * 停用门店
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deactivateStore(Long id) {
        boolean result = storeRepository.deactivateStore(id);
        if (result) {
            log.info("门店停用成功: {}", id);
        }
        return result;
    }

    /**
     * 批量更新门店状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStoreStatusBatch(List<Long> ids, Boolean isActive) {
        boolean result = storeRepository.updateStatusBatch(ids, isActive);
        if (result) {
            log.info("批量更新门店状态成功: {} 个门店 -> {}", ids.size(), isActive);
        }
        return result;
    }

    /**
     * 统计激活的门店总数
     */
    public Long countActiveStores() {
        return storeRepository.countActiveStores();
    }

    /**
     * 统计所有门店总数
     */
    public Long countAllStores() {
        return storeRepository.countAllStores();
    }
}
