package io.by.store.application;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.infrastructure.entity.StoreTable;
import io.by.store.infrastructure.repository.StoreTableRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 门店桌台服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StoreTableService {

    private final StoreTableRepository storeTableRepository;

    /**
     * 创建桌台
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createStoreTable(StoreTable storeTable) {
        // 检查同一门店下桌台号是否重复
        if (storeTableRepository.existsByStoreIdAndTableNumber(
                storeTable.getStoreId(), storeTable.getTableNumber())) {
            throw new BusinessException("桌台号已存在: " + storeTable.getTableNumber());
        }

        // 检查二维码URL是否重复（如果有）
        if (StringUtils.hasText(storeTable.getQrcodeUrl()) &&
            storeTableRepository.existsByQrcodeUrl(storeTable.getQrcodeUrl())) {
            throw new BusinessException("二维码URL已存在: " + storeTable.getQrcodeUrl());
        }

        // 设置默认值
        if (storeTable.getIsActive() == null) {
            storeTable.setIsActive(true);
        }

        boolean result = storeTableRepository.save(storeTable);
        if (result) {
            log.info("桌台创建成功: 门店ID={}, 桌台号={}",
                storeTable.getStoreId(), storeTable.getTableNumber());
            return true;
        }
        throw new BusinessException("桌台创建失败");
    }

    /**
     * 更新桌台信息
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStoreTable(StoreTable storeTable) {
        // 验证桌台是否存在
        Optional<StoreTable> existingTable = storeTableRepository.findById(storeTable.getId());
        if (!existingTable.isPresent()) {
            throw new ResourceNotFoundException("桌台不存在: " + storeTable.getId());
        }

        // 检查同一门店下桌台号是否重复（排除自己）
        if (storeTableRepository.existsByStoreIdAndTableNumberAndIdNot(
                storeTable.getStoreId(), storeTable.getTableNumber(), storeTable.getId())) {
            throw new BusinessException("桌台号已存在: " + storeTable.getTableNumber());
        }

        // 检查二维码URL是否重复（排除自己）
        if (StringUtils.hasText(storeTable.getQrcodeUrl()) &&
            storeTableRepository.existsByQrcodeUrlAndIdNot(storeTable.getQrcodeUrl(), storeTable.getId())) {
            throw new BusinessException("二维码URL已存在: " + storeTable.getQrcodeUrl());
        }

        boolean result = storeTableRepository.updateById(storeTable);
        if (result) {
            log.info("桌台更新成功: {}", storeTable.getId());
            return true;
        }
        throw new BusinessException("桌台更新失败");
    }

    /**
     * 根据ID删除桌台（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStoreTable(Long id) {
        Optional<StoreTable> storeTableOpt = storeTableRepository.findById(id);
        if (!storeTableOpt.isPresent()) {
            log.warn("桌台不存在: {}", id);
            throw new ResourceNotFoundException("桌台不存在");
        }

        boolean result = storeTableRepository.deleteById(id);
        if (result) {
            log.info("桌台删除成功: {}", id);
            return true;
        }
        throw new BusinessException("桌台删除失败");
    }

    /**
     * 根据ID查询桌台
     */
    public StoreTable getStoreTableById(Long id) {
        Optional<StoreTable> storeTableOpt = storeTableRepository.findById(id);
        if (!storeTableOpt.isPresent()) {
            throw new ResourceNotFoundException("桌台不存在: " + id);
        }
        return storeTableOpt.get();
    }

    /**
     * 根据门店ID查询激活的桌台列表
     */
    public List<StoreTable> getActiveTablesByStoreId(Long storeId) {
        return storeTableRepository.findActiveTablesByStoreId(storeId);
    }

    /**
     * 根据门店ID查询所有桌台列表
     */
    public List<StoreTable> getAllTablesByStoreId(Long storeId) {
        return storeTableRepository.findAllTablesByStoreId(storeId);
    }

    /**
     * 分页查询桌台
     */
    public IPage<StoreTable> getStoreTablesPage(int current, int size, Long storeId, String tableNumber, Boolean isActive) {
        return storeTableRepository.findPage(current, size, storeId, tableNumber, isActive);
    }

    /**
     * 根据门店ID和桌台号查询桌台
     */
    public StoreTable getStoreTableByStoreIdAndTableNumber(Long storeId, String tableNumber) {
        Optional<StoreTable> storeTableOpt = storeTableRepository.findByStoreIdAndTableNumber(storeId, tableNumber);
        return storeTableOpt.orElse(null);
    }

    /**
     * 查询门店的激活桌台总数
     */
    public Integer countActiveTablesByStoreId(Long storeId) {
        return storeTableRepository.countActiveTablesByStoreId(storeId);
    }

    /**
     * 查询门店的所有桌台总数
     */
    public Integer countAllTablesByStoreId(Long storeId) {
        return storeTableRepository.countAllTablesByStoreId(storeId);
    }

    /**
     * 根据二维码URL查询桌台
     */
    public StoreTable getStoreTableByQrcodeUrl(String qrcodeUrl) {
        Optional<StoreTable> storeTableOpt = storeTableRepository.findByQrcodeUrl(qrcodeUrl);
        return storeTableOpt.orElse(null);
    }

    /**
     * 根据门店ID查询桌台列表（包含激活和非激活）
     */
    public List<StoreTable> getTablesByStoreId(Long storeId) {
        return storeTableRepository.findAllTablesByStoreId(storeId);
    }

    /**
     * 根据二维码查询桌台
     */
    public StoreTable getTableByQrCode(String qrCode) {
        return getStoreTableByQrcodeUrl(qrCode);
    }

    /**
     * 激活桌台
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean activateTable(Long id) {
        boolean result = storeTableRepository.activateTable(id);
        if (result) {
            log.info("桌台激活成功: {}", id);
        }
        return result;
    }

    /**
     * 停用桌台
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deactivateTable(Long id) {
        boolean result = storeTableRepository.deactivateTable(id);
        if (result) {
            log.info("桌台停用成功: {}", id);
        }
        return result;
    }

    /**
     * 批量更新桌台状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTableStatusBatch(List<Long> ids, Boolean isActive) {
        boolean result = storeTableRepository.updateStatusBatch(ids, isActive);
        if (result) {
            log.info("批量更新桌台状态成功: {} 个桌台 -> {}", ids.size(), isActive);
        }
        return result;
    }

    /**
     * 根据门店ID批量更新桌台状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTableStatusByStoreId(Long storeId, Boolean isActive) {
        boolean result = storeTableRepository.updateStatusByStoreId(storeId, isActive);
        if (result) {
            log.info("根据门店ID批量更新桌台状态成功: 门店ID={}, 状态={}", storeId, isActive);
        }
        return result;
    }

    /**
     * 批量创建桌台
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchCreateStoreTables(List<StoreTable> storeTables) {
        // 验证桌台号唯一性
        for (StoreTable storeTable : storeTables) {
            if (storeTableRepository.existsByStoreIdAndTableNumber(
                    storeTable.getStoreId(), storeTable.getTableNumber())) {
                throw new BusinessException("桌台号已存在: " + storeTable.getTableNumber());
            }

            // 验证二维码URL唯一性（如果有）
            if (StringUtils.hasText(storeTable.getQrcodeUrl()) &&
                storeTableRepository.existsByQrcodeUrl(storeTable.getQrcodeUrl())) {
                throw new BusinessException("二维码URL已存在: " + storeTable.getQrcodeUrl());
            }

            if (storeTable.getIsActive() == null) {
                storeTable.setIsActive(true);
            }
        }

        boolean result = storeTableRepository.saveBatch(storeTables);
        if (result) {
            log.info("批量创建桌台成功，共创建 {} 个桌台", storeTables.size());
        }
        return result;
    }

    /**
     * 根据门店ID删除所有桌台（逻辑删除）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTablesByStoreId(Long storeId) {
        boolean result = storeTableRepository.deleteByStoreId(storeId);
        if (result) {
            log.info("根据门店ID删除所有桌台成功: 门店ID={}", storeId);
        }
        return result;
    }

    /**
     * 统计所有激活的桌台总数
     */
    public Long countAllActiveTables() {
        return storeTableRepository.countAllActiveTables();
    }

    /**
     * 统计所有桌台总数
     */
    public Long countAllTables() {
        return storeTableRepository.countAllTables();
    }
}
