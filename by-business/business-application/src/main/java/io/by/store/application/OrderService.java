package io.by.store.application;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.application.common.PaymentMethod;
import io.by.store.infrastructure.entity.Order;
import io.by.store.infrastructure.entity.OrderItem;
import io.by.store.infrastructure.entity.OrderDiscount;
import io.by.store.infrastructure.entity.Product;
import io.by.store.infrastructure.entity.Member;
import io.by.store.infrastructure.entity.StoreTable;
import io.by.store.infrastructure.repository.OrderRepository;
import io.by.store.infrastructure.repository.OrderItemRepository;
import io.by.store.infrastructure.repository.OrderDiscountRepository;
import io.by.store.infrastructure.repository.ProductRepository;
import io.by.store.infrastructure.repository.MemberRepository;
import io.by.store.infrastructure.repository.StoreTableRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 订单服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderService {

    private final OrderRepository orderRepository;
    private final OrderItemRepository orderItemRepository;
    private final OrderDiscountRepository orderDiscountRepository;
    private final ProductRepository productRepository;
    private final MemberRepository memberRepository;
    private final StoreTableRepository storeTableRepository;
    private final MemberService memberService;
    private final TableUsageService tableUsageService;

    /**
     * 创建订单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createOrder(Order order, List<OrderItem> orderItems, List<OrderDiscount> orderDiscounts) {
        // 1. 验证订单基本信息
        validateOrderBasicInfo(order);

        // 2. 验证订单商品项
        validateOrderItems(orderItems);

        // 3. 验证会员信息（如果有）并填充手机号
        if (order.getMemberId() != null) {
            validateMember(order.getMemberId());
            // 通过memberId查询会员信息，填充phoneNumber
            Member member = memberRepository.findByMemberId(order.getMemberId()).orElse(null);
            if (member != null && member.getPhoneNumber() != null) {
                order.setPhoneNumber(member.getPhoneNumber());
            }
        }

        // 4. 验证桌台信息（如果有）并填充桌台号
        if (order.getTableId() != null) {
            StoreTable storeTable = storeTableRepository.findById(order.getTableId()).orElse(null);
            if (storeTable != null && storeTable.getTableNumber() != null) {
                order.setTableNumber(storeTable.getTableNumber());
            }
        }

        // 5. 生成订单号
        order.setOrderNo(generateOrderNo());

        // 6. 计算订单金额
        calculateOrderAmounts(order, orderItems, orderDiscounts);

        // 7. 验证支付方式并处理余额支付
        validateAndProcessPayment(order);

        // 8. 设置订单初始状态
        order.setStatus(Order.Status.PENDING_PAYMENT.getCode());
        if(StrUtil.isNotBlank(order.getPaymentMethod())){
            order.setPaymentStatus(Order.PaymentStatus.PAID.getCode());
            order.setStatus(Order.Status.PROCESSING.getCode());
        }else {
            order.setStatus(Order.Status.PENDING_PAYMENT.getCode());
            order.setPaymentStatus(Order.PaymentStatus.UNPAID.getCode());
        }

        // 9. 保存订单
        boolean orderSaved = orderRepository.save(order);
        if (!orderSaved) {
            throw new BusinessException("订单保存失败");
        }

        // 10. 设置订单商品项的订单ID并保存
        orderItems.forEach(item -> {
            item.setOrderId(order.getId());
            // 计算商品项总价
            BigDecimal totalPrice = item.getUnitPrice().multiply(new BigDecimal(item.getQuantity()));
            item.setTotalPrice(totalPrice);
        });

        boolean itemsSaved = orderItemRepository.saveBatch(orderItems);
        if (!itemsSaved) {
            throw new BusinessException("订单商品项保存失败");
        }

        // 11. 保存订单优惠明细（如果有）
        if (orderDiscounts != null && !orderDiscounts.isEmpty()) {
            orderDiscounts.forEach(discount -> discount.setOrderId(order.getId()));
            boolean discountsSaved = orderDiscountRepository.saveBatch(orderDiscounts);
            if (!discountsSaved) {
                throw new BusinessException("订单优惠明细保存失败");
            }
        }

        // 12. 如果是堂食订单且有桌台ID，自动开桌
        if (Order.OrderType.DINE_IN.getCode().equals(order.getOrderType()) && order.getTableId() != null) {
            try {
                tableUsageService.openTable(order.getTableId(), order.getOrderNo());
                log.info("自动开桌成功: 桌台ID={}, 订单号={}", order.getTableId(), order.getOrderNo());
            } catch (Exception e) {
                log.warn("自动开桌失败: 桌台ID={}, 订单号={}, 错误: {}",
                        order.getTableId(), order.getOrderNo(), e.getMessage());
                // 开桌失败不影响订单创建，只记录警告日志
            }
        }

        log.info("订单创建成功: {}", order.getOrderNo());
        return true;
    }

    /**
     * 更新订单状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatus(Long orderId, String status) {
        // 1. 查询订单
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (!orderOpt.isPresent()) {
            throw new ResourceNotFoundException("订单不存在");
        }

        Order order = orderOpt.get();

        // 2. 验证状态转换
        validateStatusTransition(order.getStatus(), status);

        // 3. 更新订单状态
        boolean updated = orderRepository.updateOrderStatus(orderId, status);
        if (updated) {
            log.info("订单状态更新成功: {} -> {}", order.getOrderNo(), status);

            // 如果订单完成且是堂食订单，自动关桌
            if (Order.Status.COMPLETED.getCode().equals(status) &&
                Order.OrderType.DINE_IN.getCode().equals(order.getOrderType()) &&
                order.getTableId() != null) {
                try {
                    tableUsageService.closeTableByOrderId(order.getOrderNo());
                    log.info("自动关桌成功: 桌台ID={}, 订单号={}", order.getTableId(), order.getOrderNo());
                } catch (Exception e) {
                    log.warn("自动关桌失败: 桌台ID={}, 订单号={}, 错误: {}",
                            order.getTableId(), order.getOrderNo(), e.getMessage());
                    // 关桌失败不影响订单状态更新，只记录警告日志
                }
            }
        }
        return updated;
    }

    /**
     * 更新订单支付状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePaymentStatus(Long orderId, String paymentStatus) {
        // 1. 查询订单
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (!orderOpt.isPresent()) {
            throw new ResourceNotFoundException("订单不存在");
        }

        Order order = orderOpt.get();

        // 2. 更新支付状态
        boolean updated = orderRepository.updatePaymentStatus(orderId, paymentStatus);
        if (updated) {
            log.info("订单支付状态更新成功: {} -> {}", order.getOrderNo(), paymentStatus);

            // 如果支付成功，自动将订单状态改为制作中
            if (Order.PaymentStatus.PAID.getCode().equals(paymentStatus)) {
                updateOrderStatus(orderId, Order.Status.PROCESSING.getCode());
            }
        }
        return updated;
    }

    /**
     * 批量更新订单状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateOrderStatus(List<Long> orderIds, String status) {
        // 验证订单存在性
        for (Long orderId : orderIds) {
            Optional<Order> orderOpt = orderRepository.findById(orderId);
            if (!orderOpt.isPresent()) {
                throw new ResourceNotFoundException("订单不存在: " + orderId);
            }
        }

        // 批量更新订单状态
        boolean updated = orderRepository.batchUpdateOrderStatus(orderIds, status);
        if (updated) {
            log.info("批量更新订单状态成功: {} 个订单 -> {}", orderIds.size(), status);
        }
        return updated;
    }

    /**
     * 根据ID查询订单
     */
    public Order getOrderById(Long id) {
        Optional<Order> orderOpt = orderRepository.findById(id);
        if (!orderOpt.isPresent()) {
            throw new ResourceNotFoundException("订单不存在");
        }
        return orderOpt.get();
    }

    /**
     * 根据订单号查询订单
     */
    public Order getOrderByOrderNo(String orderNo) {
        Optional<Order> orderOpt = orderRepository.findByOrderNo(orderNo);
        if (!orderOpt.isPresent()) {
            throw new ResourceNotFoundException("订单不存在");
        }
        return orderOpt.get();
    }

    /**
     * 查询订单商品项
     */
    public List<OrderItem> getOrderItems(Long orderId) {
        return orderItemRepository.findByOrderId(orderId);
    }

    /**
     * 查询订单优惠明细
     */
    public List<OrderDiscount> getOrderDiscounts(Long orderId) {
        return orderDiscountRepository.findByOrderId(orderId);
    }

    /**
     * 分页查询订单
     */
    public IPage<Order> getOrdersPage(
            Integer current,
            Integer size,
            String orderNo,
            Long memberId,
            Long tableId,
            String orderType,
            String status,
            String paymentStatus,
            String paymentMethod,
            LocalDateTime startTime,
            LocalDateTime endTime) {
        return orderRepository.findOrdersPage(
                current, size, orderNo, memberId, tableId, orderType,
                status, paymentStatus, paymentMethod, startTime, endTime, null
        );
    }

    /**
     * 根据会员ID查询订单
     */
    public List<Order> getOrdersByMemberId(Long memberId) {
        return orderRepository.findByMemberId(memberId);
    }

    /**
     * 根据桌台ID查询订单
     */
    public List<Order> getOrdersByTableId(Long tableId) {
        return orderRepository.findByTableId(tableId);
    }

    /**
     * 根据状态查询订单
     */
    public List<Order> getOrdersByStatus(String status) {
        return orderRepository.findByStatus(status);
    }

    /**
     * 删除订单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteOrder(Long orderId) {
        // 1. 检查订单是否存在
        Optional<Order> orderOpt = orderRepository.findById(orderId);
        if (!orderOpt.isPresent()) {
            throw new ResourceNotFoundException("订单不存在");
        }

        Order order = orderOpt.get();

        // 2. 检查订单状态是否允许删除
        if (!Order.Status.PENDING_PAYMENT.getCode().equals(order.getStatus()) &&
                !Order.Status.CANCELLED.getCode().equals(order.getStatus())) {
            throw new BusinessException("只能删除待支付或已取消的订单");
        }

        // 3. 删除订单商品项
        orderItemRepository.deleteByOrderId(orderId);

        // 4. 删除订单优惠明细
        orderDiscountRepository.deleteByOrderId(orderId);

        // 5. 删除订单
        boolean deleted = orderRepository.deleteById(orderId);
        if (deleted) {
            log.info("订单删除成功: {}", order.getOrderNo());
        }
        return deleted;
    }

    // ==================== 私有方法 ====================

    /**
     * 验证订单基本信息
     */
    private void validateOrderBasicInfo(Order order) {
        if (order.getOrderType() == null) {
            order.setOrderType(Order.OrderType.DINE_IN.getCode());
        }
    }

    /**
     * 验证订单商品项
     */
    private void validateOrderItems(List<OrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            throw new BusinessException("订单商品项不能为空");
        }

        for (OrderItem item : orderItems) {
            // 验证商品是否存在
            Optional<Product> productOpt = productRepository.findById(item.getProductId());
            if (!productOpt.isPresent()) {
                throw new BusinessException("商品不存在: " + item.getProductId());
            }

            Product product = productOpt.get();

            // 验证商品状态
            if (!Product.Status.PUBLISHED.getCode().equals(product.getStatus())) {
                throw new BusinessException("商品未上架: " + product.getName());
            }

            // 验证库存
            if (product.getStockQuantity() < item.getQuantity()) {
                throw new BusinessException("商品库存不足: " + product.getName());
            }

            // 设置商品信息到订单项（冗余存储）
            item.setProductName(product.getName());
            item.setUnitPrice(product.getPrice());
        }
    }

    /**
     * 验证会员信息
     */
    private void validateMember(Long memberId) {
        Optional<Member> memberOpt = memberRepository.findByMemberId(memberId);
        if (!memberOpt.isPresent()) {
            throw new BusinessException("会员不存在: " + memberId);
        }
    }

    /**
     * 验证支付方式并处理余额支付
     */
    private void validateAndProcessPayment(Order order) {
        // 验证支付方式
        if (order.getPaymentMethod() == null || order.getPaymentMethod().trim().isEmpty()) {
            return;
        }

        PaymentMethod paymentMethod;
        try {
            paymentMethod = PaymentMethod.fromCode(order.getPaymentMethod());
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的支付方式: " + order.getPaymentMethod());
        }

        // 如果是余额支付，需要验证会员信息并扣除余额
        if (paymentMethod.isBalancePayment()) {
            // 余额支付必须有会员ID
            if (order.getMemberId() == null) {
                throw new BusinessException("余额支付必须关联会员");
            }

            // 查询会员信息
            Optional<Member> memberOpt = memberRepository.findByMemberId(order.getMemberId());
            if (!memberOpt.isPresent()) {
                throw new BusinessException("会员不存在: " + order.getMemberId());
            }

            Member member = memberOpt.get();

            // 检查余额是否足够
            if (member.getBalance().compareTo(order.getPayableAmount()) < 0) {
                throw new BusinessException("余额不足，当前余额: " + member.getBalance() +
                        "，需要支付: " + order.getPayableAmount());
            }

            // 扣除余额
            BigDecimal deductAmount = order.getPayableAmount().negate(); // 负数表示扣减
            boolean success = memberService.updateMemberBalance(order.getMemberId(), deductAmount);
            if (!success) {
                throw new BusinessException("余额扣除失败");
            }

            // 余额支付成功，直接设置为已支付状态
            order.setPaymentStatus(Order.PaymentStatus.PAID.getCode());
            order.setStatus(Order.Status.PROCESSING.getCode());

            log.info("余额支付成功: 会员ID={}, 扣除金额={}", order.getMemberId(), order.getPayableAmount());
        }
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        // 格式: yyyyMMddHHmmss + 6位随机数
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        int random = (int) (Math.random() * 900000) + 100000;
        return timestamp + random;
    }

    /**
     * 计算订单金额
     */
    private void calculateOrderAmounts(Order order, List<OrderItem> orderItems, List<OrderDiscount> orderDiscounts) {
        // 计算原始金额
        BigDecimal originalAmount = orderItems.stream()
                .map(item -> item.getUnitPrice().multiply(new BigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算优惠金额
        BigDecimal discountAmount = BigDecimal.ZERO;
        if (orderDiscounts != null && !orderDiscounts.isEmpty()) {
            discountAmount = orderDiscounts.stream()
                    .map(OrderDiscount::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 计算应付金额
        BigDecimal payableAmount = originalAmount.subtract(discountAmount);
        if (payableAmount.compareTo(BigDecimal.ZERO) < 0) {
            payableAmount = BigDecimal.ZERO;
        }

        order.setOriginalAmount(originalAmount);
        order.setDiscountAmount(discountAmount);
        order.setPayableAmount(payableAmount);
    }

    /**
     * 验证状态转换
     */
    private void validateStatusTransition(String currentStatus, String newStatus) {
        // 简单的状态转换验证
        if (Order.Status.CANCELLED.getCode().equals(currentStatus)) {
            throw new BusinessException("已取消的订单不能修改状态");
        }

//        if (Order.Status.COMPLETED.getCode().equals(currentStatus)) {
//            throw new BusinessException("已完成的订单不能修改状态");
//        }
    }
}
