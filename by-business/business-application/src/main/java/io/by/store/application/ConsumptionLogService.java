package io.by.store.application;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.infrastructure.entity.ConsumptionLog;
import io.by.store.infrastructure.entity.Member;
import io.by.store.infrastructure.mapper.ConsumptionLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 余额消费记录服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConsumptionLogService {

    private final ConsumptionLogMapper consumptionLogMapper;
    private final MemberService memberService;

    /**
     * 创建消费记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createConsumptionLog(ConsumptionLog consumptionLog) {
        // 验证会员是否存在
        Member member = memberService.getMemberByMemberId(consumptionLog.getMemberId());
        if (member == null) {
            throw new ResourceNotFoundException("会员不存在: " + consumptionLog.getMemberId());
        }

        // 验证消费金额
        if (consumptionLog.getAmountConsumed() == null || 
            consumptionLog.getAmountConsumed().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("消费金额必须大于0");
        }

        // 验证余额是否足够
        if (member.getBalance().compareTo(consumptionLog.getAmountConsumed()) < 0) {
            throw new BusinessException("余额不足，当前余额: " + member.getBalance());
        }

        // 设置消费前余额
        consumptionLog.setBalanceBefore(member.getBalance());
        
        // 计算消费后余额
        BigDecimal balanceAfter = member.getBalance().subtract(consumptionLog.getAmountConsumed());
        consumptionLog.setBalanceAfter(balanceAfter);

        // 设置创建时间
        consumptionLog.setCreatedAt(LocalDateTime.now());

        // 插入消费记录
        int result = consumptionLogMapper.insert(consumptionLog);
        if (result > 0) {
            // 更新会员余额
            boolean balanceUpdated = memberService.updateMemberBalance(
                    consumptionLog.getMemberId(), 
                    consumptionLog.getAmountConsumed().negate()
            );
            
            if (balanceUpdated) {
                log.info("消费记录创建成功: memberId={}, orderId={}, amount={}", 
                        consumptionLog.getMemberId(), consumptionLog.getOrderId(), 
                        consumptionLog.getAmountConsumed());
                return true;
            } else {
                throw new BusinessException("余额更新失败");
            }
        }
        return false;
    }

    /**
     * 根据ID查询消费记录
     */
    public ConsumptionLog getConsumptionLogById(Long id) {
        return consumptionLogMapper.selectById(id);
    }

    /**
     * 根据会员ID查询消费记录
     */
    public List<ConsumptionLog> getConsumptionLogsByMemberId(Long memberId) {
        return consumptionLogMapper.findByMemberId(memberId);
    }

    /**
     * 根据订单ID查询消费记录
     */
    public ConsumptionLog getConsumptionLogByOrderId(Long orderId) {
        return consumptionLogMapper.findByOrderId(orderId);
    }

    /**
     * 分页查询消费记录
     */
    public IPage<ConsumptionLog> getConsumptionLogsPage(Integer current, Integer size, 
                                                       Long memberId, Long orderId, 
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        Page<ConsumptionLog> page = new Page<>(current, size);
        
        LambdaQueryWrapper<ConsumptionLog> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据会员ID查询
        if (memberId != null) {
            queryWrapper.eq(ConsumptionLog::getMemberId, memberId);
        }
        
        // 根据订单ID查询
        if (orderId != null) {
            queryWrapper.eq(ConsumptionLog::getOrderId, orderId);
        }
        
        // 根据时间范围查询
        if (startTime != null && endTime != null) {
            queryWrapper.between(ConsumptionLog::getCreatedAt, startTime, endTime);
        } else if (startTime != null) {
            queryWrapper.ge(ConsumptionLog::getCreatedAt, startTime);
        } else if (endTime != null) {
            queryWrapper.le(ConsumptionLog::getCreatedAt, endTime);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(ConsumptionLog::getCreatedAt);
        
        return consumptionLogMapper.selectPage(page, queryWrapper);
    }

    /**
     * 获取消费统计信息
     */
    public ConsumptionStatistics getConsumptionStatistics() {
        BigDecimal todayAmount = consumptionLogMapper.sumTodayConsumedAmount();
        BigDecimal monthAmount = consumptionLogMapper.sumMonthConsumedAmount();
        Integer totalCount = consumptionLogMapper.countConsumptionLogs();
        List<ConsumptionLog> recentLogs = consumptionLogMapper.findRecentLogs(10);
        
        ConsumptionStatistics statistics = new ConsumptionStatistics();
        statistics.setTodayAmount(todayAmount != null ? todayAmount : BigDecimal.ZERO);
        statistics.setMonthAmount(monthAmount != null ? monthAmount : BigDecimal.ZERO);
        statistics.setTotalCount(totalCount);
        statistics.setRecentLogsCount(recentLogs.size());
        
        return statistics;
    }

    /**
     * 获取会员消费统计
     */
    public MemberConsumptionStatistics getMemberConsumptionStatistics(Long memberId) {
        BigDecimal totalAmount = consumptionLogMapper.sumConsumedAmountByMemberId(memberId);
        Integer totalCount = consumptionLogMapper.countByMemberId(memberId);
        BigDecimal maxAmount = consumptionLogMapper.findMaxConsumedAmountByMemberId(memberId);
        BigDecimal avgAmount = consumptionLogMapper.findAvgConsumedAmountByMemberId(memberId);
        
        MemberConsumptionStatistics statistics = new MemberConsumptionStatistics();
        statistics.setMemberId(memberId);
        statistics.setTotalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO);
        statistics.setTotalCount(totalCount);
        statistics.setMaxAmount(maxAmount != null ? maxAmount : BigDecimal.ZERO);
        statistics.setAvgAmount(avgAmount != null ? avgAmount : BigDecimal.ZERO);
        
        return statistics;
    }

    /**
     * 消费统计信息
     */
    @lombok.Data
    public static class ConsumptionStatistics {
        private BigDecimal todayAmount;
        private BigDecimal monthAmount;
        private Integer totalCount;
        private Integer recentLogsCount;
    }

    /**
     * 会员消费统计信息
     */
    @lombok.Data
    public static class MemberConsumptionStatistics {
        private Long memberId;
        private BigDecimal totalAmount;
        private Integer totalCount;
        private BigDecimal maxAmount;
        private BigDecimal avgAmount;
    }
}
