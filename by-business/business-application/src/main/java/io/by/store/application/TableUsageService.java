package io.by.store.application;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.infrastructure.entity.TableUsage;
import io.by.store.infrastructure.entity.StoreTable;
import io.by.store.infrastructure.repository.TableUsageRepository;
import io.by.store.infrastructure.repository.StoreTableRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 桌台使用记录服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TableUsageService {

    private final TableUsageRepository tableUsageRepository;
    private final StoreTableRepository storeTableRepository;

    /**
     * 开桌
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean openTable(Long tableId, String orderId) {
        // 验证桌台是否存在且激活
        Optional<StoreTable> tableOpt = storeTableRepository.findById(tableId);
        if (!tableOpt.isPresent()) {
            throw new ResourceNotFoundException("桌台不存在: " + tableId);
        }

        StoreTable table = tableOpt.get();
        if (!table.getIsActive()) {
            throw new BusinessException("桌台未激活，无法开桌: " + table.getTableNumber());
        }

        // 检查桌台是否已经开桌
        Optional<TableUsage> activeUsage = tableUsageRepository.findActiveUsageByTableId(tableId);
        if (activeUsage.isPresent()) {
            throw new BusinessException("桌台已经开桌，无法重复开桌: " + table.getTableNumber());
        }

        boolean result = tableUsageRepository.openTable(tableId, orderId, table.getStoreId());
        if (result) {
            log.info("开桌成功: 桌台ID={}, 桌台号={}, 订单ID={}", tableId, table.getTableNumber(), orderId);
        }
        return true;
    }

    /**
     * 关桌
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean closeTable(Long tableId) {
        // 验证桌台是否存在
        Optional<StoreTable> tableOpt = storeTableRepository.findById(tableId);
        if (!tableOpt.isPresent()) {
            throw new ResourceNotFoundException("桌台不存在: " + tableId);
        }

        StoreTable table = tableOpt.get();
        boolean result = tableUsageRepository.closeTable(tableId);
        if (result) {
            log.info("关桌成功: 桌台ID={}, 桌台号={}", tableId, table.getTableNumber());
        }
        return result;
    }

    /**
     * 根据订单ID关桌
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean closeTableByOrderId(String orderId) {
        boolean result = tableUsageRepository.closeTableByOrderId(orderId);
        if (result) {
            log.info("根据订单ID关桌成功: 订单ID={}", orderId);
        }
        return result;
    }

    /**
     * 查询桌台当前开桌状态
     */
    public boolean isTableOpen(Long tableId) {
        Optional<TableUsage> activeUsage = tableUsageRepository.findActiveUsageByTableId(tableId);
        return activeUsage.isPresent();
    }

    /**
     * 查询桌台当前开桌信息
     */
    public TableUsage getCurrentTableUsage(Long tableId) {
        Optional<TableUsage> activeUsage = tableUsageRepository.findActiveUsageByTableId(tableId);
        return activeUsage.orElse(null);
    }

    /**
     * 计算桌台当前开桌时长（分钟）
     */
    public Integer getCurrentUsageDuration(Long tableId) {
        Optional<TableUsage> activeUsage = tableUsageRepository.findActiveUsageByTableId(tableId);
        if (!activeUsage.isPresent()) {
            return 0;
        }

        TableUsage usage = activeUsage.get();
        Duration duration = Duration.between(usage.getOpenedAt(), LocalDateTime.now());
        return (int) duration.toMinutes();
    }

    /**
     * 查询桌台历史使用记录
     */
    public List<TableUsage> getTableUsageHistory(Long tableId) {
        return tableUsageRepository.findAllUsagesByTableId(tableId);
    }

    /**
     * 分页查询桌台使用记录
     */
    public IPage<TableUsage> getTableUsagesPage(
            Integer current,
            Integer size,
            Long storeId,
            Long tableId,
            String status,
            LocalDateTime startTime,
            LocalDateTime endTime) {
        return tableUsageRepository.findPage(current, size, storeId, tableId, status, startTime, endTime);
    }

    /**
     * 根据ID查询桌台使用记录
     */
    public TableUsage getTableUsageById(Long id) {
        Optional<TableUsage> usageOpt = tableUsageRepository.findById(id);
        if (!usageOpt.isPresent()) {
            throw new ResourceNotFoundException("桌台使用记录不存在: " + id);
        }
        return usageOpt.get();
    }

    /**
     * 根据订单ID查询桌台使用记录
     */
    public TableUsage getTableUsageByOrderId(String orderId) {
        Optional<TableUsage> usageOpt = tableUsageRepository.findByOrderId(orderId);
        return usageOpt.orElse(null);
    }

    /**
     * 查询门店当前开桌数量
     */
    public Integer getActiveTableCount(Long storeId) {
        return tableUsageRepository.countActiveTablesByStoreId(storeId);
    }

    /**
     * 查询门店当前开桌列表
     */
    public List<TableUsage> getActiveTableUsages(Long storeId) {
        return tableUsageRepository.findActiveUsagesByStoreId(storeId);
    }

    /**
     * 统计桌台历史使用次数
     */
    public Integer getTableUsageCount(Long tableId) {
        return tableUsageRepository.countUsagesByTableId(tableId);
    }

    /**
     * 统计指定时间范围内的桌台使用次数
     */
    public Integer getTableUsageCountByDateRange(Long tableId, LocalDateTime startTime, LocalDateTime endTime) {
        return tableUsageRepository.countUsagesByDateRange(tableId, startTime, endTime);
    }

    /**
     * 查询桌台平均使用时长（分钟）
     */
    public Double getAverageUsageDuration(Long tableId) {
        Double duration = tableUsageRepository.getAverageUsageDurationByTableId(tableId);
        return duration != null ? duration : 0.0;
    }

    /**
     * 查询门店桌台平均使用时长（分钟）
     */
    public Double getStoreAverageUsageDuration(Long storeId) {
        Double duration = tableUsageRepository.getAverageUsageDurationByStoreId(storeId);
        return duration != null ? duration : 0.0;
    }

    /**
     * 删除桌台使用记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTableUsage(Long id) {
        // 检查记录是否存在
        Optional<TableUsage> usageOpt = tableUsageRepository.findById(id);
        if (!usageOpt.isPresent()) {
            throw new ResourceNotFoundException("桌台使用记录不存在: " + id);
        }

        TableUsage usage = usageOpt.get();
        
        // 检查是否是活跃状态，活跃状态不允许删除
        if (TableUsage.Status.ACTIVE.getCode().equals(usage.getStatus())) {
            throw new BusinessException("活跃状态的桌台使用记录不能删除");
        }

        boolean result = tableUsageRepository.deleteById(id);
        if (result) {
            log.info("桌台使用记录删除成功: {}", id);
        }
        return result;
    }

    /**
     * 更新桌台使用记录状态
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTableUsageStatus(Long id, String status) {
        // 检查记录是否存在
        Optional<TableUsage> usageOpt = tableUsageRepository.findById(id);
        if (!usageOpt.isPresent()) {
            throw new ResourceNotFoundException("桌台使用记录不存在: " + id);
        }

        boolean result = tableUsageRepository.updateStatus(id, status);
        if (result) {
            log.info("桌台使用记录状态更新成功: {} -> {}", id, status);
        }
        return result;
    }
}
