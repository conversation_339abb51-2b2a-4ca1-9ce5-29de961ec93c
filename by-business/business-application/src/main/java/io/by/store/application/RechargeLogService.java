package io.by.store.application;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.application.common.*;
import io.by.store.infrastructure.entity.Member;
import io.by.store.infrastructure.entity.RechargeLog;
import io.by.store.infrastructure.mapper.RechargeLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 充值记录服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RechargeLogService {

    private final RechargeLogMapper rechargeLogMapper;
    private final MemberService memberService;
    private final PointsLogService pointsLogService;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    /**
     * 创建充值记录
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createRechargeLog(RechargeLog rechargeLog) {
        // 生成充值订单号
        String orderNumber = generateRechargeOrderNumber();
        rechargeLog.setRechargeOrderNumber(orderNumber);

        // 验证会员是否存在
        Member member = memberService.getMemberByMemberId(rechargeLog.getMemberId());
        if (member == null) {
            throw new ResourceNotFoundException("会员不存在: " + rechargeLog.getMemberId());
        }

        // 验证充值金额
        if (rechargeLog.getAmount() == null || rechargeLog.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("充值金额必须大于0");
        }

        // 验证支付方式
        try {
            PaymentMethod.fromCode(rechargeLog.getPaymentMethod());
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的支付方式: " + rechargeLog.getPaymentMethod());
        }

        // 设置充值前余额
        rechargeLog.setBalanceBefore(member.getBalance());
        
        // 计算充值后余额
        BigDecimal balanceAfter = member.getBalance().add(rechargeLog.getAmount());
        rechargeLog.setBalanceAfter(balanceAfter);

        // 设置默认状态为待处理
        if (!StringUtils.hasText(rechargeLog.getStatus())) {
            rechargeLog.setStatus(RechargeStatus.PENDING.getCode());
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        rechargeLog.setCreatedAt(now);
        rechargeLog.setUpdatedAt(now);

        int result = rechargeLogMapper.insert(rechargeLog);
        if (result > 0) {
            log.info("充值记录创建成功: orderNumber={}, memberId={}, amount={}", 
                    orderNumber, rechargeLog.getMemberId(), rechargeLog.getAmount());
            return true;
        }
        return false;
    }

    /**
     * 确认充值成功（更新状态并增加余额）
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmRecharge(String orderNumber, String transactionId) {
        RechargeLog rechargeLog = rechargeLogMapper.findByOrderNumber(orderNumber);
        if (rechargeLog == null) {
            throw new ResourceNotFoundException("充值记录不存在: " + orderNumber);
        }

        // 检查当前状态
        RechargeStatus currentStatus = RechargeStatus.fromCode(rechargeLog.getStatus());
        if (!currentStatus.isPending()) {
            throw new BusinessException("充值记录状态不正确，当前状态: " + currentStatus.getName());
        }

        // 更新充值记录状态
        rechargeLog.setStatus(RechargeStatus.SUCCESSFUL.getCode());
        rechargeLog.setPaymentTransactionId(transactionId);
        rechargeLog.setUpdatedAt(LocalDateTime.now());

        int result = rechargeLogMapper.updateById(rechargeLog);
        if (result > 0) {
            // 更新会员余额
            boolean balanceUpdated = memberService.updateMemberBalance(
                    rechargeLog.getMemberId(), rechargeLog.getAmount());
            
            if (balanceUpdated) {
                // 发放充值积分奖励
                try {
                    pointsLogService.processRechargePointsReward(
                            rechargeLog.getMemberId(),
                            rechargeLog.getAmount(),
                            rechargeLog.getRechargeOrderNumber()
                    );
                } catch (Exception e) {
                    log.error("充值积分奖励发放失败，但不影响充值成功: orderNumber={}, error={}",
                            orderNumber, e.getMessage(), e);
                }

                log.info("充值确认成功: orderNumber={}, memberId={}, amount={}",
                        orderNumber, rechargeLog.getMemberId(), rechargeLog.getAmount());
                return true;
            } else {
                // 如果余额更新失败，回滚充值记录状态
                rechargeLog.setStatus(RechargeStatus.PENDING.getCode());
                rechargeLogMapper.updateById(rechargeLog);
                throw new BusinessException("余额更新失败");
            }
        }
        return false;
    }

    /**
     * 充值失败
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean failRecharge(String orderNumber, String reason) {
        RechargeLog rechargeLog = rechargeLogMapper.findByOrderNumber(orderNumber);
        if (rechargeLog == null) {
            throw new ResourceNotFoundException("充值记录不存在: " + orderNumber);
        }

        // 检查当前状态
        RechargeStatus currentStatus = RechargeStatus.fromCode(rechargeLog.getStatus());
        if (!currentStatus.isPending()) {
            throw new BusinessException("充值记录状态不正确，当前状态: " + currentStatus.getName());
        }

        // 更新状态为失败
        rechargeLog.setStatus(RechargeStatus.FAILED.getCode());
        rechargeLog.setUpdatedAt(LocalDateTime.now());

        int result = rechargeLogMapper.updateById(rechargeLog);
        if (result > 0) {
            log.info("充值失败: orderNumber={}, reason={}", orderNumber, reason);
            return true;
        }
        return false;
    }

    /**
     * 充值退款
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean refundRecharge(String orderNumber) {
        RechargeLog rechargeLog = rechargeLogMapper.findByOrderNumber(orderNumber);
        if (rechargeLog == null) {
            throw new ResourceNotFoundException("充值记录不存在: " + orderNumber);
        }

        // 检查当前状态
        RechargeStatus currentStatus = RechargeStatus.fromCode(rechargeLog.getStatus());
        if (!currentStatus.canRefund()) {
            throw new BusinessException("该充值记录不能退款，当前状态: " + currentStatus.getName());
        }

        // 检查会员余额是否足够
        Member member = memberService.getMemberByMemberId(rechargeLog.getMemberId());
        if (member.getBalance().compareTo(rechargeLog.getAmount()) < 0) {
            throw new BusinessException("会员余额不足，无法退款");
        }

        // 扣减会员余额
        boolean balanceUpdated = memberService.updateMemberBalance(
                rechargeLog.getMemberId(), rechargeLog.getAmount().negate());

        if (balanceUpdated) {
            // 更新充值记录状态
            rechargeLog.setStatus(RechargeStatus.REFUNDED.getCode());
            rechargeLog.setUpdatedAt(LocalDateTime.now());

            int result = rechargeLogMapper.updateById(rechargeLog);
            if (result > 0) {
                log.info("充值退款成功: orderNumber={}, amount={}", 
                        orderNumber, rechargeLog.getAmount());
                return true;
            }
        }
        return false;
    }

    /**
     * 根据订单号查询充值记录
     */
    public RechargeLog getRechargeLogByOrderNumber(String orderNumber) {
        return rechargeLogMapper.findByOrderNumber(orderNumber);
    }

    /**
     * 根据ID查询充值记录
     */
    public RechargeLog getRechargeLogById(Long id) {
        return rechargeLogMapper.selectById(id);
    }

    /**
     * 线下充值（直接成功）
     * 适用于现金充值和线下扫码支付
     */
    @Transactional(rollbackFor = Exception.class)
    public RechargeLog processOfflineRecharge(Long memberId, BigDecimal amount, String paymentMethod,
                                            Long staffId, String remark, String paymentTransactionId) {
        // 验证会员是否存在
        Member member = memberService.getMemberByMemberId(memberId);
        if (member == null) {
            throw new ResourceNotFoundException("会员不存在: " + memberId);
        }

        // 验证充值金额
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("充值金额必须大于0");
        }

        // 验证支付方式
        PaymentMethod method;
        try {
            method = PaymentMethod.fromCode(paymentMethod);
        } catch (IllegalArgumentException e) {
            throw new BusinessException("无效的支付方式: " + paymentMethod);
        }

        // 验证是否为线下支付方式
        if (!method.isOfflinePayment()) {
            throw new BusinessException("该方法只支持线下支付方式");
        }

        // 验证员工ID
        if (staffId == null) {
            throw new BusinessException("线下充值必须指定操作员工");
        }

        // 创建充值记录
        RechargeLog rechargeLog = new RechargeLog();
        rechargeLog.setRechargeOrderNumber(generateRechargeOrderNumber());
        rechargeLog.setMemberId(memberId);
        rechargeLog.setAmount(amount);
        rechargeLog.setBalanceBefore(member.getBalance());
        rechargeLog.setBalanceAfter(member.getBalance().add(amount));
        rechargeLog.setPaymentMethod(paymentMethod);
        rechargeLog.setPaymentTransactionId(paymentTransactionId);
        rechargeLog.setStatus(RechargeStatus.SUCCESSFUL.getCode()); // 线下充值直接成功
        rechargeLog.setStaffId(staffId);
        rechargeLog.setSource("offline"); // 设置来源为线下
        rechargeLog.setRemark(remark);

        LocalDateTime now = LocalDateTime.now();
        rechargeLog.setCreatedAt(now);
        rechargeLog.setUpdatedAt(now);

        // 保存充值记录
        int result = rechargeLogMapper.insert(rechargeLog);
        if (result <= 0) {
            throw new BusinessException("充值记录创建失败");
        }

        // 更新会员余额
        boolean balanceUpdated = memberService.updateMemberBalance(memberId, amount);
        if (!balanceUpdated) {
            throw new BusinessException("会员余额更新失败");
        }

        // 发放充值积分奖励
        try {
            pointsLogService.processRechargePointsReward(
                    memberId,
                    amount,
                    rechargeLog.getRechargeOrderNumber()
            );
        } catch (Exception e) {
            log.error("线下充值积分奖励发放失败，但不影响充值成功: orderNumber={}, error={}",
                    rechargeLog.getRechargeOrderNumber(), e.getMessage(), e);
        }

        log.info("线下充值成功: orderNumber={}, memberId={}, amount={}, paymentMethod={}, staffId={}",
                rechargeLog.getRechargeOrderNumber(), memberId, amount, paymentMethod, staffId);

        return rechargeLog;
    }

    /**
     * 根据会员ID查询充值记录
     */
    public List<RechargeLog> getRechargeLogsByMemberId(Long memberId) {
        return rechargeLogMapper.findByMemberId(memberId);
    }

    /**
     * 分页查询充值记录
     */
    public IPage<RechargeLog> getRechargeLogsPage(Integer current, Integer size, Long memberId, 
                                                 String status, String paymentMethod, Long staffId) {
        Page<RechargeLog> page = new Page<>(current, size);
        
        LambdaQueryWrapper<RechargeLog> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据会员ID查询
        if (memberId != null) {
            queryWrapper.eq(RechargeLog::getMemberId, memberId);
        }
        
        // 根据状态查询
        if (StringUtils.hasText(status)) {
            queryWrapper.eq(RechargeLog::getStatus, status);
        }
        
        // 根据支付方式查询
        if (StringUtils.hasText(paymentMethod)) {
            queryWrapper.eq(RechargeLog::getPaymentMethod, paymentMethod);
        }
        
        // 根据员工ID查询
        if (staffId != null) {
            queryWrapper.eq(RechargeLog::getStaffId, staffId);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(RechargeLog::getCreatedAt);
        
        return rechargeLogMapper.selectPage(page, queryWrapper);
    }

    /**
     * 获取充值统计信息
     */
    public RechargeStatistics getRechargeStatistics() {
        BigDecimal todayAmount = rechargeLogMapper.sumTodaySuccessfulAmount();
        BigDecimal monthAmount = rechargeLogMapper.sumMonthSuccessfulAmount();
        Integer pendingCount = rechargeLogMapper.countByStatus(RechargeStatus.PENDING.getCode());
        Integer successfulCount = rechargeLogMapper.countByStatus(RechargeStatus.SUCCESSFUL.getCode());
        
        RechargeStatistics statistics = new RechargeStatistics();
        statistics.setTodayAmount(todayAmount != null ? todayAmount : BigDecimal.ZERO);
        statistics.setMonthAmount(monthAmount != null ? monthAmount : BigDecimal.ZERO);
        statistics.setPendingCount(pendingCount);
        statistics.setSuccessfulCount(successfulCount);
        
        return statistics;
    }

    /**
     * 生成充值订单号
     */
    private String generateRechargeOrderNumber() {
        long id = snowflakeIdGenerator.nextId();
        return "C" + id;
    }

    /**
     * 充值统计信息
     */
    @lombok.Data
    public static class RechargeStatistics {
        private BigDecimal todayAmount;
        private BigDecimal monthAmount;
        private Integer pendingCount;
        private Integer successfulCount;
    }
}
