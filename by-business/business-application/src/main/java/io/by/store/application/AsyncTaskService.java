package io.by.store.application;

import io.by.store.application.common.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 异步任务服务
 * 演示TransmittableThreadLocal在异步场景下的用户上下文传递
 */
@Slf4j
@Service
public class AsyncTaskService {

    /**
     * 异步处理任务
     * 由于使用了TransmittableThreadLocal，用户上下文会自动传递到异步线程中
     */
    @Async
    public CompletableFuture<String> processAsyncTask(String taskName) {
        // 在异步线程中获取用户上下文
        Long staffId = UserContext.getCurrentStaffId();
        String username = UserContext.getCurrentUsername();
        Long storeId = UserContext.getCurrentStoreId();

        log.info("异步任务[{}]开始执行，当前用户: staffId={}, username={}, storeId={}", 
                taskName, staffId, username, storeId);

        try {
            // 模拟耗时操作
            Thread.sleep(1000);
            
            String result = String.format("任务[%s]执行完成，处理用户: %s", taskName, username);
            log.info("异步任务[{}]执行完成", taskName);
            
            return CompletableFuture.completedFuture(result);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("异步任务[{}]执行被中断", taskName, e);
            return CompletableFuture.completedFuture("任务执行失败");
        }
    }

    /**
     * 批量异步处理任务
     */
    @Async
    public CompletableFuture<Void> batchProcessAsync(String batchName, int taskCount) {
        Long staffId = UserContext.getCurrentStaffId();
        String username = UserContext.getCurrentUsername();
        
        log.info("批量异步任务[{}]开始执行，任务数量: {}, 当前用户: {}", 
                batchName, taskCount, username);

        for (int i = 1; i <= taskCount; i++) {
            // 在循环中，用户上下文依然可用
            log.debug("处理子任务[{}-{}]，当前用户: staffId={}", batchName, i, staffId);
            
            try {
                Thread.sleep(100); // 模拟处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        log.info("批量异步任务[{}]执行完成", batchName);
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 嵌套异步调用示例
     */
    @Async
    public CompletableFuture<String> nestedAsyncCall(String parentTask) {
        String username = UserContext.getCurrentUsername();
        log.info("父任务[{}]开始执行，用户: {}", parentTask, username);

        // 调用另一个异步方法
        CompletableFuture<String> childTask = processAsyncTask(parentTask + "-child");
        
        return childTask.thenApply(childResult -> {
            // 在回调中，用户上下文依然可用
            String currentUser = UserContext.getCurrentUsername();
            String result = String.format("父任务[%s]完成，子任务结果: %s，当前用户: %s", 
                    parentTask, childResult, currentUser);
            log.info("嵌套异步任务完成: {}", result);
            return result;
        });
    }
}
