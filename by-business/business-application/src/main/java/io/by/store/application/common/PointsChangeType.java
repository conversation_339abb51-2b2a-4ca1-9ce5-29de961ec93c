package io.by.store.application.common;

import lombok.Getter;

/**
 * 积分变动类型枚举
 */
@Getter
public enum PointsChangeType {
    
    /**
     * 下单获赠
     */
    EARN_FROM_ORDER("earn_from_order", "下单获赠", "购买商品获得积分奖励"),

    /**
     * 下单抵扣
     */
    SPEND_ON_ORDER("spend_on_order", "下单抵扣", "使用积分抵扣订单金额"),
    
    /**
     * 营销活动获赠
     */
    EARN_FROM_PROMO("earn_from_promo", "营销活动获赠", "参与营销活动获得积分"),
    
    /**
     * 退款返还
     */
    REFUND_POINTS("refund_points", "退款返还", "订单退款返还积分"),
    
    /**
     * 后台调整
     */
    ADMIN_ADJUSTMENT("admin_adjustment", "后台调整", "管理员手动调整积分"),
    
    /**
     * 签到奖励
     */
    DAILY_CHECKIN("daily_checkin", "签到奖励", "每日签到获得积分"),
    
    /**
     * 生日奖励
     */
    BIRTHDAY_BONUS("birthday_bonus", "生日奖励", "生日当天获得积分奖励"),
    
    /**
     * 推荐奖励
     */
    REFERRAL_BONUS("referral_bonus", "推荐奖励", "推荐新用户获得积分"),

    /**
     * 充值奖励
     */
    EARN_FROM_RECHARGE("earn_from_recharge", "充值奖励", "充值获得积分奖励"),

    /**
     * 积分过期扣减
     */
    POINTS_EXPIRED("points_expired", "积分过期", "积分过期自动扣减");

    /**
     * 变动类型代码
     */
    private final String code;
    
    /**
     * 变动类型名称
     */
    private final String name;
    
    /**
     * 变动类型描述
     */
    private final String description;

    PointsChangeType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     */
    public static PointsChangeType fromCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (PointsChangeType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        
        throw new IllegalArgumentException("未知的积分变动类型: " + code);
    }

    /**
     * 检查是否是增加积分的类型
     */
    public boolean isEarnType() {
        return this == EARN_FROM_ORDER || this == EARN_FROM_PROMO ||
               this == REFUND_POINTS || this == DAILY_CHECKIN ||
               this == BIRTHDAY_BONUS || this == REFERRAL_BONUS ||
               this == EARN_FROM_RECHARGE;
    }

    /**
     * 检查是否是消费积分的类型
     */
    public boolean isSpendType() {
        return this == SPEND_ON_ORDER || this == POINTS_EXPIRED;
    }

    /**
     * 检查是否是管理员操作类型
     */
    public boolean isAdminType() {
        return this == ADMIN_ADJUSTMENT;
    }

    /**
     * 检查是否与订单相关
     */
    public boolean isOrderRelated() {
        return this == EARN_FROM_ORDER || this == SPEND_ON_ORDER || this == REFUND_POINTS;
    }

    /**
     * 检查是否是系统自动操作
     */
    public boolean isSystemAuto() {
        return this == POINTS_EXPIRED || this == DAILY_CHECKIN || this == BIRTHDAY_BONUS;
    }

    /**
     * 获取变动类型的显示名称
     */
    public String getDisplayName() {
        return this.name;
    }

    /**
     * 获取积分变动的符号（+ 或 -）
     */
    public String getChangeSymbol() {
        return isEarnType() ? "+" : "-";
    }
}
