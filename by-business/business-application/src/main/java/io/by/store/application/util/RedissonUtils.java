package io.by.store.application.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Redisson工具类
 * 提供字符串、列表、分布式锁等常用操作
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedissonUtils {

    private final RedissonClient redissonClient;

    // ==================== 字符串操作 ====================

    /**
     * 设置字符串值
     */
    public void set(String key, Object value) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.set(value);
        log.debug("Redis SET: key={}, value={}", key, value);
    }

    /**
     * 设置字符串值并指定过期时间
     */
    public void set(String key, Object value, long timeout, TimeUnit timeUnit) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        bucket.set(value, timeout, timeUnit);
        log.debug("Redis SET with TTL: key={}, value={}, timeout={} {}", key, value, timeout, timeUnit);
    }

    /**
     * 获取字符串值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        T value = bucket.get();
        log.debug("Redis GET: key={}, value={}", key, value);
        return value;
    }

    /**
     * 获取字符串值，如果不存在则返回默认值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key, T defaultValue) {
        RBucket<T> bucket = redissonClient.getBucket(key);
        T value = bucket.get();
        if (value == null) {
            value = defaultValue;
        }
        log.debug("Redis GET with default: key={}, value={}, default={}", key, value, defaultValue);
        return value;
    }

    /**
     * 删除键
     */
    public boolean delete(String key) {
        boolean result = redissonClient.getBucket(key).delete();
        log.debug("Redis DELETE: key={}, result={}", key, result);
        return result;
    }

    /**
     * 批量删除键
     */
    public long delete(String... keys) {
        long result = redissonClient.getKeys().delete(keys);
        log.debug("Redis BATCH DELETE: keys={}, result={}", keys, result);
        return result;
    }

    /**
     * 判断键是否存在
     */
    public boolean exists(String key) {
        boolean result = redissonClient.getBucket(key).isExists();
        log.debug("Redis EXISTS: key={}, result={}", key, result);
        return result;
    }

    /**
     * 设置键的过期时间
     */
    public boolean expire(String key, long timeout, TimeUnit timeUnit) {
        boolean result = redissonClient.getBucket(key).expire(timeout, timeUnit);
        log.debug("Redis EXPIRE: key={}, timeout={} {}, result={}", key, timeout, timeUnit, result);
        return result;
    }

    /**
     * 设置键的过期时间（Duration）
     */
    public boolean expire(String key, Duration duration) {
        boolean result = redissonClient.getBucket(key).expire(duration);
        log.debug("Redis EXPIRE with Duration: key={}, duration={}, result={}", key, duration, result);
        return result;
    }

    /**
     * 获取键的剩余过期时间（秒）
     */
    public long getExpire(String key) {
        long ttl = redissonClient.getBucket(key).remainTimeToLive();
        log.debug("Redis TTL: key={}, ttl={}ms", key, ttl);
        return ttl > 0 ? ttl / 1000 : ttl; // 转换为秒
    }

    /**
     * 移除键的过期时间
     */
    public boolean persist(String key) {
        boolean result = redissonClient.getBucket(key).clearExpire();
        log.debug("Redis PERSIST: key={}, result={}", key, result);
        return result;
    }

    // ==================== 列表操作 ====================

//    /**
//     * 向列表左侧添加元素
//     */
//    public int leftPush(String key, Object... values) {
//        RList<Object> list = redissonClient.getList(key);
//        int result = list.addAll(0, Lists.of(values));
//        log.debug("Redis LPUSH: key={}, values={}, result={}", key, values, result);
//        return result;
//    }
//
//    /**
//     * 向列表右侧添加元素
//     */
//    public boolean rightPush(String key, Object... values) {
//        RList<Object> list = redissonClient.getList(key);
//        boolean result = list.addAll(List.of(values));
//        log.debug("Redis RPUSH: key={}, values={}, result={}", key, values, result);
//        return result;
//    }

    /**
     * 从列表左侧弹出元素
     */
    @SuppressWarnings("unchecked")
    public <T> T leftPop(String key) {
        RList<T> list = redissonClient.getList(key);
        T value = list.isEmpty() ? null : list.remove(0);
        log.debug("Redis LPOP: key={}, value={}", key, value);
        return value;
    }

    /**
     * 从列表右侧弹出元素
     */
    @SuppressWarnings("unchecked")
    public <T> T rightPop(String key) {
        RList<T> list = redissonClient.getList(key);
        T value = list.isEmpty() ? null : list.remove(list.size() - 1);
        log.debug("Redis RPOP: key={}, value={}", key, value);
        return value;
    }

    /**
     * 获取列表长度
     */
    public int listSize(String key) {
        RList<Object> list = redissonClient.getList(key);
        int size = list.size();
        log.debug("Redis LLEN: key={}, size={}", key, size);
        return size;
    }

    /**
     * 获取列表指定范围的元素
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> listRange(String key, int start, int end) {
        RList<T> list = redissonClient.getList(key);
        List<T> result = list.range(start, end);
        log.debug("Redis LRANGE: key={}, start={}, end={}, size={}", key, start, end, result.size());
        return result;
    }

    /**
     * 获取列表所有元素
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> listGetAll(String key) {
        RList<T> list = redissonClient.getList(key);
        List<T> result = list.readAll();
        log.debug("Redis LRANGE ALL: key={}, size={}", key, result.size());
        return result;
    }

    /**
     * 获取列表指定索引的元素
     */
    @SuppressWarnings("unchecked")
    public <T> T listGet(String key, int index) {
        RList<T> list = redissonClient.getList(key);
        T value = list.get(index);
        log.debug("Redis LINDEX: key={}, index={}, value={}", key, index, value);
        return value;
    }

    /**
     * 设置列表指定索引的元素
     */
    public void listSet(String key, int index, Object value) {
        RList<Object> list = redissonClient.getList(key);
        list.set(index, value);
        log.debug("Redis LSET: key={}, index={}, value={}", key, index, value);
    }

    /**
     * 从列表中移除指定值的元素
     */
    public boolean listRemove(String key, Object value) {
        RList<Object> list = redissonClient.getList(key);
        boolean result = list.remove(value);
        log.debug("Redis LREM: key={}, value={}, result={}", key, value, result);
        return result;
    }

    /**
     * 清空列表
     */
    public void listClear(String key) {
        RList<Object> list = redissonClient.getList(key);
        list.clear();
        log.debug("Redis LIST CLEAR: key={}", key);
    }

    /**
     * 修剪列表，只保留指定范围的元素
     */
    public void listTrim(String key, int start, int end) {
        RList<Object> list = redissonClient.getList(key);
        list.trim(start, end);
        log.debug("Redis LTRIM: key={}, start={}, end={}", key, start, end);
    }

    // ==================== 分布式锁操作 ====================

    /**
     * 获取分布式锁
     */
    public RLock getLock(String lockKey) {
        return redissonClient.getLock(lockKey);
    }

    /**
     * 尝试获取锁（立即返回）
     */
    public boolean tryLock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean result = lock.tryLock();
        log.debug("Redis TRY LOCK: key={}, result={}", lockKey, result);
        return result;
    }

    /**
     * 尝试获取锁并指定等待时间和锁持有时间
     */
    public boolean tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit timeUnit) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean result = lock.tryLock(waitTime, leaseTime, timeUnit);
            log.debug("Redis TRY LOCK with timeout: key={}, waitTime={}, leaseTime={}, unit={}, result={}",
                    lockKey, waitTime, leaseTime, timeUnit, result);
            return result;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Redis TRY LOCK interrupted: key={}", lockKey, e);
            return false;
        }
    }

    /**
     * 获取锁（阻塞直到获取成功）
     */
    public void lock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        log.debug("Redis LOCK: key={}", lockKey);
    }

    /**
     * 获取锁并指定锁持有时间
     */
    public void lock(String lockKey, long leaseTime, TimeUnit timeUnit) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock(leaseTime, timeUnit);
        log.debug("Redis LOCK with lease time: key={}, leaseTime={}, unit={}", lockKey, leaseTime, timeUnit);
    }

    /**
     * 释放锁
     */
    public void unlock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        if (lock.isHeldByCurrentThread()) {
            lock.unlock();
            log.debug("Redis UNLOCK: key={}", lockKey);
        } else {
            log.warn("Redis UNLOCK failed: key={}, not held by current thread", lockKey);
        }
    }

    /**
     * 强制释放锁
     */
    public boolean forceUnlock(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean result = lock.forceUnlock();
        log.debug("Redis FORCE UNLOCK: key={}, result={}", lockKey, result);
        return result;
    }

    /**
     * 检查锁是否被持有
     */
    public boolean isLocked(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean result = lock.isLocked();
        log.debug("Redis IS LOCKED: key={}, result={}", lockKey, result);
        return result;
    }

    /**
     * 检查锁是否被当前线程持有
     */
    public boolean isHeldByCurrentThread(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        boolean result = lock.isHeldByCurrentThread();
        log.debug("Redis IS HELD BY CURRENT THREAD: key={}, result={}", lockKey, result);
        return result;
    }

    /**
     * 获取锁的持有次数（重入次数）
     */
    public int getHoldCount(String lockKey) {
        RLock lock = redissonClient.getLock(lockKey);
        int count = lock.getHoldCount();
        log.debug("Redis LOCK HOLD COUNT: key={}, count={}", lockKey, count);
        return count;
    }

    // ==================== 高级功能 ====================

    /**
     * 执行带锁的操作（自动加锁和解锁）
     */
    public <T> T executeWithLock(String lockKey, java.util.function.Supplier<T> supplier) {
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();
        try {
            log.debug("Redis EXECUTE WITH LOCK: key={}, operation started", lockKey);
            return supplier.get();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("Redis EXECUTE WITH LOCK: key={}, operation completed and unlocked", lockKey);
            }
        }
    }

    /**
     * 执行带锁的操作（自动加锁和解锁，指定超时时间）
     */
    public <T> T executeWithLock(String lockKey, long waitTime, long leaseTime, TimeUnit timeUnit,
                                java.util.function.Supplier<T> supplier) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(waitTime, leaseTime, timeUnit)) {
                try {
                    log.debug("Redis EXECUTE WITH LOCK (timeout): key={}, operation started", lockKey);
                    return supplier.get();
                } finally {
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.debug("Redis EXECUTE WITH LOCK (timeout): key={}, operation completed and unlocked", lockKey);
                    }
                }
            } else {
                log.warn("Redis EXECUTE WITH LOCK (timeout): key={}, failed to acquire lock", lockKey);
                throw new RuntimeException("Failed to acquire lock: " + lockKey);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Redis EXECUTE WITH LOCK (timeout): key={}, interrupted", lockKey, e);
            throw new RuntimeException("Lock acquisition interrupted: " + lockKey, e);
        }
    }

    /**
     * 执行带锁的操作（无返回值）
     */
    public void executeWithLock(String lockKey, Runnable runnable) {
        executeWithLock(lockKey, () -> {
            runnable.run();
            return null;
        });
    }

    /**
     * 批量获取键值
     */
    @SuppressWarnings("unchecked")
    public <T> java.util.Map<String, T> multiGet(String... keys) {
        RBuckets buckets = redissonClient.getBuckets();
        java.util.Map<String, T> result = buckets.get(keys);
        log.debug("Redis MGET: keys={}, result size={}", keys, result.size());
        return result;
    }

    /**
     * 批量设置键值
     */
    public void multiSet(java.util.Map<String, Object> keyValues) {
        RBuckets buckets = redissonClient.getBuckets();
        buckets.set(keyValues);
        log.debug("Redis MSET: size={}", keyValues.size());
    }

    /**
     * 原子递增
     */
    public long increment(String key) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        long result = atomicLong.incrementAndGet();
        log.debug("Redis INCR: key={}, result={}", key, result);
        return result;
    }

    /**
     * 原子递增指定值
     */
    public long incrementBy(String key, long delta) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        long result = atomicLong.addAndGet(delta);
        log.debug("Redis INCRBY: key={}, delta={}, result={}", key, delta, result);
        return result;
    }

    /**
     * 原子递减
     */
    public long decrement(String key) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        long result = atomicLong.decrementAndGet();
        log.debug("Redis DECR: key={}, result={}", key, result);
        return result;
    }

    /**
     * 原子递减指定值
     */
    public long decrementBy(String key, long delta) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        long result = atomicLong.addAndGet(-delta);
        log.debug("Redis DECRBY: key={}, delta={}, result={}", key, delta, result);
        return result;
    }

    /**
     * 获取所有匹配的键
     */
    public Iterable<String> getKeys(String pattern) {
        Iterable<String> keys = redissonClient.getKeys().getKeysByPattern(pattern);
        log.debug("Redis KEYS: pattern={}", pattern);
        return keys;
    }

//    /**
//     * 获取Redis信息
//     */
//    public String getInfo() {
//        return redissonClient.getNodesGroup().getNode().info();
//    }

//    /**
//     * 检查Redis连接状态
//     */
//    public boolean ping() {
//        try {
//            return redissonClient.getNodesGroup().pingAll();
//        } catch (Exception e) {
//            log.error("Redis PING failed", e);
//            return false;
//        }
//    }
}
