package io.by.store.application;

import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.by.store.application.common.BusinessException;
import io.by.store.application.common.ResourceNotFoundException;
import io.by.store.infrastructure.entity.Staff;
import io.by.store.infrastructure.mapper.StaffMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StaffService {

    private final StaffMapper staffMapper;


    /**
     * 创建员工
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createStaff(Staff staff, String password) {
        // 检查用户名是否重复
        Staff existingByUsername = staffMapper.findByUsername(staff.getUsername());
        if (existingByUsername != null) {
            throw new BusinessException("用户名已存在: " + staff.getUsername());
        }

        // 检查手机号是否重复
        if (StringUtils.hasText(staff.getPhoneNumber())) {
            Staff existingByPhone = staffMapper.findByPhoneNumber(staff.getPhoneNumber());
            if (existingByPhone != null) {
                throw new BusinessException("手机号已存在: " + staff.getPhoneNumber());
            }
        }

        // 加密密码
        staff.setPasswordHash(BCrypt.hashpw(password, BCrypt.gensalt()));

        // 设置默认值
        if (staff.getIsActive() == null) {
            staff.setIsActive(true);
        }

        // 设置创建时间和更新时间
        LocalDateTime now = LocalDateTime.now();
        staff.setCreatedAt(now);
        staff.setUpdatedAt(now);

        int result = staffMapper.insert(staff);
        if (result > 0) {
            log.info("员工创建成功: {}", staff.getUsername());
            return true;
        }
        return false;
    }

    /**
     * 更新员工信息
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStaff(Staff staff) {
        // 检查员工是否存在
        Staff existing = staffMapper.selectById(staff.getId());
        if (existing == null) {
            throw new ResourceNotFoundException("员工不存在: " + staff.getId());
        }

        // 如果修改了用户名，检查新用户名是否重复
        if (!existing.getUsername().equals(staff.getUsername())) {
            Staff usernameExists = staffMapper.findByUsername(staff.getUsername());
            if (usernameExists != null && !usernameExists.getId().equals(staff.getId())) {
                throw new BusinessException("用户名已存在: " + staff.getUsername());
            }
        }

        // 如果修改了手机号，检查新手机号是否重复
        if (StringUtils.hasText(staff.getPhoneNumber()) &&
            !staff.getPhoneNumber().equals(existing.getPhoneNumber())) {
            Staff phoneExists = staffMapper.findByPhoneNumber(staff.getPhoneNumber());
            if (phoneExists != null && !phoneExists.getId().equals(staff.getId())) {
                throw new BusinessException("手机号已存在: " + staff.getPhoneNumber());
            }
        }

        // 保留原密码哈希值（密码单独更新）
        staff.setPasswordHash(existing.getPasswordHash());
        
        // 设置更新时间
        staff.setUpdatedAt(LocalDateTime.now());

        int result = staffMapper.updateById(staff);
        if (result > 0) {
            log.info("员工信息更新成功: {}", staff.getUsername());
            return true;
        }
        return false;
    }

    /**
     * 更新员工密码
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStaffPassword(Long staffId, String newPassword) {
        Staff staff = staffMapper.selectById(staffId);
        if (staff == null) {
            throw new ResourceNotFoundException("员工不存在: " + staffId);
        }

        // 加密新密码
        staff.setPasswordHash(BCrypt.hashpw(newPassword, BCrypt.gensalt()));
        staff.setUpdatedAt(LocalDateTime.now());

        int result = staffMapper.updateById(staff);
        if (result > 0) {
            log.info("员工密码更新成功: {}", staff.getUsername());
            return true;
        }
        return false;
    }

    /**
     * 删除员工
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStaff(Long id) {
        Staff staff = staffMapper.selectById(id);
        if (staff == null) {
            throw new ResourceNotFoundException("员工不存在: " + id);
        }

        int result = staffMapper.deleteById(id);
        if (result > 0) {
            log.info("员工删除成功: {}", staff.getUsername());
            return true;
        }
        return false;
    }

    /**
     * 根据ID查询员工
     */
    public Staff getStaffById(Long id) {
        return staffMapper.selectById(id);
    }

    /**
     * 根据用户名查询员工
     */
    public Staff getStaffByUsername(String username) {
        return staffMapper.findByUsername(username);
    }

    /**
     * 根据门店ID查询激活的员工列表
     */
    public List<Staff> getActiveStaffByStoreId(Long storeId) {
        return staffMapper.findActiveStaffByStoreId(storeId);
    }

    /**
     * 分页查询员工
     */
    public IPage<Staff> getStaffPage(Integer current, Integer size, Long storeId, 
                                    String username, String fullName, Long roleId, Boolean isActive) {
        Page<Staff> page = new Page<>(current, size);
        
        LambdaQueryWrapper<Staff> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据门店ID查询
        if (storeId != null) {
            queryWrapper.eq(Staff::getStoreId, storeId);
        }
        
        // 根据用户名模糊查询
        if (StringUtils.hasText(username)) {
            queryWrapper.like(Staff::getUsername, username);
        }
        
        // 根据真实姓名模糊查询
        if (StringUtils.hasText(fullName)) {
            queryWrapper.like(Staff::getFullName, fullName);
        }
        
        // 根据角色ID查询
        if (roleId != null) {
            queryWrapper.eq(Staff::getRoleId, roleId);
        }
        
        // 根据状态查询
        if (isActive != null) {
            queryWrapper.eq(Staff::getIsActive, isActive);
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc(Staff::getCreatedAt);
        
        return staffMapper.selectPage(page, queryWrapper);
    }

    /**
     * 验证员工密码
     */
    public boolean validatePassword(String username, String password) {
        Staff staff = staffMapper.findByUsername(username);
        if (staff == null || !staff.getIsActive()) {
            return false;
        }
        
        return BCrypt.checkpw(password, staff.getPasswordHash());
    }

}
