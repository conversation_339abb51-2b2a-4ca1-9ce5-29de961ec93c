package io.by.store.application.common;

import lombok.Getter;

/**
 * 会员等级标签枚举
 * 用于程序内部识别会员等级
 */
@Getter
public enum LevelTag {
    
    /**
     * 普通会员
     */
    NORMAL("normal", "普通会员", 0L, "基础会员权益"),
    
    /**
     * 白银会员
     */
    SILVER("silver", "白银会员", 1000L, "享受9.5折优惠，生日专属礼品"),
    
    /**
     * 黄金会员
     */
    GOLD("gold", "黄金会员", 5000L, "享受9折优惠，免费配送，专属客服"),
    
    /**
     * 铂金会员
     */
    PLATINUM("platinum", "铂金会员", 15000L, "享受8.5折优惠，优先配送，专属活动邀请"),
    
    /**
     * 钻石会员
     */
    DIAMOND("diamond", "钻石会员", 50000L, "享受8折优惠，VIP专线，定制化服务");

    /**
     * 等级标签/编码
     */
    private final String tag;
    
    /**
     * 等级名称
     */
    private final String levelName;
    
    /**
     * 升级积分阈值
     */
    private final Long upgradePointsThreshold;
    
    /**
     * 等级描述
     */
    private final String description;

    LevelTag(String tag, String levelName, Long upgradePointsThreshold, String description) {
        this.tag = tag;
        this.levelName = levelName;
        this.upgradePointsThreshold = upgradePointsThreshold;
        this.description = description;
    }

    /**
     * 根据标签获取枚举
     */
    public static LevelTag fromTag(String tag) {
        if (tag == null) {
            return null;
        }
        
        for (LevelTag levelTag : values()) {
            if (levelTag.getTag().equals(tag)) {
                return levelTag;
            }
        }
        
        throw new IllegalArgumentException("未知的等级标签: " + tag);
    }

    /**
     * 根据积分获取对应的等级
     */
    public static LevelTag getLevelByPoints(Long points) {
        if (points == null || points < 0) {
            return NORMAL;
        }
        
        LevelTag currentLevel = NORMAL;
        for (LevelTag level : values()) {
            if (points >= level.getUpgradePointsThreshold()) {
                currentLevel = level;
            } else {
                break;
            }
        }
        
        return currentLevel;
    }

    /**
     * 获取下一个等级
     */
    public LevelTag getNextLevel() {
        LevelTag[] levels = values();
        for (int i = 0; i < levels.length - 1; i++) {
            if (levels[i] == this) {
                return levels[i + 1];
            }
        }
        return null; // 已经是最高等级
    }

    /**
     * 获取升级到下一等级还需要的积分
     */
    public Long getPointsToNextLevel(Long currentPoints) {
        LevelTag nextLevel = getNextLevel();
        if (nextLevel == null) {
            return 0L; // 已经是最高等级
        }
        
        if (currentPoints == null || currentPoints < 0) {
            currentPoints = 0L;
        }
        
        return Math.max(0L, nextLevel.getUpgradePointsThreshold() - currentPoints);
    }

    /**
     * 检查是否是最高等级
     */
    public boolean isMaxLevel() {
        return this == DIAMOND;
    }

    /**
     * 获取等级排序值（用于比较等级高低）
     */
    public int getOrder() {
        return ordinal();
    }

    /**
     * 比较等级高低
     */
    public boolean isHigherThan(LevelTag other) {
        return this.getOrder() > other.getOrder();
    }

    /**
     * 比较等级高低
     */
    public boolean isLowerThan(LevelTag other) {
        return this.getOrder() < other.getOrder();
    }
}
