# 测试备忘录模块API

## 测试步骤

### 1. 启动应用程序
```bash
cd by-boot
mvn spring-boot:run
```

### 2. 登录获取Token
```bash
curl -X POST "http://localhost:8081/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "admin",
       "password": "123456"
     }'
```

保存返回的accessToken用于后续请求。

### 3. 创建备忘录
```bash
curl -X POST "http://localhost:8081/api/v1/memos/create" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -d '{
       "content": "检查库存并补充原材料",
       "status": "TODO"
     }'
```

### 4. 分页查询备忘录
```bash
curl -X POST "http://localhost:8081/api/v1/memos/query" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -d '{
       "current": 1,
       "size": 10
     }'
```

### 5. 更新备忘录状态
```bash
curl -X POST "http://localhost:8081/api/v1/memos/update-status" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     -d '{
       "ids": [1],
       "status": "DONE"
     }'
```

### 6. 根据ID查询备忘录
```bash
curl -X GET "http://localhost:8081/api/v1/memos/1" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 7. 获取统计信息
```bash
curl -X GET "http://localhost:8081/api/v1/memos/statistics" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 8. 根据状态查询
```bash
curl -X GET "http://localhost:8081/api/v1/memos/by-status/TODO" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 9. 删除备忘录
```bash
curl -X DELETE "http://localhost:8081/api/v1/memos/1" \
     -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 预期响应

### 创建备忘录响应
```json
{
  "code": 200,
  "msg": "备忘录创建成功",
  "data": {
    "id": 1,
    "storeId": 1,
    "content": "检查库存并补充原材料",
    "status": "TODO",
    "statusDescription": "待办",
    "creatorId": 1,
    "createdAt": "2024-01-01T10:00:00",
    "updatedAt": "2024-01-01T10:00:00",
    "completedAt": null
  },
  "timestamp": 1640234567890
}
```

### 分页查询响应
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "current": 1,
    "size": 10,
    "total": 1,
    "pages": 1,
    "records": [
      {
        "id": 1,
        "storeId": 1,
        "content": "检查库存并补充原材料",
        "status": "TODO",
        "statusDescription": "待办",
        "creatorId": 1,
        "createdAt": "2024-01-01T10:00:00",
        "updatedAt": "2024-01-01T10:00:00",
        "completedAt": null
      }
    ]
  },
  "timestamp": 1640234567890
}
```

### 状态更新响应
```json
{
  "code": 200,
  "msg": "状态更新成功",
  "data": null,
  "timestamp": 1640234567890
}
```

## 数据库准备

确保数据库中存在store_memos表：

```sql
-- 创建备忘录表
CREATE TABLE store_memos (
    "id" BIGSERIAL PRIMARY KEY,
    "store_id" BIGINT NOT NULL,
    "content" TEXT NOT NULL,
    "status" VARCHAR(20) NOT NULL DEFAULT 'TODO',
    "creator_id" BIGINT NOT NULL,
    "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "updated_at" TIMESTAMP NOT NULL DEFAULT NOW(),
    "completed_at" TIMESTAMP
);

-- 添加索引
CREATE INDEX idx_memos_store_id_status ON store_memos(store_id, status);
CREATE INDEX idx_memos_creator_id ON store_memos(creator_id);

-- 插入测试数据（可选）
INSERT INTO store_memos (store_id, content, status, creator_id) 
VALUES 
(1, '检查设备运行状态', 'TODO', 1),
(1, '整理收银台', 'DONE', 1),
(1, '补充纸巾和餐具', 'TODO', 1);
```

## 注意事项

1. 所有接口都需要JWT认证，请确保在请求头中包含有效的Authorization token
2. 备忘录会自动关联到当前用户的门店
3. 只能操作自己门店的备忘录
4. 状态更新为DONE时会自动记录完成时间
5. 内容长度限制为1000字符

## 错误排查

如果遇到问题，请检查：

1. 数据库连接是否正常
2. store_memos表是否存在
3. JWT token是否有效
4. 用户是否有相应权限
5. 请求参数格式是否正确
