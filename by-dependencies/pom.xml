<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>io.by.store</groupId>
        <artifactId>by-store-parent</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    
    <artifactId>by-dependencies</artifactId>
    <packaging>pom</packaging>
    <name>by-dependencies</name>
    <description>Dependencies management for by-store project</description>
    
    <properties>
        <!-- Spring Boot -->
        <spring.boot.version>2.7.18</spring.boot.version>

        <!-- Database -->
        <mybatis-plus.version>*******</mybatis-plus.version>
        <mybatis-plus-join.version>1.4.5</mybatis-plus-join.version>
        <postgresql.version>42.6.0</postgresql.version>
        <jsqlparser.version>4.6</jsqlparser.version>

        <!-- Redis -->
        <redisson.version>3.21.3</redisson.version>

        <!-- Third Party Libraries -->
        <lombok.version>1.18.38</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.22</hutool.version>
        <jwt.version>0.11.5</jwt.version>
        <junit.version>5.8.2</junit.version>

        <!-- Maven Plugins -->
        <maven.compiler.plugin.version>3.8.1</maven.compiler.plugin.version>
        <maven.surefire.plugin.version>2.22.2</maven.surefire.plugin.version>
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <!-- Database Dependencies -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>

            <!-- Redis Dependencies -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- Third Party Libraries -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- MapStruct Dependencies -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- Hutool Dependencies -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    
</project>
