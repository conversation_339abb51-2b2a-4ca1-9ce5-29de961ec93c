# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据库配置（测试环境可以使用H2内存数据库）
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # Redis配置（测试环境可以禁用）
  redis:
    enabled: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 七牛云配置（测试环境）
# 注意：运行测试前需要配置真实的七牛云参数
qiniu:
  access-key: test-access-key        # 替换为真实的AccessKey
  secret-key: test-secret-key        # 替换为真实的SecretKey
  bucket: test-bucket                # 替换为真实的存储空间名称
  domain: test-domain.com            # 替换为真实的访问域名
  region: z0                         # 存储区域
  enabled: false                     # 测试环境默认禁用，需要测试时改为true

# JWT配置
jwt:
  secret: test-jwt-secret-key-for-testing-environment-must-be-at-least-64-characters-long
  expiration: 24
  refresh-expiration: 7

# 雪花算法配置
snowflake:
  worker-id: 1
  datacenter-id: 1

# 日志配置
logging:
  level:
    io.by.store: DEBUG
    com.qiniu: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
