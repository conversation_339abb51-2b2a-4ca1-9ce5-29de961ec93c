spring:
  application:
    name: by-store
  
  # 数据源配置
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *****************************************
    username: postgres
    password: root
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  # Redis 配置
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        min-idle: 8
        max-active: 32
# MyBatis Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: io.by.store.infrastructure.entity



# 服务器配置
server:
  port: 8081
  servlet:
    context-path: /

# 日志配置
logging:
  level:
    io.by.store: DEBUG
    com.baomidou.mybatisplus: DEBUG
    org.redisson: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT配置
jwt:
  secret: by-store-jwt-secret-key-2024-very-long-secret-key-for-security-must-be-at-least-64-characters-long
  expiration: 24  # 访问令牌过期时间（小时）
  refresh-expiration: 7  # 刷新令牌过期时间（天）

# 雪花算法配置
snowflake:
  worker-id: 1      # 机器ID (0-31)
  datacenter-id: 1  # 数据中心ID (0-31)

# 七牛云配置
qiniu:
  access-key: iF5W4S8jvblyXjxpaVdiHu389uFFl62sqZQ6YzBp    # 七牛云AccessKey
  secret-key: SrZ7srZj9FTkVFqGwUssjmzLjHVavJZEp1E6ZH0q    # 七牛云SecretKey
  bucket: wcby      # 存储空间名称
  domain: http://t1l8c4oaa.hn-bkt.clouddn.com  # 访问域名
  region: z2                          # 存储区域 (z0:华东, z1:华北, z2:华南, na0:北美, as0:东南亚)
  enabled: true                       # 是否启用七牛云存储

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
wx:
  miniapp:
    configs:
      - appid: wxef74d14b11ae883d #微信小程序的appid
        secret: ef04922a3e024808066b67752ad24b25 #微信小程序的Secret
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON